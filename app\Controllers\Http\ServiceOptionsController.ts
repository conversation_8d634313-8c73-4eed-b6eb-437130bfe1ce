import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ServiceOption from 'App/Models/ServiceOption'
import { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'

export default class ServiceOptionsController {
  /**
   * Display a list of service options with filtering and pagination
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const vendorId = request.input('vendorId')
      const type = request.input('type')
      const active = request.input('active')
      const search = request.input('search')

      const query = ServiceOption.query()

      // Apply filters
      if (vendorId) {
        if (vendorId === 'global') {
          query.whereNull('vendorId')
        } else {
          query.where((builder) => {
            builder.whereNull('vendorId').orWhere('vendorId', vendorId)
          })
        }
      }

      if (type) {
        query.where('type', type)
      }

      if (active !== undefined) {
        query.where('active', active === 'true')
      }

      if (search) {
        query.where((builder) => {
          builder
            .where('name', 'ILIKE', `%${search}%`)
            .orWhere('description', 'ILIKE', `%${search}%`)
        })
      }

      // Include related data
      query
        .preload('duration')
        .orderBy(['type', 'name'])

      const options = await query.paginate(page, limit)

      return response.json({
        success: true,
        data: options.toJSON(),
        meta: {
          total: options.total,
          perPage: options.perPage,
          currentPage: options.currentPage,
          lastPage: options.lastPage,
          hasMorePages: options.hasMorePages
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch service options',
        error: error.message
      })
    }
  }

  /**
   * Create a new service option
   */
  public async store({ request, response }: HttpContextContract) {
    try {
      const payload = request.only([
        'vendorId', 'name', 'type', 'description', 'defaultPriceAdjustment',
        'durationId', 'constraints', 'active'
      ])

      const option = await ServiceOption.create(payload)

      // Validate the option configuration
      const validation = await option.validateConfiguration()
      if (!validation.valid) {
        await option.delete()
        return response.status(400).json({
          success: false,
          message: 'Invalid option configuration',
          errors: validation.errors
        })
      }

      // Load related data for response
      await option.load('duration')

      return response.status(201).json({
        success: true,
        message: 'Service option created successfully',
        data: option
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create service option',
        error: error.message
      })
    }
  }

  /**
   * Display a specific service option
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const option = await ServiceOption.query()
        .where('id', params.id)
        .preload('duration')
        .firstOrFail()

      const validation = await option.validateConfiguration()
      const calendarBlockMinutes = await option.getCalendarBlockMinutes()

      return response.json({
        success: true,
        data: {
          ...option.toJSON(),
          validation,
          calendarBlockMinutes
        }
      })
    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Service option not found',
        error: error.message
      })
    }
  }

  /**
   * Update a service option
   */
  public async update({ params, request, response }: HttpContextContract) {
    try {
      const option = await ServiceOption.findOrFail(params.id)
      const payload = request.only([
        'vendorId', 'name', 'type', 'description', 'defaultPriceAdjustment',
        'durationId', 'constraints', 'active'
      ])

      option.merge(payload)

      // Validate the option configuration before saving
      const validation = await option.validateConfiguration()
      if (!validation.valid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid option configuration',
          errors: validation.errors
        })
      }

      await option.save()
      await option.load('duration')

      return response.json({
        success: true,
        message: 'Service option updated successfully',
        data: option
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to update service option',
        error: error.message
      })
    }
  }

  /**
   * Delete a service option
   */
  public async destroy({ params, response }: HttpContextContract) {
    try {
      const option = await ServiceOption.findOrFail(params.id)
      
      // TODO: Check if option is being used by any products
      // For now, we'll just soft delete by setting active to false
      option.active = false
      await option.save()

      return response.json({
        success: true,
        message: 'Service option deactivated successfully'
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to delete service option',
        error: error.message
      })
    }
  }

  /**
   * Create service option from template
   */
  public async createFromTemplate({ request, response }: HttpContextContract) {
    try {
      const { name, type, vendorId, options } = request.only(['name', 'type', 'vendorId', 'options'])

      if (!name || !type) {
        return response.status(400).json({
          success: false,
          message: 'Name and type are required'
        })
      }

      const template = ServiceOption.createTemplate(name, type, vendorId, options || {})
      const option = await ServiceOption.create(template)

      await option.load('duration')

      return response.status(201).json({
        success: true,
        message: 'Service option created from template successfully',
        data: option
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create option from template',
        error: error.message
      })
    }
  }

  /**
   * Get options by type
   */
  public async getByType({ params, request, response }: HttpContextContract) {
    try {
      const { type } = params
      const vendorId = request.input('vendorId')

      const options = await ServiceOption.getByType(type, vendorId)

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch options by type',
        error: error.message
      })
    }
  }

  /**
   * Get vendor-specific options
   */
  public async getVendorOptions({ params, response }: HttpContextContract) {
    try {
      const { vendorId } = params

      const options = await ServiceOption.getVendorOptions(vendorId)

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch vendor options',
        error: error.message
      })
    }
  }

  /**
   * Get global options
   */
  public async getGlobalOptions({ response }: HttpContextContract) {
    try {
      const options = await ServiceOption.getGlobalOptions()

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch global options',
        error: error.message
      })
    }
  }

  /**
   * Search service options
   */
  public async search({ request, response }: HttpContextContract) {
    try {
      const { query, vendorId } = request.only(['query', 'vendorId'])

      if (!query) {
        return response.status(400).json({
          success: false,
          message: 'Search query is required'
        })
      }

      const options = await ServiceOption.search(query, vendorId)

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to search options',
        error: error.message
      })
    }
  }

  /**
   * Get available option types
   */
  public async getTypes({ response }: HttpContextContract) {
    try {
      const types = Object.values(ServiceOptionType).map(type => ({
        value: type,
        label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      }))

      return response.json({
        success: true,
        data: types
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch option types',
        error: error.message
      })
    }
  }
}
