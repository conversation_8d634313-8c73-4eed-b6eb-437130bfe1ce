import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export interface ItemValidationResult {
  isValid: boolean
  errors: string[]
  itemCount: number
}

export interface OrderItemData {
  [productId: string]: number | { quantity: number; [key: string]: any }
}

export class OrderValidationHelper {
  /**
   * Validate items for temp-order creation (object format)
   * Expected format: { "productId": quantity } or { "productId": { quantity: number } }
   */
  public static validateTempOrderItems(items: OrderItemData): ItemValidationResult {
    const errors: string[] = []
    
    if (!items || typeof items !== 'object') {
      errors.push('Items must be provided as an object')
      return { isValid: false, errors, itemCount: 0 }
    }

    const itemKeys = Object.keys(items)
    
    if (itemKeys.length === 0) {
      errors.push('Order must contain at least one item')
      return { isValid: false, errors, itemCount: 0 }
    }

    let validItemCount = 0
    
    for (const productId of itemKeys) {
      if (!productId || productId.trim() === '') {
        errors.push('Product ID cannot be empty')
        continue
      }

      const itemData = items[productId]
      let quantity: number

      // Handle both formats: quantity number or object with quantity
      if (typeof itemData === 'number') {
        quantity = itemData
      } else if (typeof itemData === 'object' && itemData.quantity) {
        quantity = itemData.quantity
      } else {
        errors.push(`Invalid item data for product ${productId}`)
        continue
      }

      if (!quantity || quantity <= 0) {
        errors.push(`Invalid quantity for product ${productId}: ${quantity}`)
        continue
      }

      validItemCount++
    }

    if (validItemCount === 0) {
      errors.push('No valid items found in order')
    }

    return {
      isValid: errors.length === 0 && validItemCount > 0,
      errors,
      itemCount: validItemCount
    }
  }

  /**
   * Validate items for direct order creation (array format)
   * Expected format: [{ "productId": quantity }] or [{ productId: "id", quantity: number }]
   */
  public static validateDirectOrderItems(items: any[]): ItemValidationResult {
    const errors: string[] = []
    
    if (!Array.isArray(items)) {
      errors.push('Items must be provided as an array')
      return { isValid: false, errors, itemCount: 0 }
    }

    if (items.length === 0) {
      errors.push('Order must contain at least one item')
      return { isValid: false, errors, itemCount: 0 }
    }

    let validItemCount = 0

    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      
      if (!item || typeof item !== 'object') {
        errors.push(`Item ${i + 1} must be an object`)
        continue
      }

      // Handle different item formats
      if (item.productId && item.quantity) {
        // Format: { productId: "id", quantity: number }
        if (!item.productId.trim()) {
          errors.push(`Item ${i + 1} has empty product ID`)
          continue
        }
        
        if (!item.quantity || item.quantity <= 0) {
          errors.push(`Item ${i + 1} has invalid quantity: ${item.quantity}`)
          continue
        }
        
        validItemCount++
      } else {
        // Format: { "productId": quantity }
        const productIds = Object.keys(item)
        
        if (productIds.length === 0) {
          errors.push(`Item ${i + 1} has no product data`)
          continue
        }

        for (const productId of productIds) {
          if (!productId.trim()) {
            errors.push(`Item ${i + 1} has empty product ID`)
            continue
          }

          const quantity = item[productId]
          if (!quantity || quantity <= 0) {
            errors.push(`Item ${i + 1} has invalid quantity for product ${productId}: ${quantity}`)
            continue
          }

          validItemCount++
        }
      }
    }

    if (validItemCount === 0) {
      errors.push('No valid items found in order')
    }

    return {
      isValid: errors.length === 0 && validItemCount > 0,
      errors,
      itemCount: validItemCount
    }
  }

  /**
   * Generic validation that handles both formats
   */
  public static validateOrderItems(items: any): ItemValidationResult {
    if (Array.isArray(items)) {
      return this.validateDirectOrderItems(items)
    } else if (typeof items === 'object') {
      return this.validateTempOrderItems(items)
    } else {
      return {
        isValid: false,
        errors: ['Items must be provided as an array or object'],
        itemCount: 0
      }
    }
  }

  /**
   * Helper to return standardized error response
   */
  public static createItemValidationErrorResponse(
    response: HttpContextContract['response'], 
    validation: ItemValidationResult
  ) {
    return response.badRequest({
      error: 'Order validation failed',
      details: 'Orders must contain at least one valid item',
      validation_errors: validation.errors,
      items_found: validation.itemCount
    })
  }

  /**
   * Validate required order fields
   */
  public static validateRequiredOrderFields(data: any): ItemValidationResult {
    const errors: string[] = []

    if (!data.vendorId) {
      errors.push('Vendor ID is required')
    }

    if (!data.branchId) {
      errors.push('Branch ID is required')
    }

    // Add other required field validations as needed

    return {
      isValid: errors.length === 0,
      errors,
      itemCount: 0
    }
  }

  /**
   * Comprehensive order validation (fields + items)
   */
  public static validateCompleteOrder(data: any): ItemValidationResult {
    const fieldValidation = this.validateRequiredOrderFields(data)
    const itemValidation = this.validateOrderItems(data.items)

    return {
      isValid: fieldValidation.isValid && itemValidation.isValid,
      errors: [...fieldValidation.errors, ...itemValidation.errors],
      itemCount: itemValidation.itemCount
    }
  }
}
