import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Form from '../../Models/Form'
import { bind } from '@adonisjs/route-model-binding'

export default class FormsController {
  /**
   * @index
   * @summary Show all forms
   * @version 1.0.0
   * @description Form management for the application
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page Number
   * @paramQuery order - Order by field
   * @paramQuery sort order - (asc, desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const formQuery = Form.filter(filters)

    return await formQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a form
   * @description Create a form with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <form>
   */
  public async store({ request }: HttpContextContract) {
    const { name, details, productId } = request.all()
    const form = new Form()

    form.fill({ name, details, productId })

    const image = request.file('image')

    if (image) {
      form.image = Attachment.fromFile(image)
    }

    return await form.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a form
   * @description Show a form with their details (name and details)
   * @responseBody 200 - <Form>
   * @response 404 - Form not found
   */
  public async show({ response }: HttpContextContract, form: Form) {
    return response.json(form)
  }

  @bind()

  /**
   * @update
   * @summary Update a form
   * @description Update a form with their details (name and details)
   * @requestBody <Form>
   * @responseBody 200 - <Form>
   * @response 404 - Form not found
   */
  public async update({ request, response }: HttpContextContract, form: Form) {
    const { name, details, sections } = request.all()
    const image = request.file('image')

    await form
      .merge({ name, details, sections: sections.reduce((p, s) => ({ ...p, [s.id]: s }), {}) })
      .save()

    if (image) {
      form.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(form)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a form
   * @responseBody 204 - No content
   *
   */
  public async destroy(_: HttpContextContract, form: Form) {
    return await form.delete()
  }
}
