import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import VendorCategory from 'App/Models/VendorCategory'

export default class VendorVendorCategoriesController {
  @bind()

  /**
   * @name VendorVendorCategory management
   * @index
   * @summary List all Vendors
   * @description List all Vendors, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const categoryQuery = vendor.related('categories').query().filter(filters)

    return await categoryQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @update
   * @summary Update a Vendor
   * @description Update a Vendor with their details (name and details)
   * @paramPath id required number - Vendor ID
   * @requestBody <Vendor>
   * @responseBody 200 - <Vendor>
   * @response 404 - VendorType not found
   */
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const { categoryIds } = request.all()
    await vendor.related('categories').sync(categoryIds)

    await vendor.load('categories')

    return response.json(vendor.categories)
  }

  /**
   * @destroy
   * @summary Delete a category
   * @responseBody 204 - No content
   */
  @bind()
  public async destroy({ response }: HttpContextContract, vendor: Vendor, category: VendorCategory) {
    await vendor.related('categories').detach([category.id])

    return response.noContent()
  }
}
