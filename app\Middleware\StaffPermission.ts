import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from 'App/Models/Order'

/**
 * Staff Permission Middleware
 * 
 * Validates that the authenticated user has permission to perform staff operations
 * on orders, particularly for transitioning temp orders to placed orders.
 */
export default class StaffPermission {
  /**
   * Handle staff permission validation
   */
  public async handle(
    { auth, response, params }: HttpContextContract,
    next: () => Promise<void>,
    guards: string[] = []
  ) {
    try {
      // Ensure user is authenticated
      if (!auth.user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // If no specific guards are specified, allow any authenticated staff
      if (guards.length === 0) {
        return await next()
      }

      // Check specific permission guards
      for (const guard of guards) {
        switch (guard) {
          case 'place-order':
            if (!(await this.canPlaceOrder(auth.user, params))) {
              return response.forbidden({ 
                error: 'You do not have permission to place orders' 
              })
            }
            break

          case 'manage-temp-orders':
            if (!(await this.canManageTempOrders(auth.user, params))) {
              return response.forbidden({ 
                error: 'You do not have permission to manage temp orders' 
              })
            }
            break

          case 'admin':
            if (!this.isAdmin(auth.user)) {
              return response.forbidden({ 
                error: 'Administrator privileges required' 
              })
            }
            break

          case 'branch-staff':
            if (!(await this.isBranchStaff(auth.user, params))) {
              return response.forbidden({ 
                error: 'You must be staff at this branch' 
              })
            }
            break

          default:
            console.warn(`Unknown staff permission guard: ${guard}`)
        }
      }

      await next()
    } catch (error) {
      console.error('Staff permission middleware error:', error)
      return response.internalServerError({ 
        error: 'Permission validation failed' 
      })
    }
  }

  /**
   * Check if user can place orders
   */
  private async canPlaceOrder(user: any, params: any): Promise<boolean> {
    try {
      // If no order ID in params, allow (will be validated later)
      if (!params.id) {
        return true
      }

      // Find the order to check permissions
      const order = await Order.query()
        .where('id', params.id)
        .where('status', 'Pending')
        .first()

      if (!order) {
        return false // Order not found or not in Pending status
      }

      return this.canStaffManageOrder(user, order)
    } catch (error) {
      console.error('Error checking place order permission:', error)
      return false
    }
  }

  /**
   * Check if user can manage temp orders
   */
  private async canManageTempOrders(user: any, params: any): Promise<boolean> {
    // For general temp order management, check if user is staff
    return this.isStaff(user)
  }

  /**
   * Check if user can manage a specific order
   */
  private canStaffManageOrder(user: any, order: Order): boolean {
    // Allow if user is the assigned staff for this order
    if (user.id === order.staffId) {
      return true
    }

    // Allow if user has admin/manager role
    if (this.isAdmin(user)) {
      return true
    }

    // Allow if user works at the same branch
    if (this.worksAtBranch(user, order.branchId)) {
      return true
    }

    return false
  }

  /**
   * Check if user is admin/manager
   */
  private isAdmin(user: any): boolean {
    return user.roles?.some((role: any) => 
      ['admin', 'manager', 'supervisor'].includes(role.name?.toLowerCase())
    ) || false
  }

  /**
   * Check if user is staff (has any staff role)
   */
  private isStaff(user: any): boolean {
    return user.roles?.some((role: any) => 
      ['staff', 'waiter', 'cashier', 'kitchen', 'admin', 'manager', 'supervisor']
        .includes(role.name?.toLowerCase())
    ) || false
  }

  /**
   * Check if user works at a specific branch
   */
  private worksAtBranch(user: any, branchId: string): boolean {
    return user.employers?.some((employer: any) => 
      employer.pivot?.branch_id === branchId
    ) || false
  }

  /**
   * Check if user is staff at a specific branch
   */
  private async isBranchStaff(user: any, params: any): Promise<boolean> {
    try {
      // Get branch ID from params or order
      let branchId = params.branchId

      if (!branchId && params.id) {
        // Try to get branch from order
        const order = await Order.query()
          .where('id', params.id)
          .select('branchId')
          .first()
        
        branchId = order?.branchId
      }

      if (!branchId) {
        return false
      }

      return this.worksAtBranch(user, branchId)
    } catch (error) {
      console.error('Error checking branch staff permission:', error)
      return false
    }
  }
}
