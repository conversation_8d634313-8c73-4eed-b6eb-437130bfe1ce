import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'
import Hotelplus from 'App/Services/Hotelplus'

export default class HotelPlusController {
  public async syncProducts({ response, params }: HttpContextContract) {
    const { vendorId } = params

    const hotelPlus = new Hotelplus(vendorId)

    const apiProducts = await hotelPlus.syncProducts()

    const products = await Product.createMany(apiProducts)

    return response.status(201).json(products)
  }
}
