import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import ServiceArea from 'App/Models/ServiceArea'


/**
 * @name Service Area management
 * @version 1.0.0
 * @description Service area management for vendors
 */
export default class VendorServiceAreasController {
  /**
   * @index
   * @summary List all service areas
   * @description List all service areas for a vendor, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const {
      per = 10,
      page = 1,
      order = 'createdAt',
      sort = 'asc',
      ...filters
    } = request.qs()

    const serviceAreaQuery = vendor
      .related('serviceAreas')
      .query()
      .preload('branch')

    // Apply basic filters manually
    if (filters.name) {
      serviceAreaQuery.where('name', 'like', `%${filters.name}%`)
    }
    if (filters.type) {
      serviceAreaQuery.where('type', filters.type)
    }
    if (filters.active !== undefined) {
      serviceAreaQuery.where('active', filters.active)
    }

    return await serviceAreaQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a service area
   * @description Create a new service area for a vendor
   */
  @bind()
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const {
      name,
      description,
      type,
      radius,
      geom,
      branch_id: branchId,
      priority = 0,
      active = true,
      meta,
    } = request.all()

    // Validate branch belongs to vendor
    await vendor.related('branches').query().where('id', branchId).firstOrFail()

    const serviceArea = await vendor.related('serviceAreas').create({
      name,
      description,
      type,
      radius: type === 'circle' ? radius : null,
      geom: type === 'polygon' ? geom : null,
      branchId,
      priority,
      active,
      meta,
    })

    return response.json(serviceArea)
  }

  /**
   * @show
   * @summary Show a service area
   * @description Show a specific service area
   */
  @bind()
  public async show({ response }: HttpContextContract, vendor: Vendor, serviceArea: ServiceArea) {
    // Ensure service area belongs to vendor
    if (serviceArea.vendorId !== vendor.id) {
      return response.forbidden({ message: 'Service area does not belong to vendor' })
    }

    await serviceArea.load('branch')
    return response.json(serviceArea)
  }

  /**
   * @update
   * @summary Update a service area
   * @description Update a specific service area
   */
  @bind()
  public async update({ request, response }: HttpContextContract, vendor: Vendor, serviceArea: ServiceArea) {
    // Ensure service area belongs to vendor
    if (serviceArea.vendorId !== vendor.id) {
      return response.forbidden({ message: 'Service area does not belong to vendor' })
    }

    const {
      name,
      description,
      type,
      radius,
      geom,
      branch_id: branchId,
      priority,
      active,
      meta,
    } = request.all()

    // If branchId is being updated, validate it belongs to vendor
    if (branchId && branchId !== serviceArea.branchId) {
      await vendor.related('branches').query().where('id', branchId).firstOrFail()
    }

    serviceArea.merge({
      name,
      description,
      type,
      radius: type === 'circle' ? radius : null,
      geom: type === 'polygon' ? geom : null,
      branchId,
      priority,
      active,
      meta,
    })

    await serviceArea.save()
    await serviceArea.load('branch')

    return response.json(serviceArea)
  }

  /**
   * @destroy
   * @summary Delete a service area
   * @description Delete a specific service area
   */
  @bind()
  public async destroy({ response }: HttpContextContract, vendor: Vendor, serviceArea: ServiceArea) {
    // Ensure service area belongs to vendor
    if (serviceArea.vendorId !== vendor.id) {
      return response.forbidden({ message: 'Service area does not belong to vendor' })
    }

    await serviceArea.delete()
    return response.noContent()
  }

  /**
   * @check
   * @summary Check if a point is in service area
   * @description Check if a given point is within any of the vendor's service areas
   */
  @bind()
  public async check({ request, response }: HttpContextContract, vendor: Vendor) {
    const { lat, lng } = request.qs()

    if (!lat || !lng) {
      return response.badRequest({ message: 'Latitude and longitude are required' })
    }

    // const point = Database.st().geomFromText(`POINT(${lng} ${lat})`, 4326)

    // Find all active service areas that contain the point
    const serviceAreas = await vendor
      .related('serviceAreas')
      .query()
      .where('active', true)
      .preload('branch')
      .orderBy('priority', 'desc')

    const matchingAreas: ServiceArea[] = []

    for (const area of serviceAreas) {
      if (await area.containsPoint(Number(lat), Number(lng))) {
        matchingAreas.push(area)
      }
    }

    return response.json({
      inServiceArea: matchingAreas.length > 0,
      serviceAreas: matchingAreas,
    })
  }
} 