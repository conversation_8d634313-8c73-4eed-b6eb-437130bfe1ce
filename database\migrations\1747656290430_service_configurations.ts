import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'service_configurations'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('name').notNullable()
      table.text('description').nullable()
      table.string('service_id').references('id').inTable('services').onDelete('CASCADE').index()
      table.boolean('active').notNullable().defaultTo(true).index()
      
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index(['service_id', 'active'])
      table.index(['name', 'active'])
      
      // Unique constraint for name per service
      table.unique(['name', 'service_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
