import { compose } from '@ioc:Adonis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import FormFilter from './Filters/FormFilter'
import { ulid } from 'ulidx'

export default class FormTemplate extends compose(BaseModel, Filterable) {
  public static $filter = () => FormFilter
  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public sections: Record<string, unknown>[]

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(template: FormTemplate) {
    template.id = ulid().toLowerCase()
  }
}
