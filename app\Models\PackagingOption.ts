import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  ManyToMany,
  beforeCreate,
  belongsTo,
  column,
  manyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { ulid } from 'ulidx'
import Vendor from './Vendor'
import Product from './Product'
import PackagingOptionFilter from './Filters/PackagingOptionFilter'

export default class PackagingOption extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => PackagingOptionFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public description: string | null

  @column()
  public price: number

  @column()
  public vendorId: string

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async assignId(packagingOption: PackagingOption) {
    packagingOption.id = ulid().toLowerCase()
  }

  /**
   * Relationships
   */
  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @manyToMany(() => Product, {
    pivotTable: 'product_packaging_options',
    localKey: 'id',
    pivotForeignKey: 'packaging_option_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'product_id',
  })
  public products: ManyToMany<typeof Product>
}
