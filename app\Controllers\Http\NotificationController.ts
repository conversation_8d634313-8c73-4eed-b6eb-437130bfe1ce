import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import RealTimeNotificationService from '../../Services/RealTimeNotificationService'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { DateTime } from 'luxon'

/**
 * Controller for managing real-time notifications
 */
export default class NotificationController {

  /**
   * @summary Get user notifications
   * @description Get all active notifications for the authenticated user
   * @responseBody 200 - User notifications
   */
  public async getUserNotifications({ response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      const notifications = RealTimeNotificationService.getActiveNotifications(user.id)

      return response.json({
        user_id: user.id,
        total_notifications: notifications.length,
        notifications: notifications.map(notification => ({
          id: notification.id,
          type: notification.type,
          priority: notification.priority,
          title: notification.title,
          message: notification.message,
          created_at: notification.created_at.toISO(),
          expires_at: notification.expires_at?.toISO(),
          action_url: notification.action_url,
          action_label: notification.action_label,
          requires_acknowledgment: notification.requires_acknowledgment,
          data: notification.data
        })),
        timestamp: DateTime.now().toISO()
      })

    } catch (error) {
      console.error('Error getting user notifications:', error)
      return response.status(500).json({
        error: 'Failed to get user notifications',
        details: error.message
      })
    }
  }

  /**
   * @summary Acknowledge notification
   * @description Mark a notification as acknowledged by the user
   * @paramPath id required string - Notification ID
   * @responseBody 200 - Acknowledgment result
   */
  public async acknowledgeNotification({ params, response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      const { id } = params
      const acknowledged = RealTimeNotificationService.acknowledgeNotification(id, user.id)

      if (acknowledged) {
        return response.json({
          status: 'success',
          message: 'Notification acknowledged',
          notification_id: id,
          acknowledged_by: user.id,
          acknowledged_at: DateTime.now().toISO()
        })
      } else {
        return response.badRequest({
          error: 'Failed to acknowledge notification',
          details: 'Notification not found or does not require acknowledgment'
        })
      }

    } catch (error) {
      console.error('Error acknowledging notification:', error)
      return response.status(500).json({
        error: 'Failed to acknowledge notification',
        details: error.message
      })
    }
  }

  /**
   * @summary Send custom notification
   * @description Send a custom notification to specified targets (admin/manager only)
   * @requestBody Custom notification data
   * @responseBody 200 - Send result
   */
  public async sendCustomNotification({ request, response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Check if user has permission to send notifications
      await user.load('roles')
      const canSendNotifications = user.roles.some(role => 
        ['admin', 'manager'].includes(role.name)
      )

      if (!canSendNotifications) {
        return response.forbidden({ 
          error: 'Insufficient permissions to send notifications' 
        })
      }

      const validationSchema = schema.create({
        type: schema.enum([
          'order_status_change',
          'item_status_change',
          'order_completion',
          'overdue_alert',
          'staff_assignment',
          'department_workload',
          'system_alert',
          'customer_notification',
          'manager_alert'
        ]),
        targets: schema.array().members(
          schema.object().members({
            type: schema.enum(['user', 'role', 'department', 'vendor', 'branch']),
            id: schema.string({ trim: true }),
            filters: schema.object.optional().anyMembers()
          })
        ),
        title: schema.string({ trim: true }),
        message: schema.string({ trim: true }),
        priority: schema.enum(['low', 'medium', 'high', 'critical']),
        action_url: schema.string.optional({ trim: true }),
        action_label: schema.string.optional({ trim: true }),
        expires_in_minutes: schema.number.optional(),
        requires_acknowledgment: schema.boolean.optional(),
        data: schema.object.optional().anyMembers()
      })

      const {
        type,
        targets,
        title,
        message,
        priority,
        action_url,
        action_label,
        expires_in_minutes,
        requires_acknowledgment,
        data = {}
      } = await request.validate({ schema: validationSchema })

      // Create custom template
      const customTemplate = {
        type,
        priority,
        title_template: title,
        message_template: message,
        action_url_template: action_url,
        action_label,
        expires_in_minutes,
        requires_acknowledgment
      }

      // Add sender information to data
      const notificationData = {
        ...data,
        sent_by: user.name,
        sent_by_id: user.id,
        custom_notification: true
      }

      // Send notification
      const notificationId = await RealTimeNotificationService.sendNotification(
        type,
        targets,
        notificationData,
        customTemplate
      )

      return response.json({
        status: 'success',
        message: 'Custom notification sent successfully',
        notification_id: notificationId,
        targets: targets.length,
        sent_by: user.name,
        timestamp: DateTime.now().toISO()
      })

    } catch (error) {
      console.error('Error sending custom notification:', error)
      return response.status(500).json({
        error: 'Failed to send custom notification',
        details: error.message
      })
    }
  }

  /**
   * @summary Get notification statistics
   * @description Get notification system statistics (admin only)
   * @responseBody 200 - Notification statistics
   */
  public async getStatistics({ response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Check if user is admin
      await user.load('roles')
      const isAdmin = user.roles.some(role => role.name === 'admin')
      
      if (!isAdmin) {
        return response.forbidden({ error: 'Admin access required' })
      }

      const statistics = RealTimeNotificationService.getStatistics()

      return response.json({
        status: 'success',
        timestamp: DateTime.now().toISO(),
        statistics
      })

    } catch (error) {
      console.error('Error getting notification statistics:', error)
      return response.status(500).json({
        error: 'Failed to get notification statistics',
        details: error.message
      })
    }
  }

  /**
   * @summary Test notification system
   * @description Send a test notification to verify system functionality (admin only)
   * @requestBody Test notification parameters
   * @responseBody 200 - Test result
   */
  public async testNotification({ request, response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Check if user is admin
      await user.load('roles')
      const isAdmin = user.roles.some(role => role.name === 'admin')
      
      if (!isAdmin) {
        return response.forbidden({ error: 'Admin access required' })
      }

      const validationSchema = schema.create({
        target_user_id: schema.string.optional({ trim: true }),
        target_type: schema.enum(['user', 'department', 'vendor', 'branch']).optional(),
        target_id: schema.string.optional({ trim: true }),
        message: schema.string.optional({ trim: true })
      })

      const { 
        target_user_id, 
        target_type = 'user', 
        target_id, 
        message = 'This is a test notification from the system'
      } = await request.validate({ schema: validationSchema })

      // Determine target
      const finalTargetId = target_user_id || target_id || user.id
      const targets = [{
        type: target_type as any,
        id: finalTargetId
      }]

      // Send test notification
      const notificationId = await RealTimeNotificationService.sendNotification(
        'system_alert',
        targets,
        {
          test_notification: true,
          sent_by: user.name,
          message
        },
        {
          type: 'system_alert',
          priority: 'medium',
          title_template: 'Test Notification',
          message_template: message,
          expires_in_minutes: 5
        }
      )

      return response.json({
        status: 'success',
        message: 'Test notification sent successfully',
        notification_id: notificationId,
        target: {
          type: target_type,
          id: finalTargetId
        },
        sent_by: user.name,
        timestamp: DateTime.now().toISO()
      })

    } catch (error) {
      console.error('Error sending test notification:', error)
      return response.status(500).json({
        error: 'Failed to send test notification',
        details: error.message
      })
    }
  }

  /**
   * @summary Get notification templates
   * @description Get available notification templates (admin only)
   * @responseBody 200 - Notification templates
   */
  public async getTemplates({ response, auth }: HttpContextContract) {
    try {
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Check if user is admin
      await user.load('roles')
      const isAdmin = user.roles.some(role => role.name === 'admin')
      
      if (!isAdmin) {
        return response.forbidden({ error: 'Admin access required' })
      }

      // Return template information (without exposing internal implementation)
      const templates = [
        {
          type: 'order_status_change',
          description: 'Notification for order status changes',
          priority: 'medium',
          target_roles: ['customer', 'staff', 'manager']
        },
        {
          type: 'item_status_change',
          description: 'Notification for item status changes',
          priority: 'low',
          target_roles: ['staff', 'manager']
        },
        {
          type: 'order_completion',
          description: 'Notification when order is completed',
          priority: 'high',
          target_roles: ['customer', 'staff']
        },
        {
          type: 'overdue_alert',
          description: 'Alert for overdue items',
          priority: 'high',
          target_roles: ['staff', 'manager']
        },
        {
          type: 'staff_assignment',
          description: 'Notification for staff assignments',
          priority: 'medium',
          target_roles: ['staff']
        },
        {
          type: 'department_workload',
          description: 'Department workload alerts',
          priority: 'medium',
          target_roles: ['manager']
        }
      ]

      return response.json({
        status: 'success',
        templates,
        timestamp: DateTime.now().toISO()
      })

    } catch (error) {
      console.error('Error getting notification templates:', error)
      return response.status(500).json({
        error: 'Failed to get notification templates',
        details: error.message
      })
    }
  }
}
