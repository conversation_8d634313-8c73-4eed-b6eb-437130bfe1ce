import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class UpdateDurationValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    name: schema.string.optional({ trim: true }, [
      rules.minLength(3),
      rules.maxLength(255),
      rules.unique({ 
        table: 'durations', 
        column: 'name',
        whereNot: { id: this.ctx.params.id }
      })
    ]),
    
    description: schema.string.optional({ trim: true }, [
      rules.maxLength(1000)
    ]),
    
    minutes: schema.number.optional([
      rules.range(1, 1440) // 1 minute to 24 hours
    ]),
    
    bufferMinutes: schema.number.optional([
      rules.range(0, 480) // 0 to 8 hours
    ]),
    
    category: schema.enum.optional(['short', 'medium', 'long', 'full-day'] as const),
    
    maxConcurrent: schema.number.optional([
      rules.range(1, 50)
    ]),
    
    allowsBackToBack: schema.boolean.optional(),
    
    requiredBreakAfter: schema.number.optional([
      rules.range(0, 480) // 0 to 8 hours
    ]),
    
    schedulingRules: schema.object.optional().members({
      minAdvanceHours: schema.number.optional([
        rules.range(0, 168) // 0 to 1 week
      ]),
      maxPerDay: schema.number.optional([
        rules.range(1, 50)
      ]),
      timeSlots: schema.array.optional().members(
        schema.string({ trim: true }, [
          rules.minLength(1),
          rules.maxLength(50)
        ])
      ),
      blackoutDays: schema.array.optional().members(
        schema.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as const)
      )
    }),
    
    branchConstraints: schema.object.optional().members({
      respectBranchHours: schema.boolean.optional(),
      staffRequired: schema.number.optional([
        rules.range(0, 20)
      ]),
      equipmentRequired: schema.array.optional().members(
        schema.string({ trim: true }, [
          rules.minLength(1),
          rules.maxLength(100)
        ])
      )
    }),
    
    active: schema.boolean.optional()
  })

  public messages: CustomMessages = {
    'name.minLength': 'Duration name must be at least 3 characters long',
    'name.maxLength': 'Duration name cannot exceed 255 characters',
    'name.unique': 'A duration with this name already exists',
    
    'description.maxLength': 'Description cannot exceed 1000 characters',
    
    'minutes.number': 'Duration minutes must be a number',
    'minutes.range': 'Duration minutes must be between 1 and 1440 (24 hours)',
    
    'bufferMinutes.number': 'Buffer minutes must be a number',
    'bufferMinutes.range': 'Buffer minutes must be between 0 and 480 (8 hours)',
    
    'category.enum': 'Category must be one of: short, medium, long, full-day',
    
    'maxConcurrent.number': 'Max concurrent must be a number',
    'maxConcurrent.range': 'Max concurrent must be between 1 and 50',
    
    'allowsBackToBack.boolean': 'Allows back to back must be true or false',
    
    'requiredBreakAfter.number': 'Required break after must be a number',
    'requiredBreakAfter.range': 'Required break after must be between 0 and 480 minutes',
    
    'schedulingRules.minAdvanceHours.range': 'Minimum advance hours must be between 0 and 168 (1 week)',
    'schedulingRules.maxPerDay.range': 'Max per day must be between 1 and 50',
    'schedulingRules.timeSlots.array': 'Time slots must be an array',
    'schedulingRules.blackoutDays.array': 'Blackout days must be an array',
    'schedulingRules.blackoutDays.*.enum': 'Invalid day of week',
    
    'branchConstraints.respectBranchHours.boolean': 'Respect branch hours must be true or false',
    'branchConstraints.staffRequired.range': 'Staff required must be between 0 and 20',
    'branchConstraints.equipmentRequired.array': 'Equipment required must be an array'
  }
}
