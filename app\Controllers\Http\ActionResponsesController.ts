import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import ActionResponse from 'App/Models/ActionResponse'
import MessageAction from 'App/Models/MessageAction'
import CreateActionResponseValidator from 'App/Validators/CreateActionResponseValidator'

/**
 * @swagger
 * components:
 *   schemas:
 *     ActionResponse:
 *       type: object
 *       properties:
 *         id:
 *           type: number
 *         action_id:
 *           type: number
 *         type:
 *           type: string
 *         text:
 *           type: string
 *         data:
 *           type: object
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */
export default class ActionResponsesController {
  /**
   * @swagger
   * /api/v1/messages/{id}/actions/{actionId}/responses:
   *   get:
   *     tags:
   *       - Action Responses
   *     summary: Get all responses for an action
   *     description: Returns a list of all responses associated with an action
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: actionId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       200:
   *         description: List of action responses
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 responses:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/ActionResponse'
   *       404:
   *         description: Message or action not found
   */
  @bind()
  public async index({ response }: HttpContextContract, action: MessageAction) {
    const responses = await action.related('responses').query()
    return response.ok({ responses })
  }

  /**
   * @swagger
   * /api/v1/messages/{id}/actions/{actionId}/responses:
   *   post:
   *     tags:
   *       - Action Responses
   *     summary: Create a new response for an action
   *     description: Creates a new response associated with an action
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: actionId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - type
   *             properties:
   *               type:
   *                 type: string
   *               text:
   *                 type: string
   *               data:
   *                 type: object
   *     responses:
   *       201:
   *         description: Response created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 response:
   *                   $ref: '#/components/schemas/ActionResponse'
   *       400:
   *         description: Invalid input
   *       404:
   *         description: Message or action not found
   */
  @bind()
  public async store({ request, response }: HttpContextContract, action: MessageAction) {
    const data = await request.validate(CreateActionResponseValidator)
    const actionResponse = await action.related('responses').create(data)
    return response.created({ response: actionResponse })
  }

  /**
   * @swagger
   * /api/v1/messages/{id}/actions/{actionId}/responses/{responseId}:
   *   get:
   *     tags:
   *       - Action Responses
   *     summary: Get a specific response
   *     description: Returns details of a specific response
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: actionId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: responseId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       200:
   *         description: Response details
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 response:
   *                   $ref: '#/components/schemas/ActionResponse'
   *       404:
   *         description: Message, action, or response not found
   */
  @bind()
  public async show({ response }: HttpContextContract, actionResponse: ActionResponse) {
    return response.ok({ response: actionResponse })
  }

  /**
   * @swagger
   * /api/v1/messages/{id}/actions/{actionId}/responses/{responseId}:
   *   delete:
   *     tags:
   *       - Action Responses
   *     summary: Delete a response
   *     description: Deletes an existing response
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: actionId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: responseId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       204:
   *         description: Response deleted successfully
   *       404:
   *         description: Message, action, or response not found
   */
  @bind()
  public async destroy({ response }: HttpContextContract, actionResponse: ActionResponse) {
    await actionResponse.delete()
    return response.noContent()
  }
} 