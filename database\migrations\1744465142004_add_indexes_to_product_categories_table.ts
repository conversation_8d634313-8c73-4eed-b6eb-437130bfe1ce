import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'product_categories'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add individual indexes
      table.index('product_type_id')
      table.index('slug')
      table.index('name')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove indexes
      table.dropIndex('product_type_id')
      table.dropIndex('slug')
      table.dropIndex('name')
    })
  }
} 