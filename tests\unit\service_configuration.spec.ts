import test from 'japa'
import Database from '@ioc:Adonis/Lucid/Database'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption, { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'
import Duration from 'App/Models/Duration'

test.group('Service Configuration Model', (group) => {
  group.beforeEach(async () => {
    await Database.beginGlobalTransaction()
  })

  group.afterEach(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('should create a service configuration', async (assert) => {
    const configData = {
      name: 'Home Cleaning Configuration',
      description: 'Standard home cleaning service options',
      serviceId: 'service-001',
      active: true
    }

    const config = await ServiceConfiguration.create(configData)

    assert.exists(config.id)
    assert.equal(config.name, configData.name)
    assert.equal(config.serviceId, configData.serviceId)
    assert.isTrue(config.active)
  })

  test('should create configuration options with duration links', async (assert) => {
    // Create a duration first
    const duration = await Duration.create(
      Duration.createTemplate('Test Duration', 120, 'medium')
    )

    // Create configuration
    const config = await ServiceConfiguration.create({
      name: 'Test Configuration',
      description: 'Test configuration',
      serviceId: 'service-001',
      active: true
    })

    // Create duration option
    const durationOption = await ServiceConfigurationOption.create({
      serviceConfigurationId: config.id,
      name: 'Standard Service (2 hours)',
      type: ServiceOptionType.DURATION,
      description: '2-hour standard service',
      priceAdjustment: 0,
      durationId: duration.id,
      isDefault: true,
      sortOrder: 1,
      constraints: {},
      active: true
    })

    // Create personnel option
    const personnelOption = await ServiceConfigurationOption.create({
      serviceConfigurationId: config.id,
      name: '2 Cleaners',
      type: ServiceOptionType.PERSONNEL,
      description: 'Two professional cleaners',
      priceAdjustment: 40,
      durationId: null,
      isDefault: false,
      sortOrder: 2,
      constraints: {},
      active: true
    })

    assert.exists(durationOption.id)
    assert.equal(durationOption.durationId, duration.id)
    assert.isTrue(durationOption.isDurationOption)
    assert.isTrue(durationOption.hasCalendarIntegration)

    assert.exists(personnelOption.id)
    assert.isNull(personnelOption.durationId)
    assert.isFalse(personnelOption.isDurationOption)
    assert.isFalse(personnelOption.hasCalendarIntegration)
  })

  test('should get options grouped by type', async (assert) => {
    const config = await ServiceConfiguration.create({
      name: 'Test Configuration',
      description: 'Test configuration',
      serviceId: 'service-001',
      active: true
    })

    // Create options of different types
    await ServiceConfigurationOption.createMany([
      {
        serviceConfigurationId: config.id,
        name: 'Quick Service',
        type: ServiceOptionType.DURATION,
        priceAdjustment: 0,
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true
      },
      {
        serviceConfigurationId: config.id,
        name: 'Deep Service',
        type: ServiceOptionType.DURATION,
        priceAdjustment: 50,
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true
      },
      {
        serviceConfigurationId: config.id,
        name: '1 Cleaner',
        type: ServiceOptionType.PERSONNEL,
        priceAdjustment: 0,
        isDefault: true,
        sortOrder: 3,
        constraints: {},
        active: true
      }
    ])

    const optionsByType = await config.getOptionsByType()

    assert.property(optionsByType, 'duration')
    assert.property(optionsByType, 'personnel')
    assert.equal(optionsByType.duration.length, 2)
    assert.equal(optionsByType.personnel.length, 1)
  })

  test('should validate configuration correctly', async (assert) => {
    const config = await ServiceConfiguration.create({
      name: 'Test Configuration',
      description: 'Test configuration',
      serviceId: 'service-001',
      active: true
    })

    // Configuration with no options should have warnings
    let validation = await config.validateConfiguration()
    assert.isTrue(validation.valid) // No errors, but warnings
    assert.isAbove(validation.warnings.length, 0)

    // Add valid options
    await ServiceConfigurationOption.create({
      serviceConfigurationId: config.id,
      name: 'Standard Service',
      type: ServiceOptionType.DURATION,
      priceAdjustment: 0,
      isDefault: true,
      sortOrder: 1,
      constraints: {},
      active: true
    })

    validation = await config.validateConfiguration()
    assert.isTrue(validation.valid)
    assert.equal(validation.errors.length, 0)
  })

  test('should clone configuration with options', async (assert) => {
    const originalConfig = await ServiceConfiguration.create({
      name: 'Original Configuration',
      description: 'Original description',
      serviceId: 'service-001',
      active: true
    })

    // Add options to original
    await ServiceConfigurationOption.createMany([
      {
        serviceConfigurationId: originalConfig.id,
        name: 'Option 1',
        type: ServiceOptionType.DURATION,
        priceAdjustment: 0,
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true
      },
      {
        serviceConfigurationId: originalConfig.id,
        name: 'Option 2',
        type: ServiceOptionType.PERSONNEL,
        priceAdjustment: 25,
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true
      }
    ])

    const clonedConfig = await originalConfig.clone('Cloned Configuration', 'Cloned description')

    assert.notEqual(clonedConfig.id, originalConfig.id)
    assert.equal(clonedConfig.name, 'Cloned Configuration')
    assert.equal(clonedConfig.description, 'Cloned description')
    assert.equal(clonedConfig.serviceId, originalConfig.serviceId)

    // Check that options were cloned
    const clonedOptions = await clonedConfig.related('options').query()
    assert.equal(clonedOptions.length, 2)
  })

  test('should get configuration statistics', async (assert) => {
    const config = await ServiceConfiguration.create({
      name: 'Test Configuration',
      description: 'Test configuration',
      serviceId: 'service-001',
      active: true
    })

    await ServiceConfigurationOption.createMany([
      {
        serviceConfigurationId: config.id,
        name: 'Duration Option',
        type: ServiceOptionType.DURATION,
        priceAdjustment: 0,
        isDefault: true,
        sortOrder: 1,
        constraints: {},
        active: true
      },
      {
        serviceConfigurationId: config.id,
        name: 'Personnel Option',
        type: ServiceOptionType.PERSONNEL,
        priceAdjustment: 25,
        isDefault: false,
        sortOrder: 2,
        constraints: {},
        active: true
      },
      {
        serviceConfigurationId: config.id,
        name: 'Inactive Option',
        type: ServiceOptionType.ADD_ON,
        priceAdjustment: 15,
        isDefault: false,
        sortOrder: 3,
        constraints: {},
        active: false
      }
    ])

    const stats = await config.getStatistics()

    assert.equal(stats.totalOptions, 3)
    assert.equal(stats.activeOptions, 2)
    assert.equal(stats.defaultOptions, 1)
    assert.equal(stats.durationOptions, 1)
    assert.property(stats.optionsByType, 'duration')
    assert.property(stats.optionsByType, 'personnel')
    assert.property(stats.optionsByType, 'add_on')
  })

  test('should create template configurations', async (assert) => {
    const basicTemplate = ServiceConfiguration.createTemplate(
      'Basic Service',
      'service-001',
      'basic'
    )

    assert.equal(basicTemplate.name, 'Basic Service')
    assert.equal(basicTemplate.serviceId, 'service-001')
    assert.include(basicTemplate.description, 'Basic service configuration')
    assert.isTrue(basicTemplate.active)

    const premiumTemplate = ServiceConfiguration.createTemplate(
      'Premium Service',
      'service-002',
      'premium',
      { description: 'Custom premium description' }
    )

    assert.equal(premiumTemplate.description, 'Custom premium description')
  })
})
