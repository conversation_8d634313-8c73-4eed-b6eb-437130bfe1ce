import Route from '@ioc:Adonis/Core/Route'
import User from 'App/Models/User'
import TestNotification from 'App/Notifications/TestNotification'

import HealthCheck from '@ioc:Adonis/Core/HealthCheck'

Route.get('queue', 'JobQueuesController').as('queue')

Route.get('notify', async ({ request, response }) => {
  const { email } = request.qs()
  const user = await User.findByOrFail('email', email)
  await user.notify(new TestNotification())
  return response.json(user)
})

/**
 * Auth Routes
 */
Route.group(() => {
  Route.group(() => {
    Route.post('', 'AuthController.login').as('login')
    Route.post('staff', 'AuthController.staffLogin').as('staff')
    Route.get('staff/me', 'AuthController.staffMe').middleware('auth').as('staffMe')
    Route.get('staff/role-actions', 'AuthController.getStaffRoleActions')
      .middleware('auth')
      .as('staffRoleActions')
  })
    .as('login')
    .prefix('login')

  Route.group(() => {
    Route.post('', 'AuthController.register')
    Route.post(':staffId', 'AuthController.staffRegister').middleware([
      'auth',
      'acl:staff,manager,admin,vendor,waiter,barman,barista,rider',
    ])
    Route.group(() => {
      Route.post('resend', 'AuthController.sendOtp')
      Route.post('verify', 'AuthController.verifyOtp')
    }).prefix('otp')
  }).prefix('register')

  Route.post('addrole', 'AuthController.addRole').middleware('auth')
  Route.post('addroletouser', 'AuthController.addRoleToUser').middleware('auth')
  Route.post('removerolefromuser', 'AuthController.removeRoleFromUser').middleware('auth')
  Route.get('getordersbyrole', 'AuthController.getOrdersFromRoles').middleware('auth')
  Route.get('staff-registration-stats', 'AuthController.getStaffRegistrationStats').middleware(
    'auth'
  )
  Route.get('staff/:staffId/customers', 'AuthController.getStaffCustomers').middleware([
    'auth',
    'acl:staff,manager,admin,vendor,waiter,barman,barista,rider',
  ])

  Route.get('me', 'AuthController.me').middleware('auth')
  Route.get('role-actions', 'AuthController.getRoleActions').middleware('auth')
  Route.post('logout', 'AuthController.logout').middleware('auth')
  Route.post('refresh', 'AuthController.refresh').as('refresh')

  Route.group(() => {
    Route.post('resend', 'AuthController.sendOtp')
    Route.post('verify', 'AuthController.verifyOtp')
  }).prefix('otp')

  Route.group(() => {
    Route.post('forgot', 'AuthController.forgot')
    Route.post('reset', 'AuthController.reset')
  }).prefix('password')

  Route.group(() => {
    Route.get(':provider', 'AuthController.socialRedirect').as('redirect')
    Route.get(':provider/callback', 'AuthController.socialCallback').as('callback')
    Route.post('mobile', 'AuthController.socialMobileAuth').as('mobile')
  })
    .prefix('social')
    .as('social')
}).prefix('v1/auth')

Route.get('health', async ({ response }) => {
  const report = await HealthCheck.getReport()

  return report.healthy ? response.ok(report) : response.badRequest(report)
})

/*Route.group(() => {
  Route.get('getcart', 'CartController.getCart').middleware('auth')
  Route.post('postcart', 'CartController.postCart').middleware('auth')

}).prefix('v1/cart')*/
