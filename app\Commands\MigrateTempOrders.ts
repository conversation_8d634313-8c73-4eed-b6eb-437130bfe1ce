import { BaseCommand, args, flags } from '@adonisjs/core/build/standalone'
import TempOrder from 'App/Models/TempOrder'
import Order from 'App/Models/Order'
import Database from '@ioc:Adonis/Lucid/Database'

export default class MigrateTempOrders extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'migrate:temp-orders'

  /**
   * Command description is displayed in the "help" output
   */
  public static description =
    'Migrate existing temp orders from temp_orders table to unified orders table'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest`
     * after creating the command to update the manifest file.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call
     * `node ace generate:manifest` after creating the command to update the manifest file.
     */
    stayAlive: false,
  }

  @flags.boolean({ description: 'Perform a dry run without making changes' })
  public dryRun: boolean

  @flags.boolean({ description: 'Force migration even if orders table has pending orders' })
  public force: boolean

  @flags.number({ description: 'Batch size for processing temp orders', default: 50 })
  public batchSize: number

  @flags.boolean({ description: 'Skip backup verification' })
  public skipBackup: boolean

  @flags.boolean({ description: 'Delete temp_orders table after successful migration' })
  public cleanup: boolean

  @flags.boolean({ description: 'Verify migration integrity after completion' })
  public verify: boolean

  public async run() {
    this.logger.info('🚀 Starting temp orders migration to unified system')

    try {
      // Step 1: Pre-migration checks
      await this.performPreMigrationChecks()

      // Step 2: Get temp orders to migrate
      const tempOrders = await this.getTempOrdersToMigrate()

      if (tempOrders.length === 0) {
        this.logger.success('✅ No temp orders found to migrate')
        return
      }

      this.logger.info(`📊 Found ${tempOrders.length} temp orders to migrate`)

      // Step 3: Perform migration
      if (this.dryRun) {
        await this.performDryRun(tempOrders)
      } else {
        await this.performMigration(tempOrders)

        // Step 4: Verify migration if requested
        if (this.verify) {
          await this.verifyMigration(tempOrders.length)
        }

        // Step 5: Cleanup if requested
        if (this.cleanup) {
          await this.performCleanup()
        }
      }

      this.logger.success('🎉 Temp orders migration completed successfully!')
    } catch (error) {
      this.logger.error('❌ Migration failed:', error.message)
      this.exitCode = 1
    }
  }

  /**
   * Perform pre-migration checks
   */
  private async performPreMigrationChecks(): Promise<void> {
    this.logger.info('🔍 Performing pre-migration checks...')

    // Check if backup exists
    if (!this.skipBackup) {
      const backupExists = await Database.rawQuery(
        "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'temp_orders_backup')"
      )

      if (!backupExists.rows[0].exists) {
        throw new Error(
          'Backup table temp_orders_backup not found. Run backup script first or use --skip-backup flag.'
        )
      }
      this.logger.success('✅ Backup table verified')
    }

    // Check for existing pending orders in orders table
    const existingPendingOrders = await Order.query().where('status', 'Pending').count('* as total')
    const pendingCount = Number(existingPendingOrders[0].$extras.total)

    if (pendingCount > 0 && !this.force) {
      throw new Error(
        `Found ${pendingCount} existing pending orders in orders table. ` +
          'This might indicate a partial migration. Use --force flag to proceed anyway.'
      )
    }

    if (pendingCount > 0) {
      this.logger.warning(
        `⚠️  Found ${pendingCount} existing pending orders, proceeding with --force flag`
      )
    }

    this.logger.success('✅ Pre-migration checks passed')
  }

  /**
   * Get temp orders to migrate
   */
  private async getTempOrdersToMigrate(): Promise<TempOrder[]> {
    this.logger.info('📋 Fetching temp orders to migrate...')

    const tempOrders = await TempOrder.query().preload('customer').orderBy('createdAt', 'asc')

    this.logger.info(`📊 Temp orders summary:`)
    this.logger.info(`   - Total: ${tempOrders.length}`)
    this.logger.info(`   - Status distribution:`)

    const statusCounts = tempOrders.reduce(
      (acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    Object.entries(statusCounts).forEach(([status, count]) => {
      this.logger.info(`     - ${status}: ${count}`)
    })

    return tempOrders
  }

  /**
   * Perform dry run
   */
  private async performDryRun(tempOrders: TempOrder[]): Promise<void> {
    this.logger.info('🧪 Performing dry run (no changes will be made)...')

    for (let i = 0; i < tempOrders.length; i += this.batchSize) {
      const batch = tempOrders.slice(i, i + this.batchSize)
      this.logger.info(
        `📦 Processing batch ${Math.floor(i / this.batchSize) + 1} (${batch.length} orders)`
      )

      for (const tempOrder of batch) {
        this.logger.info(`   - Would migrate: ${tempOrder.id} (${tempOrder.status})`)

        // Validate data structure
        this.validateTempOrderData(tempOrder)
      }
    }

    this.logger.success('✅ Dry run completed - all temp orders are valid for migration')
  }

  /**
   * Perform actual migration
   */
  private async performMigration(tempOrders: TempOrder[]): Promise<void> {
    this.logger.info('🔄 Starting actual migration...')

    let migratedCount = 0
    let errorCount = 0

    for (let i = 0; i < tempOrders.length; i += this.batchSize) {
      const batch = tempOrders.slice(i, i + this.batchSize)
      const batchNumber = Math.floor(i / this.batchSize) + 1

      this.logger.info(
        `📦 Processing batch ${batchNumber}/${Math.ceil(tempOrders.length / this.batchSize)} (${batch.length} orders)`
      )

      // Use transaction for each batch
      const trx = await Database.transaction()

      try {
        for (const tempOrder of batch) {
          await this.migrateSingleTempOrder(tempOrder, trx)
          migratedCount++
        }

        await trx.commit()
        this.logger.success(`✅ Batch ${batchNumber} completed successfully`)
      } catch (error) {
        await trx.rollback()
        errorCount += batch.length
        this.logger.error(`❌ Batch ${batchNumber} failed:`, error.message)

        if (!this.force) {
          throw error
        }
      }
    }

    this.logger.info(`📊 Migration summary:`)
    this.logger.info(`   - Successfully migrated: ${migratedCount}`)
    this.logger.info(`   - Errors: ${errorCount}`)
    this.logger.info(`   - Total processed: ${migratedCount + errorCount}`)

    if (errorCount > 0 && !this.force) {
      throw new Error(`Migration completed with ${errorCount} errors`)
    }
  }

  /**
   * Migrate a single temp order
   */
  private async migrateSingleTempOrder(tempOrder: TempOrder, trx: any): Promise<void> {
    // Validate temp order data
    this.validateTempOrderData(tempOrder)

    // Prepare meta with temp_items
    const unifiedMeta = {
      ...tempOrder.meta,
      temp_items: tempOrder.items, // Move items to meta.temp_items
    }

    // Create order in unified table
    const orderData = {
      id: tempOrder.id, // Keep same ID
      userId: tempOrder.userId,
      staffId: tempOrder.staffId,
      vendorId: tempOrder.vendorId,
      branchId: tempOrder.branchId,
      lotId: tempOrder.lotId,
      sectionId: tempOrder.sectionId,
      action: tempOrder.action,
      type: tempOrder.type,
      delivery: tempOrder.delivery,
      status: 'Pending', // All temp orders become Pending
      meta: unifiedMeta,
      ref: tempOrder.ref,
      acceptedAt: tempOrder.acceptedAt,
      createdAt: tempOrder.createdAt,
      updatedAt: tempOrder.updatedAt,
    }

    await trx.table('orders').insert(orderData)
  }

  /**
   * Validate temp order data
   */
  private validateTempOrderData(tempOrder: TempOrder): void {
    if (!tempOrder.id) {
      throw new Error('Temp order missing ID')
    }

    if (!tempOrder.vendorId) {
      throw new Error(`Temp order ${tempOrder.id} missing vendorId`)
    }

    if (!tempOrder.branchId) {
      throw new Error(`Temp order ${tempOrder.id} missing branchId`)
    }

    if (!tempOrder.items || Object.keys(tempOrder.items).length === 0) {
      throw new Error(`Temp order ${tempOrder.id} has no items`)
    }

    // Validate items structure
    Object.entries(tempOrder.items).forEach(([productId, itemData]) => {
      if (!productId) {
        throw new Error(`Temp order ${tempOrder.id} has invalid product ID`)
      }

      if (!itemData || typeof itemData !== 'object') {
        throw new Error(`Temp order ${tempOrder.id} has invalid item data for product ${productId}`)
      }

      if (!itemData.quantity || itemData.quantity <= 0) {
        throw new Error(`Temp order ${tempOrder.id} has invalid quantity for product ${productId}`)
      }
    })
  }

  /**
   * Verify migration integrity
   */
  private async verifyMigration(expectedCount: number): Promise<void> {
    this.logger.info('🔍 Verifying migration integrity...')

    // Count migrated orders
    const migratedOrders = await Order.query().where('status', 'Pending').count('* as total')
    const migratedCount = Number(migratedOrders[0].$extras.total)

    // Count remaining temp orders
    const remainingTempOrders = await TempOrder.query().count('* as total')
    const remainingCount = Number(remainingTempOrders[0].$extras.total)

    this.logger.info(`📊 Migration verification:`)
    this.logger.info(`   - Expected to migrate: ${expectedCount}`)
    this.logger.info(`   - Orders with Pending status: ${migratedCount}`)
    this.logger.info(`   - Remaining temp orders: ${remainingCount}`)

    // Verify data integrity for a sample
    const sampleSize = Math.min(10, migratedCount)
    const sampleOrders = await Order.query().where('status', 'Pending').limit(sampleSize)

    for (const order of sampleOrders) {
      if (!order.meta?.temp_items) {
        throw new Error(`Order ${order.id} missing temp_items in meta`)
      }

      if (Object.keys(order.meta.temp_items).length === 0) {
        throw new Error(`Order ${order.id} has empty temp_items`)
      }
    }

    this.logger.success(`✅ Migration verification passed`)
    this.logger.success(`   - ${migratedCount} orders successfully migrated`)
    this.logger.success(`   - ${remainingCount} temp orders remaining`)
    this.logger.success(`   - Sample data integrity verified`)
  }

  /**
   * Perform cleanup after successful migration
   */
  private async performCleanup(): Promise<void> {
    this.logger.info('🧹 Starting cleanup process...')

    // Final verification before cleanup
    const remainingTempOrders = await TempOrder.query().count('* as total')
    const remainingCount = Number(remainingTempOrders[0].$extras.total)

    if (remainingCount > 0) {
      this.logger.warning(`⚠️  Found ${remainingCount} remaining temp orders`)

      if (!this.force) {
        throw new Error('Cleanup aborted: temp orders still exist. Use --force to proceed anyway.')
      }
    }

    // Update migration metadata
    await Database.rawQuery(`
      UPDATE temp_orders_backup_metadata
      SET migration_status = 'migration_completed'
      WHERE backup_date = (SELECT MAX(backup_date) FROM temp_orders_backup_metadata)
    `)

    // Drop temp_orders table
    this.logger.info('🗑️  Dropping temp_orders table...')
    await Database.rawQuery('DROP TABLE IF EXISTS temp_orders CASCADE')

    this.logger.success('✅ Cleanup completed successfully')
    this.logger.success('   - temp_orders table removed')
    this.logger.success('   - Migration metadata updated')
    this.logger.warning('⚠️  Backup table temp_orders_backup preserved for safety')
  }
}
