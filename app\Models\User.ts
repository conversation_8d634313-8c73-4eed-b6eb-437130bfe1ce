import { DateTime } from 'luxon'
import Hash from '@ioc:Adonis/Core/Hash'
import {
  column,
  beforeSave,
  BaseModel,
  computed,
  HasMany,
  hasMany,
  manyToMany,
  ManyToMany,
  beforeCreate,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Notifiable } from '@ioc:Verful/Notification/Mixins'
import { attachment, AttachmentContract } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { SoftDeletes } from '@ioc:Adonis/Addons/LucidSoftDeletes'
import { Authorizable } from '@ioc:Verful/Permissions/Mixins'
import Database from '@ioc:Adonis/Lucid/Database'
import UserFilter from './Filters/UserFilter'
import { Observable } from '@ioc:Adonis/Addons/LucidObserver'
import crypto from 'crypto'
import CamelCaseStrategy from '../Strategies/CamelCaseStrategy'
import Payment from './Payment'
import Device from './Device'
import { ulid } from 'ulidx'
import Vendor from './Vendor'
import Branch from './Branch'
import Address from './Address'
import Group from './Group'
import Account from './Account'
import Lot from './Lot'
import Order from './Order'
import Subscription from './Subscription'
import Product from './Product'
import Department from './Department'

export default class User extends compose(
  BaseModel,
  Notifiable('notifications'),
  Observable,
  Filterable,
  SoftDeletes,
  Authorizable({
    permissionsPivotTable: 'user_permissions',
    rolesPivotTable: 'user_roles',
  })
) {
  public static table = 'users'
  public static $filter = () => UserFilter
  public static namingStrategy = new CamelCaseStrategy()

  /**
   * Serialize the `$extras` object as it is
   */
  public serializeExtras = true

  // protected static $observers = [new UserObserver()]

  @column({ isPrimary: true })
  public id: string

  @column()
  public title: string | null

  @column()
  public firstName: string

  @column()
  public lastName: string

  @column()
  public email: string

  @column()
  public phone: string | null

  @column()
  public idpass: string | null

  @column()
  public otp: number | null

  @column()
  public details: string | null

  @column()
  public gender: string | null

  @column()
  public dob: string | null

  @column({ serializeAs: null })
  public password: string

  @column()
  public status: 'Active' | 'Inactive' | 'Suspended'

  @attachment({ folder: 'avatars', preComputeUrl: true })
  public avatar: AttachmentContract | null

  @column()
  public location: {
    name?: string
    address: string
    regions: {
      administrative_area_level_3?: string | null
      administrative_area_level_1?: string | null
      country: string
    }
    coordinates: {
      lat: number
      lng: number
    }
    place_id: string
  } | null

  @column()
  public geom?: any

  @column()
  public meta: any

  @column()
  public rememberMeToken: string | null

  @column.dateTime()
  public phoneVerifiedAt: DateTime | null

  @column.dateTime()
  public emailVerifiedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime

  @computed()
  public get name(): string {
    return `${this.firstName} ${this.lastName}`
  }

  @computed()
  public get avatarUrl() {
    if (!this.avatar) {
      const hash = crypto
        .createHash('md5')
        .update(this.email ?? '<EMAIL>')
        .digest('hex')

      return `https://www.gravatar.com/avatar/${hash}?s=250&d=mp`
    }
  }

  @computed()
  public get initials() {
    let initials = this.email.slice(0, 2).toUpperCase()

    if (this.firstName) {
      initials = this.firstName.charAt(0).toUpperCase()

      if (this.lastName) {
        initials += this.lastName.charAt(0).toUpperCase()
      }
    }

    return initials
  }

  @beforeSave()
  public static async setGeom(user: User) {
    if (user.location?.coordinates) {
      user.geom = Database.st().geomFromText(
        `POINT(${user.location.coordinates.lng} ${user.location.coordinates.lat})`,
        4326
      )
    }
  }

  @computed()
  public get vendorId() {
    return this.$extras.pivot_vendor_id
  }

  @computed()
  public get identifier() {
    return this.$extras.pivot_identifier
  }

  @computed()
  public get online() {
    return this.$extras.pivot_online
  }

  @beforeCreate()
  public static async generateId(model: User) {
    model.id = ulid().toLowerCase()
  }

  @beforeSave()
  public static async hashPassword(user: User) {
    if (user.$dirty.password) {
      user.password = await Hash.make(user.password)
    }
  }

  @hasMany(() => Device)
  public devices: HasMany<typeof Device>

  @hasMany(() => Payment)
  public payments: HasMany<typeof Payment>

  @hasMany(() => Vendor)
  public vendors: HasMany<typeof Vendor>

  @hasMany(() => Address)
  public addresses: HasMany<typeof Address>

  @hasMany(() => Account)
  public accounts: HasMany<typeof Account>

  @hasMany(() => Order)
  public orders: HasMany<typeof Order>

  @manyToMany(() => Vendor, {
    pivotTable: 'staff',
    pivotTimestamps: true,
    pivotColumns: ['vendor_id', 'branch_id', 'identifier'],
  })
  public employers: ManyToMany<typeof Vendor>

  @manyToMany(() => Branch, {
    pivotTable: 'staff',
    pivotTimestamps: true,
    pivotColumns: ['vendor_id', 'branch_id', 'identifier'],
  })
  public stations: ManyToMany<typeof Branch>

  @manyToMany(() => Branch, {
    pivotTable: 'customers',
    pivotTimestamps: true,
    pivotColumns: ['vendor_id', 'branch_id', 'active', 'registered_by_staff_id'],
  })
  public branches: ManyToMany<typeof Branch>

  @manyToMany(() => User, {
    pivotTable: 'customers',
    pivotTimestamps: true,
    pivotColumns: ['vendor_id', 'branch_id'],
  })
  public brands: ManyToMany<typeof User>

  @manyToMany(() => Group, {
    pivotTable: 'customer_groups',
    pivotTimestamps: true,
    pivotColumns: ['vendor_id', 'branch_id'],
  })
  public groups: ManyToMany<typeof Group>

  @manyToMany(() => Product, {
    pivotTable: 'wishlists',
    pivotTimestamps: true,
  })
  public wishlist: ManyToMany<typeof Product>

  @manyToMany(() => Lot, {
    pivotTable: 'rosters',
    pivotTimestamps: true,
    pivotColumns: ['start_at', 'end_at'],
    pivotForeignKey: 'staff_id',
  })
  public lots: ManyToMany<typeof Lot>

  @hasMany(() => Subscription)
  public subscriptions: HasMany<typeof Subscription>

  // Department relationships for fulfillment tracking
  @manyToMany(() => Department, {
    pivotTable: 'department_staff',
    pivotTimestamps: true,
    pivotColumns: ['role', 'active', 'is_primary_department', 'skill_level', 'performance_rating']
  })
  public departments: ManyToMany<typeof Department>

  // Computed properties for department management
  @computed()
  public get primaryDepartment(): Department | null {
    if (!this.departments) return null
    return this.departments.find(dept => dept.$pivot.is_primary_department) || null
  }

  @computed()
  public get activeDepartments(): Department[] {
    if (!this.departments) return []
    return this.departments.filter(dept => dept.$pivot.active)
  }

  @computed()
  public get averageSkillLevel(): number {
    if (!this.departments || this.departments.length === 0) return 0
    const totalSkill = this.departments.reduce((sum, dept) => sum + (dept.$pivot.skill_level || 1), 0)
    return Math.round((totalSkill / this.departments.length) * 10) / 10
  }

  @computed()
  public get canSupervise(): boolean {
    if (!this.departments) return false
    return this.departments.some(dept =>
      dept.$pivot.role === 'supervisor' || dept.$pivot.can_supervise
    )
  }
}
