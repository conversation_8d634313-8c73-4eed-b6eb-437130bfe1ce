import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { ProductArchetype } from 'App/Enums/ProductArchetype'
import Product from './Product'

export default class ProductFulfillmentSetting extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public productId: string | null

  @column()
  public archetype: ProductArchetype

  @column()
  public isPayable: boolean

  @column()
  public hasForm: boolean

  @column()
  public isInformational: boolean

  @column()
  public isDeliverable: boolean

  @column()
  public isPickup: boolean

  @column()
  public physicalConsumptionIsOnsite: boolean

  @column()
  public isDownloadable: boolean

  @column()
  public digitalDeliveryMethod: 'emailLink' | 'inAppAccess' | 'licenseKey' | null

  @column()
  public isSchedulable: boolean

  @column()
  public serviceIsOnsite: boolean

  @column()
  public serviceIsRemote: boolean

  @column()
  public serviceIsDynamicQuery: boolean

  @column()
  public preorderAllowed: boolean

  @column()
  public scheduleAllowed: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Product)
  public product: BelongsTo<typeof Product>
} 