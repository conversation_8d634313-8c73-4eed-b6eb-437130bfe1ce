import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Order from 'App/Models/Order'
import Product from 'App/Models/Product'
import Lot from 'App/Models/Lot'
import User from 'App/Models/User'
import axios from 'axios'
import Database from '@ioc:Adonis/Lucid/Database'
import { OrderValidationHelper } from 'App/Helpers/OrderValidationHelper'

// Interface definition for the temporal product object structure
interface TemporalProductInfo {
  name: string
  pos_product_id: string
  aia_product_id: string
  user_id: string
  product_state: number
  product_station: string
  view_all: number
}

/**
 * @name Temp TempOrder management
 * @version 1.0.0
 * @description TempOrder management for the application
 */
export default class TempOrdersController {
  /**
   * @index
   * @summary List all TempOrders (now queries orders table)
   * @description List all TempOrders, paginated - now unified with orders table
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()

    // Query orders table for Pending status (temp orders)
    const OrderQuery = Order.query()
      .where('status', 'Pending')
      .preload('customer')
      .preload('branch')
      .preload('vendor')
      .preload('staff')
      .preload('section')

    // Apply filters if any (need to adapt filters for Order model)
    if (filters.vendorId) {
      OrderQuery.where('vendorId', filters.vendorId)
    }
    if (filters.branchId) {
      OrderQuery.where('branchId', filters.branchId)
    }
    if (filters.userId) {
      OrderQuery.where('userId', filters.userId)
    }
    if (filters.staffId) {
      OrderQuery.where('staffId', filters.staffId)
    }

    // Get temp orders with pagination
    const orders = await OrderQuery.orderBy(order, sort).paginate(page, per)

    // Format each order as temp order response
    const tempOrdersWithItems = await Promise.all(
      orders.map(async (order) => {
        return await this.formatAsTempOrder(order)
      })
    )

    return {
      ...orders.toJSON(),
      data: tempOrdersWithItems,
    }
  }

  /**
   * @store
   * @summary Create a TempOrder (now creates Order with Pending status)
   * @description Create a TempOrder with their details - now unified with orders table
   * @requestBody {"staffId": "", "vendorId": "", "branchId": "", "sectionId": "", "action": "", "type": "", "delivery": "", "status": "", "meta": {}} - <TempOrder>
   * @responseBody 200 - <TempOrder>
   */
  public async store({ request, response, auth }: HttpContextContract) {
    try {
      const {
        vendorId,
        branchId,
        sectionId,
        lotId,
        action,
        type,
        delivery,
        status = 'Pending', // Force Pending status for temp orders
        meta = {},
        staffId: postedStaffId,
        userId = null,
        items = {},
      } = request.all()

      // Validate required fields
      if (!vendorId) {
        return response.badRequest({ error: 'Vendor ID is required' })
      }

      if (!branchId) {
        return response.badRequest({ error: 'Branch ID is required' })
      }

      // Comprehensive item validation using OrderValidationHelper
      const itemValidation = OrderValidationHelper.validateTempOrderItems(items)
      if (!itemValidation.isValid) {
        return OrderValidationHelper.createItemValidationErrorResponse(response, itemValidation)
      }

      // Validate total
      if (meta?.charges) {
        const total = Object.values(meta.charges as Record<string, number>).reduce(
          (sum: number, charge: number) => sum + Number(charge),
          0
        )
        if (total < 0) {
          return response.badRequest({ error: 'Order total cannot be negative' })
        }
      }

      let staffId = postedStaffId

      console.log('STAFF: ' + staffId)
      console.log('REQUEST' + JSON.stringify(request.all()))

      if (!staffId) {
        const staff = lotId
          ? await Lot.find(lotId).then(async (lot) => await lot?.related('staff').query().first())
          : await User.query()
              .whereHas('employers', (q) => {
                q.where('branch_id', branchId)
                q.wherePivot('online', true)
              })
              .first()

        staffId = staff?.id
      }

      // Normalize charges in meta if present
      let normalizedMeta = { ...meta }
      if (meta?.charges) {
        const normalizedCharges: Record<string, number> = {}

        for (const [key, value] of Object.entries(meta.charges)) {
          if (value === 'NaN' || value === '"NaN"') {
            normalizedCharges[key] = 0
          } else if (typeof value === 'string') {
            const cleanValue = value.replace(/^"|"$/g, '') // Remove surrounding quotes
            const numericValue = parseFloat(cleanValue)
            normalizedCharges[key] = isNaN(numericValue) ? 0 : numericValue
          } else {
            normalizedCharges[key] = Number(value) || 0
          }
        }

        normalizedMeta.charges = normalizedCharges
      }

      // Prepare meta with temp_items for unified system
      const unifiedMeta = {
        ...normalizedMeta,
        temp_items: Object.keys(items).reduce(
          (acc, productId) => ({
            ...acc,
            [productId]: {
              quantity: items[productId],
            },
          }),
          {}
        ),
      }

      // Create Order with Pending status (unified temp order)
      console.log('🔄 Creating order...')
      const order = await Order.create({
        userId: userId ? userId : auth.user?.id,
        staffId,
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status: 'Pending', // Always Pending for temp orders
        meta: unifiedMeta, // Store items in meta.temp_items for temp orders
      })
      console.log('✅ Order created:', order.id)

      // Ensure pricing is calculated and stored for new temp order
      console.log('🔄 Calculating pricing...')
      await order.ensureTempOrderPricing()
      console.log('✅ Pricing calculated')

      // Load relationships for temp order response format
      console.log('🔄 Loading relationships...')
      await order.load('customer')
      console.log('✅ Customer loaded')
      await order.load('vendor')
      console.log('✅ Vendor loaded')
      await order.load('branch')
      console.log('✅ Branch loaded')

      // Only load section if sectionId is present
      if (order.sectionId) {
        await order.load('section')
        console.log('✅ Section loaded')
      } else {
        console.log('⚠️ Section skipped (no sectionId)')
      }

      // Associate customer with branch
      if (order.customer) {
        await order.customer.related('branches').sync(
          {
            [order.branchId]: {
              active: true,
              vendor_id: order.vendorId,
              branch_id: order.branchId,
            },
          },
          false
        )
      }

      // Format response as temp order for backward compatibility
      console.log('🔄 Formatting response...')
      const tempOrderResponse = await this.formatAsTempOrder(order)
      console.log('✅ Response formatted')
      return response.json(tempOrderResponse)
    } catch (error) {
      console.error(error)
      return response.badRequest({ error: error.message })
    }
  }

  /**
   * Helper method to format Order as TempOrder response for backward compatibility
   */
  private async formatAsTempOrder(order: Order) {
    console.log('🔄 formatAsTempOrder: Starting...')
    // Load products for temp_items if not already loaded
    const tempItems = order.meta?.temp_items || {}
    const productIds = Object.keys(tempItems)
    console.log(`🔄 formatAsTempOrder: Found ${productIds.length} product IDs`)

    let items: any[] = []
    if (productIds.length > 0) {
      console.log('🔄 formatAsTempOrder: Loading products with relationships...')
      // Load products with all relationships to match orders endpoint structure
      const products = await Product.query()
        .whereIn('id', productIds)
        .preload('category')
        .preload('gallery')
        .preload('forms')
        .preload('branch')
        .preload('vendor')
        .exec()
      console.log(`✅ formatAsTempOrder: Loaded ${products.length} products with relationships`)

      // Create items array matching the orders endpoint structure
      items = products.map((product) => {
        const quantity = tempItems[product.id]?.quantity || 0
        const productPrice = product.price || 0
        const totalItemCost = productPrice * quantity

        return {
          id: null, // No order_items record yet for temp orders
          orderId: order.id,
          productId: product.id,
          quantity: quantity,
          meta: null,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          price: productPrice, // ✅ FIX: Set actual product price instead of null
          status: 'pending',
          departmentId: null,
          assignedStaffId: null,
          estimatedPreparationTime: null,
          preparationStartedAt: null,
          preparationCompletedAt: null,
          servedAt: null,
          priorityLevel: 1,
          requiresSpecialAttention: false,
          specialInstructions: null,
          preparationNotes: null,
          statusHistory: null,
          qualityCheckStatus: 'not_required',
          qualityCheckedBy: null,
          customerModifications: null,
          cancellationReason: null,
          actualPreparationTime: null,
          preparationAttempts: 1,
          modifiers: [],
          product: product.toJSON(), // ✅ FIX: Now includes all relationships (category, gallery, forms, branch, vendor)
          actualPreparationTimeMinutes: null,
          isOverdue: false,
          preparationProgress: 0,
          statusDisplayName: 'Pending',
          canBeStarted: true,
          canBeCompleted: false,
          canBeServed: false,
          requiresAttention: false,
          estimatedCompletionTime: null,
          allModifiersCompleted: true,
          totalItemCost: totalItemCost, // ✅ FIX: Calculate actual cost instead of 0
        }
      })
    }

    // Get enhanced pricing using the same service as orders endpoint
    console.log('🔄 formatAsTempOrder: Getting pricing...')

    // Create a mock order object with items for pricing calculation
    const orderWithItems = {
      ...order.toJSON(),
      items: items,
    }

    // Use the same pricing service as orders endpoint for consistency
    const { default: OrderPricingService } = await import('../../Services/OrderPricingService')
    const orderWithPricing = OrderPricingService.addPricingToOrder(orderWithItems)

    console.log('✅ formatAsTempOrder: Pricing calculated using OrderPricingService')

    // Return temp order format with enhanced structure matching orders endpoint
    console.log('🔄 formatAsTempOrder: Building response object...')
    const response = {
      id: order.id,
      vendorId: order.vendorId,
      branchId: order.branchId,
      sectionId: order.sectionId,
      lotId: order.lotId,
      userId: order.userId,
      staffId: order.staffId,
      action: order.action,
      type: order.type,
      delivery: order.delivery,
      status: order.status,
      meta: order.meta,
      ref: order.ref,
      acceptedAt: order.acceptedAt,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      total: orderWithPricing.pricing?.total || 0, // ✅ FIX: Use calculated total from pricing service
      items: items, // ✅ FIX: Enhanced items with complete product relationships and correct pricing
      customer: order.customer,
      vendor: order.vendor,
      branch: order.branch,
      section: order.section,
      invoices: [], // ✅ FIX: Add empty arrays for consistency (temp orders don't have invoices yet)
      payments: [], // ✅ FIX: Add empty arrays for consistency (temp orders don't have payments yet)
      pricing: orderWithPricing.pricing, // ✅ FIX: Add pricing object for consistency with orders endpoint
    }
    console.log('✅ formatAsTempOrder: Response object built with enhanced structure')
    return response
  }

  /**
   * Helper method to calculate temp order total
   */
  private calculateTempOrderTotal(items: any[], charges?: Record<string, number>): number {
    let total = 0

    // Calculate items total
    items.forEach((item) => {
      total += parseInt(String(item.price)) * item.quantity
    })

    // Add charges if any
    if (charges) {
      total += Object.values(charges as Record<string, number>)?.reduce(
        (acc, charge) => acc + parseInt(String(charge)),
        0
      )
    }

    return total
  }

  /**
   * Helper method to validate temp order data
   */
  private validateTempOrderData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.vendorId) {
      errors.push('Vendor ID is required')
    }

    if (!data.branchId) {
      errors.push('Branch ID is required')
    }

    if (!data.items || Object.keys(data.items).length === 0) {
      errors.push('Order must contain at least one item')
    }

    if (data.meta?.charges) {
      const total = Object.values(data.meta.charges as Record<string, number>).reduce(
        (sum: number, charge: number) => sum + Number(charge),
        0
      )
      if (total < 0) {
        errors.push('Order total cannot be negative')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * @show
   * @summary Show a single TempOrder (now queries orders table)
   * @description Show a TempOrder with their details - now unified with orders table
   * @paramPath id required number - TempOrder ID
   * @responseBody 200 - <TempOrder>
   * @response 404 - TempOrder not found
   */
  public async show({ response, params }: HttpContextContract) {
    // Query orders table for Pending status (temp orders)
    const order = await Order.query()
      .where('id', params.id)
      .where('status', 'Pending')
      .preload('customer')
      .preload('vendor')
      .preload('branch')
      .preload('section')
      .firstOrFail()

    // Format as temp order response for backward compatibility
    const tempOrderResponse = await this.formatAsTempOrder(order)
    return response.json(tempOrderResponse)
  }

  /**
   * @update
   * @summary Update a TempOrder (now updates Order with Pending status)
   * @description Update a TempOrder - now unified with orders table
   * @paramPath id required string - TempOrder ID
   * @responseBody 200 - <TempOrder>
   * @response 404 - TempOrder not found
   */
  public async update({ request, response, params }: HttpContextContract) {
    try {
      // Find the pending order (temp order)
      const order = await Order.query()
        .where('id', params.id)
        .where('status', 'Pending')
        .firstOrFail()

      const updateData = request.all()

      // Validate update data
      const validation = this.validateTempOrderData(updateData)
      if (!validation.isValid) {
        return response.badRequest({ errors: validation.errors })
      }

      // Prepare updated meta with temp_items
      let updatedMeta = { ...order.meta, ...updateData.meta }
      if (updateData.items) {
        updatedMeta.temp_items = Object.keys(updateData.items).reduce(
          (acc, productId) => ({
            ...acc,
            [productId]: {
              quantity: updateData.items[productId],
            },
          }),
          {}
        )
      }

      // Normalize charges in meta if present
      if (updatedMeta.charges) {
        const normalizedCharges: Record<string, number> = {}

        for (const [key, value] of Object.entries(updatedMeta.charges)) {
          if (value === 'NaN' || value === '"NaN"') {
            normalizedCharges[key] = 0
          } else if (typeof value === 'string') {
            const cleanValue = value.replace(/^"|"$/g, '') // Remove surrounding quotes
            const numericValue = parseFloat(cleanValue)
            normalizedCharges[key] = isNaN(numericValue) ? 0 : numericValue
          } else {
            normalizedCharges[key] = Number(value) || 0
          }
        }

        updatedMeta.charges = normalizedCharges
      }

      // Update the order
      await order
        .merge({
          ...updateData,
          meta: updatedMeta,
          status: 'Pending', // Keep as Pending for temp orders
        })
        .save()

      // Recalculate pricing after update
      await order.ensureTempOrderPricing()

      // Load relationships
      await order.load('customer')
      await order.load('vendor')
      await order.load('branch')
      await order.load('section')

      // Format response as temp order
      const tempOrderResponse = await this.formatAsTempOrder(order)
      return response.json(tempOrderResponse)
    } catch (error) {
      console.error('Error updating temp order:', error)
      return response.badRequest({ error: error.message })
    }
  }

  /**
   * @destroy
   * @summary Delete a TempOrder (now deletes Order with Pending status)
   * @description Delete a TempOrder - now unified with orders table
   * @paramPath id required string - TempOrder ID
   * @responseBody 204 - No content
   * @response 404 - TempOrder not found
   */
  public async destroy({ response, params }: HttpContextContract) {
    try {
      // Find and delete the pending order (temp order)
      const order = await Order.query()
        .where('id', params.id)
        .where('status', 'Pending')
        .firstOrFail()

      await order.delete()
      return response.noContent()
    } catch (error) {
      console.error('Error deleting temp order:', error)
      return response.notFound({ error: 'Temp order not found' })
    }
  }

  /**
   * @update
   *
   * @summary Update a TempOrder (convert to Order and process POS data)
   * @description Takes a TempOrder ID via URL parameter and request body to create a final Order,
   *              process POS related data, interact with external POS system.
   * @paramPath id required number - TempOrder ID
   *
   * @requestBody <TempOrder & {id: string}> // Should include TempOrder fields and the ID of the TempOrder
   * @responseBody 201 - { message: string, order: Order }
   * @responseBody 403 - { message: string, err: any }
   * @response 404 - TempOrder not found
   */
  public async update({ request, response, auth }: HttpContextContract) {
    const transaction = await Database.transaction()

    try {
      const {
        id, // This is the TempOrder ID from the request body, used for deletion
        staffId,
        vendorId,
        branchId,
        sectionId,
        lotId,
        action,
        type,
        delivery,
        status,
        ref,
        meta,
        userId = null,
        items = {},
      } = request.all()

      console.log('request object', JSON.stringify(request.all()))

      // FIX: Explicitly type arrays
      var prod_ids: string[] = []
      var p_i = 0
      var item_obj = request.all().items // Assuming items is an array in the request for this logic

      // FIX: Define pload with correctly typed fields
      let pload: {
        waiter_id: string
        product_array: string[]
        table_no: string
        customer_name: string
        temporal_payload: TemporalProductInfo[]
        aia_order_id: string
      } = {
        waiter_id: '',
        product_array: [] as string[], // Explicit type
        table_no: '',
        customer_name: '',
        temporal_payload: [] as TemporalProductInfo[], // Explicit type
        aia_order_id: '',
      }

      var is_caught = false //Temporal payload creation error status
      // FIX: Explicitly type arrays
      var h_ids: string[] = [] //product id on pos
      var print_station: string[] = [] //product station name

      var staff_id = request.all().staffId
      var customer_name = ''

      try {
        const customer_data = await Database.from('users')
          .select('first_name', 'last_name')
          .where('id', request.all().userId) //Table info

        if (customer_data.length == 1) {
          customer_name = customer_data[0].first_name + ' ' + customer_data[0].last_name
        }
      } catch (e) {}

      const table_data = await Database.from('lots').select('name').where('id', lotId) //Table info

      var table_name = 'N/A'
      if (table_data.length == 1) {
        table_name = table_data[0].name
      }

      const waiter_data = await Database.from('users')
        .select('meta', 'idpass')
        .where('id', staff_id) //Get waiter info from staff id

      try {
        // Check if item_obj is an array before accessing length
        if (waiter_data.length == 1 && Array.isArray(item_obj)) {
          for (var i = 0; i < item_obj.length; i++) {
            // Ensure item_obj[i] exists and has an id property
            if (item_obj[i] && item_obj[i].id) {
              prod_ids[i] = item_obj[i].id
              p_i += 1
            }
          }

          if (p_i > 0) {
            //If products exist
            const products_by_id = await Database.from('products') //Get product ref from aia db
              .select('id', 'ref')
              .whereIn('id', prod_ids)

            var staff_list = await Database.from('staff')
              .select('user_id', 'identifier')
              .where('vendor_id', request.all().vendorId)

            // FIX: Explicitly type array
            var staff_m_ids: string[] = []
            for (var i = 0; i < staff_list.length; i++) {
              //Build staff ids
              staff_m_ids[i] = staff_list[i].user_id
            }

            var pos_users = await Database.from('users')
              .select('id', 'meta', 'first_name', 'last_name')
              // FIX: Use whereNotNull for checking non-null JSON column
              .whereNotNull('meta')
              .whereIn('id', staff_m_ids)

            // FIX: Explicitly type array
            var temporal_product_object: TemporalProductInfo[] = []
            var t_obj_count = 0

            for (var i = 0; i < products_by_id.length; i++) {
              //Build array of pos product ids
              try {
                // Basic check for ref format before splitting
                if (
                  products_by_id[i]?.ref &&
                  typeof products_by_id[i].ref === 'string' &&
                  products_by_id[i].ref.includes('-')
                ) {
                  h_ids[i] = products_by_id[i]['ref'].split('-')[0]
                  print_station[i] = products_by_id[i]['ref'].split('-')[1]
                } else {
                  console.warn(
                    `Product ${products_by_id[i]?.id} has invalid ref format: ${products_by_id[i]?.ref}`
                  )
                  continue // Skip this product if ref is invalid
                }

                for (var j = 0; j < pos_users.length; j++) {
                  // Ensure meta and pos_data exist before accessing nested properties
                  if (pos_users[j]?.meta?.pos_data?.pos_station) {
                    if (pos_users[j].meta.pos_data.pos_station.length > 1) {
                      //admin/waiter
                      temporal_product_object[t_obj_count] = {
                        name: pos_users[j].first_name + ' ' + pos_users[j].last_name,
                        pos_product_id: h_ids[i],
                        aia_product_id: products_by_id[i].id,
                        user_id: pos_users[j].id,
                        product_state: 0,
                        product_station: print_station[i],
                        view_all: 1,
                      }
                      t_obj_count += 1
                    } else {
                      //other user roles
                      if (print_station[i] == pos_users[j].meta.pos_data.pos_station[0]) {
                        temporal_product_object[t_obj_count] = {
                          name: pos_users[j].first_name + ' ' + pos_users[j].last_name,
                          pos_product_id: h_ids[i],
                          aia_product_id: products_by_id[i].id,
                          user_id: pos_users[j].id,
                          product_state: 0,
                          product_station: print_station[i],
                          view_all: 0,
                        }
                        t_obj_count += 1
                      }
                    }
                  } else {
                    console.warn(`User ${pos_users[j]?.id} has missing meta.pos_data.pos_station`)
                  }
                }
              } catch (e) {
                console.error('Error processing product ref or user station:', e) // Log error
                is_caught = true
              }
            }

            // Ensure waiter_data[0] and nested properties exist before accessing
            if (waiter_data[0]?.meta?.pos_data?.pos_id) {
              pload.waiter_id = waiter_data[0].meta.pos_data.pos_id
            } else {
              console.warn(`Waiter ${staff_id} missing meta.pos_data.pos_id`)
              // Handle missing waiter pos_id appropriately, maybe set a default or raise error
              pload.waiter_id = 'UNKNOWN' // Example default
            }
            pload.product_array = h_ids
            pload.table_no = table_name
            pload.customer_name = customer_name
            pload.temporal_payload = temporal_product_object // Already assigned the typed array
            pload.aia_order_id = ''
          } else {
            //console.group(' **** NO PRODUCT ****');
          }
        } else if (!Array.isArray(item_obj)) {
          console.warn('items object in request is not an array, skipping POS product processing.')
        }
      } catch (e) {
        console.error('Error fetching waiter/product data:', e) // Log error
        is_caught = true
      }

      meta.pos_data = pload //Add pos data to order meta

      const order = await Order.create(
        {
          userId: userId ? userId : auth.user?.id,
          staffId,
          vendorId,
          branchId,
          lotId,
          sectionId,
          action,
          type,
          delivery,
          status,
          ref,
          meta, // Include the enriched meta here
        },
        { client: transaction }
      )

      const itemsToAdd: {
        order_id: string
        product_id: string
        quantity: number
        created_at: string
        updated_at: string
      }[] = []
      const createdAt: string = new Date().toISOString().replace('T', ' ').replace('Z', '+00') // Consider Luxon for consistency

      // Handle items object (assuming it's key-value from the start of the method)
      if (typeof items === 'object' && items !== null && !Array.isArray(items)) {
        for (let productId of Object.keys(items)) {
          const itemValue = items[productId]
          // Check if itemValue is an object with 'id' and 'quantity' (like from `placeOrder`?)
          // OR if it's just a quantity number. This adapts to potential inconsistencies.
          if (
            typeof itemValue === 'object' &&
            itemValue !== null &&
            itemValue.hasOwnProperty('id') &&
            itemValue.hasOwnProperty('quantity')
          ) {
            itemsToAdd.push({
              order_id: order.id,
              product_id: itemValue['id'], // Use the id from the inner object
              quantity: Number(itemValue['quantity']), // Ensure quantity is number
              created_at: createdAt,
              updated_at: createdAt,
            })
          } else if (
            typeof itemValue === 'number' ||
            (typeof itemValue === 'string' && !isNaN(Number(itemValue)))
          ) {
            // Assumes the key `productId` is the actual product ID here
            itemsToAdd.push({
              order_id: order.id,
              product_id: productId,
              quantity: Number(itemValue), // Ensure quantity is number
              created_at: createdAt,
              updated_at: createdAt,
            })
          } else if (
            typeof itemValue === 'object' &&
            itemValue !== null &&
            itemValue.hasOwnProperty('quantity')
          ) {
            // Handles the structure { productId: { quantity: X } } from store method
            itemsToAdd.push({
              order_id: order.id,
              product_id: productId,
              quantity: Number(itemValue['quantity']), // Ensure quantity is number
              created_at: createdAt,
              updated_at: createdAt,
            })
          } else {
            console.warn(
              `Skipping item with unexpected format: ${productId} -> ${JSON.stringify(itemValue)}`
            )
          }
        }
      } else {
        console.warn(
          'Items data is not in the expected format (object/dictionary). Cannot add items.'
        )
      }

      const tempOrderToDelete: string = id // ID from request body

      if (itemsToAdd.length > 0) {
        await Database.table('order_items').useTransaction(transaction).multiInsert(itemsToAdd)
      }

      // Legacy temp_orders deletion removed - now using unified orders table
      // The temp order conversion is handled by status change from 'Pending' to 'Placed'
      console.log('Temp order conversion completed using unified orders table')

      // Reload items association on the Order model instance
      await order.load('items') // Make sure this relation is defined in Order model

      // Check order.items *after* loading
      if (order.items) {
        // Recalculate amount based on loaded items if necessary (ensure Product model has 'price')
        // Assuming Order 'items' relationship loads Product models which have a 'price' property
        let amount = order.items.reduce((acc, item) => {
          // Ensure item and price are valid before calculation
          const price = Number(item.price || 0)
          const quantity = Number(item.$extras.pivot_quantity || 0) // Access quantity from pivot table
          return acc + price * quantity
        }, 0)

        if (order.meta && order.meta.charges) {
          amount += Object.values(order.meta.charges as Record<string, number>)?.reduce(
            (acc, charge) => acc + Number(charge || 0),
            0
          )
        }

        await order.related('invoices').create({
          amount,
          status: 'Pending',
        }) // Ensure 'invoices' relationship is defined

        await order.load('customer') // Ensure 'customer' relationship is defined

        // Check if customer loaded successfully
        if (order.customer) {
          await order.customer.related('branches').sync(
            {
              [order.branchId]: {
                active: true,
                vendor_id: order.vendorId,
                branch_id: order.branchId,
              },
            },
            false
          ) // Ensure 'branches' relationship is defined on User
        } else {
          console.warn(`Customer with ID ${order.userId} not found or relation failed.`)
        }

        if (h_ids.length > 0 && !is_caught) {
          //Do push to POS only if product refs exist
          pload.aia_order_id = order.id
          console.log('*** PLOAD POS: ' + JSON.stringify(pload))

          // Insert into temporal_orders table for logging/retry purposes
          await transaction
            .insertQuery()
            .table('temporal_orders') // Ensure this table exists
            .insert({ order_id: order.id, order_object: JSON.stringify(pload), status: '0' }) // Store pload as JSON string if column type is text/json

          // Push to external POS system
          try {
            // Use environment variables for the URL
            const posUrl = process.env.POS_PUSH_URL || 'https://staging-temporal.appinapp.ke/push' // Default fallback
            await axios.post(posUrl, pload)
            // Consider adding success logging or updating temporal_orders status
          } catch (axiosError) {
            console.error('Failed to push order to POS:', axiosError.message)
            // Decide if this should cause the transaction to rollback
            // For now, we continue and commit, but log the error.
            // Could update temporal_orders status to 'failed' here.
          }
        }

        await transaction.commit()

        // Reload the full order with relations for the response
        await order.load((loader) => {
          loader.load('items').load('invoices').load('customer')
        })

        return response.status(201).json({ message: 'Order created successfully', order: order })
      } else {
        // Handle case where items didn't load correctly or there were no items
        console.error('Order items could not be loaded after creation.')
        await transaction.rollback() // Rollback if items are crucial and failed to load/associate
        return response.status(500).json({ message: 'Failed to process order items.' })
      }
    } catch (error) {
      await transaction.rollback() // Rollback on any error during the process
      console.error('ERROR SECTION', error) // Use console.error for errors
      // Provide a more generic error message to the client for security
      return response
        .status(500)
        .json({ message: 'Failed to process your order due to an internal error.' })
      // Optionally log the specific error internally: return response.status(403).json({ message: "failed to process your order", err: error.message }) // Be cautious exposing internal errors
    }
  }

  // Note: This function seems largely redundant with `update` or `placeOrder` and uses commented-out logic.
  // Consider removing or refactoring if its purpose is covered elsewhere.
  public async updateOld({ request, response, auth }: HttpContextContract) {
    try {
      const {
        staffId,
        vendorId,
        branchId,
        sectionId,
        lotId,
        action,
        type,
        delivery,
        status,
        ref,
        meta,
        userId = null,
        items = {}, // Assuming this follows the same structure as in 'update' or 'store'
      } = request.all()

      // FIX: Removed unused variables prod_ids and p_i
      // var prod_ids = [] // REMOVED
      // var p_i = 0       // REMOVED

      /* Commented out POS logic - remains commented */
      /* ... */

      const order = await Order.create({
        userId: userId ? userId : auth.user?.id,
        staffId,
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        ref,
        meta,
      })

      // Reusing the item processing logic from 'update' for consistency
      const itemsToAdd: { [key: string]: { quantity: number } } = {}
      if (typeof items === 'object' && items !== null && !Array.isArray(items)) {
        for (let productId of Object.keys(items)) {
          const itemValue = items[productId]
          if (
            typeof itemValue === 'object' &&
            itemValue !== null &&
            itemValue.hasOwnProperty('id') &&
            itemValue.hasOwnProperty('quantity')
          ) {
            // Structure { someKey: { id: 'prod1', quantity: 2 } } -> use inner id
            itemsToAdd[itemValue['id']] = { quantity: Number(itemValue['quantity']) }
          } else if (
            typeof itemValue === 'number' ||
            (typeof itemValue === 'string' && !isNaN(Number(itemValue)))
          ) {
            // Structure { prod1: 2 } -> use key as id
            itemsToAdd[productId] = { quantity: Number(itemValue) }
          } else if (
            typeof itemValue === 'object' &&
            itemValue !== null &&
            itemValue.hasOwnProperty('quantity')
          ) {
            // Structure { prod1: { quantity: 2 } } -> use key as id
            itemsToAdd[productId] = { quantity: Number(itemValue['quantity']) }
          } else {
            console.warn(
              `updateOld: Skipping item with unexpected format: ${productId} -> ${JSON.stringify(itemValue)}`
            )
          }
        }
      }

      // Replace items if any were successfully processed
      if (Object.keys(itemsToAdd).length > 0) {
        // Delete existing items and create new ones
        await order.related('items').query().delete()
        await Promise.all(
          Object.keys(itemsToAdd).map(async (productId) => {
            return await order.related('items').create({
              productId: productId,
              quantity: itemsToAdd[productId].quantity,
            })
          })
        )
      }

      // Legacy tempOrder.delete() removed - now using unified orders table
      // The temp order is converted by updating status from 'Pending' to 'Placed'

      // Using .then() structure as in original, but consider async/await for clarity
      await order.load('items').then(async () => {
        if (order.items) {
          let amount = order.items.reduce((acc, item) => {
            const price = Number(item.price || 0)
            const quantity = Number(item.$extras.pivot_quantity || 0)
            return acc + price * quantity
          }, 0)

          if (order.meta && order.meta.charges) {
            amount += Object.values(order.meta.charges as Record<string, number>)?.reduce(
              (acc, charge) => acc + Number(charge || 0),
              0
            )
          }

          await order.related('invoices').create({
            amount,
            status: 'Pending',
          })
        } else {
          console.warn('updateOld: Order items not found after loading.')
        }
      })

      await order.load('customer')
      if (order.customer) {
        order.customer.related('branches').sync(
          {
            [order.branchId]: {
              active: true,
              vendor_id: order.vendorId,
              branch_id: order.branchId,
            },
          },
          false
        )
      } else {
        console.warn(`updateOld: Customer ${order.userId} not loaded.`)
      }

      // Reload relations for response
      await order.load((loader) => {
        loader.load('items').load('invoices').load('customer')
      })

      return response.json(order)
    } catch (error) {
      console.error('Error in updateOld:', error) // Log specific error
      return response.badRequest({ error: 'Failed to process order (legacy method).' }) // More generic client message
    }
  }

  /**
   * @placeOrder
   * @summary Place a TempOrder (convert Pending Order to Placed Order)
   * @description Convert a Pending Order (temp order) to Placed Order, creating OrderItems and Invoice
   * @paramPath id required string - TempOrder ID to place
   * @requestBody {} - Optional overrides for order data
   * @responseBody 200 - <Order>
   * @response 400 - { error: string }
   * @response 403 - { error: string } - Staff permission required
   * @response 404 - TempOrder not found
   */
  public async placeOrder({ request, response, auth, params }: HttpContextContract) {
    try {
      // Find the pending order (temp order)
      const order = await Order.query()
        .where('id', params.id)
        .where('status', 'Pending')
        .preload('customer')
        .firstOrFail()

      // Check staff permissions
      if (!this.canStaffPlaceOrder(auth.user, order)) {
        return response.forbidden({ error: 'Only authorized staff can place orders' })
      }

      // Get any overrides from request
      const overrides = request.all()

      // Convert meta.temp_items to OrderItems
      await this.convertTempItemsToOrderItems(order)

      // Update order status and any overrides
      await order
        .merge({
          ...overrides,
          status: 'Placed',
          ref: overrides.ref || null,
        })
        .save()

      // Calculate total amount from OrderItems
      await order.load('items')
      let amount = 0

      if (order.items && order.items.length > 0) {
        // Load products for each order item to get prices
        await Promise.all(
          order.items.map(async (item) => {
            await item.load('product')
            const price = Number(item.product?.price || 0)
            const quantity = Number(item.quantity || 0)
            amount += price * quantity
          })
        )
      }

      // Add charges from meta
      if (order.meta && order.meta.charges) {
        amount += Object.values(order.meta.charges as Record<string, number>)?.reduce(
          (acc, charge) => acc + Number(charge || 0),
          0
        )
      }

      // Create invoice
      await order.related('invoices').create({
        amount,
        status: 'Pending',
      })

      // Generate order number if not exists
      if (!order.orderNumber) {
        order.orderNumber = await this.generateOrderNumber()
        await order.save()
      }

      // Associate customer with branch
      if (order.customer) {
        await order.customer.related('branches').sync(
          {
            [order.branchId]: {
              active: true,
              vendor_id: order.vendorId,
              branch_id: order.branchId,
            },
          },
          false
        )
      }

      // Load all relationships for response
      await order.load((loader) => {
        loader
          .load('items', (itemsQuery) => {
            itemsQuery.preload('product')
          })
          .load('invoices')
          .load('customer')
          .load('vendor')
          .load('branch')
          .load('section')
      })

      // Send notifications (replace queue jobs)
      await this.sendOrderNotifications(order)

      return response.json(order)
    } catch (error) {
      console.error('Error in placeOrder:', error)
      if (error.code === 'E_ROW_NOT_FOUND') {
        return response.notFound({ error: 'Temp order not found' })
      }
      return response.badRequest({ error: 'Failed to place order.' })
    }
  }

  /**
   * Helper method to check if staff can place orders
   */
  private canStaffPlaceOrder(user: any, order: Order): boolean {
    if (!user) return false

    // Allow if user is the assigned staff for this order
    if (user.id === order.staffId) return true

    // Allow if user has admin/manager role
    if (
      user.roles?.some((role: any) =>
        ['admin', 'manager', 'supervisor'].includes(role.name?.toLowerCase())
      )
    ) {
      return true
    }

    // Allow if user works at the same branch
    if (user.employers?.some((employer: any) => employer.pivot?.branch_id === order.branchId)) {
      return true
    }

    return false
  }

  /**
   * Helper method to convert meta.temp_items to OrderItems table
   */
  private async convertTempItemsToOrderItems(order: Order): Promise<void> {
    const tempItems = order.meta?.temp_items || {}

    if (Object.keys(tempItems).length === 0) {
      console.warn('No temp_items found in order meta')
      return
    }

    // Clear existing order items (if any)
    await order.related('items').query().delete()

    // Create new order items from temp_items
    for (const [productId, itemData] of Object.entries(tempItems)) {
      await order.related('items').create({
        productId,
        quantity: (itemData as any).quantity || 1,
      })
    }

    // Remove temp_items from meta after conversion
    const newMeta = { ...order.meta }
    delete newMeta.temp_items
    order.meta = newMeta
    await order.save()
  }

  /**
   * Helper method to generate order number
   */
  private async generateOrderNumber(): Promise<string> {
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')
    return `ORD-${timestamp}-${random}`
  }

  /**
   * Helper method to send order notifications (replaces queue jobs)
   */
  private async sendOrderNotifications(order: Order): Promise<void> {
    try {
      // This replaces the ProcessOrder queue job
      console.log(`Order placed: ${order.id} - ${order.orderNumber}`)

      // TODO: Implement actual notification logic
      // - Send SMS/email to customer
      // - Notify kitchen/staff
      // - Update external systems (POS, etc.)

      // For now, just log the notification
      console.log(`Notifications sent for order ${order.id}`)
    } catch (error) {
      console.error('Failed to send order notifications:', error)
      // Don't throw error - notifications are not critical for order placement
    }
  }

  /**
   * @destroy
   * @summary Delete a temporary order (now deletes Order with Pending status)
   * @description Delete a TempOrder - now unified with orders table
   * @paramPath id required string - TempOrder ID
   * @responseBody 204 - No content
   * @response 404 - TempOrder not found
   */
  public async destroy({ response, params }: HttpContextContract) {
    try {
      // Find and delete the pending order (temp order)
      const order = await Order.query()
        .where('id', params.id)
        .where('status', 'Pending')
        .firstOrFail()

      await order.delete()
      return response.noContent()
    } catch (error) {
      console.error('Error deleting temp order:', error)
      return response.notFound({ error: 'Temp order not found' })
    }
  }
}
