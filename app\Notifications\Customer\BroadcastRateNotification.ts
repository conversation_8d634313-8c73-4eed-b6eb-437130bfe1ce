import { NotificationContract, NotificationChannelsList } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import User from 'App/Models/User'

interface RateChange {
  service: string
  oldRate: number
  newRate: number
  currency: string
  effectiveDate: Date
}

export default class BroadcastRateNotification implements NotificationContract {
  constructor(
    private title: string,
    private message: string,
    private rateChanges: RateChange[] = [],
    private effectiveDate?: Date,
    private campaignId?: string
  ) {}

  public via(_notifiable: User): (keyof NotificationChannelsList)[] {
    return ['database', 'fcm']
  }

  public toDatabase(notifiable: User) {
    // Personalize message
    const personalizedMessage = this.message
      .replace('{firstName}', notifiable.firstName || '')
      .replace('{lastName}', notifiable.lastName || '')

    // Create context for actions
    const actionContext = {
      section: 'pricing',
      highlight: 'changes',
      ...(this.campaignId && { campaignId: this.campaignId }),
      ...(this.effectiveDate && { effectiveDate: this.effectiveDate.toISOString() })
    }

    return NotificationHelper.createNotificationData(
      this.title,
      personalizedMessage,
      NotificationHelper.createPromotionActions('rate', actionContext),
      {
        category: NotificationCategory.PROMOTION,
        notificationType: NotificationType.RATE_UPDATE,
        priority: NotificationPriority.MEDIUM,
        campaignId: this.campaignId || `rate_update_${new Date().getFullYear()}_${new Date().getMonth() + 1}`,
        rateChanges: this.rateChanges,
        effectiveDate: this.effectiveDate?.toISOString(),
        totalChanges: this.rateChanges.length
      },
      'https://cdn.verful.com/icons/rate-update-icon.png'
    )
  }

  public toFcm(notifiable: User) {
    const personalizedMessage = this.message
      .replace('{firstName}', notifiable.firstName || '')
      .replace('{lastName}', notifiable.lastName || '')

    return {
      title: this.title,
      body: personalizedMessage,
      url: '/pricing',
      icon: 'https://cdn.verful.com/icons/rate-update-icon.png',
      actions: [
        {
          screen: 'view_rates',
          label: 'View New Rates',
          args: {}
        },
        {
          screen: 'compare_plans',
          label: 'Compare Plans',
          args: {}
        }
      ]
    }
  }
}
