// import { bind } from '@adonisjs/route-model-binding'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import WebSocketManager from 'App/Services/WebSocketManager'

export default class NotificationsController {
  public async index({ auth, request, response }: HttpContextContract) {
    const { per = 10, page = 1, order = 'id', sort = 'desc' } = request.qs()
    // const notificationsQuery = auth.user!.related('notifications').query()
    const notificationsQuery = Database.from('notifications')
      .where('notifiable_id', auth.user?.id!)
      .whereNull('read_at')

    const notifications = await notificationsQuery.orderBy(order, sort).paginate(page, per)

    // Send notifications via the new WebSocket system
    if (auth.user?.id) {
      WebSocketManager.sendToUser(auth.user.id, 'notification', {
        type: 'notifications_list',
        data: notifications,
        timestamp: new Date().toISOString(),
      })
    }

    return response.json(notifications)
  }

  public async store({ auth }: HttpContextContract) {
    const user = auth.user
    await user?.markNotificationsAsRead()
  }

  public async show({ response, params }: HttpContextContract) {
    const notification = await Database.from('notifications').where('id', params.id).first()

    return response.json(notification)
  }

  public async update({ auth, params, response }: HttpContextContract) {
    const user = auth.user
    const notification = await user?.related('notifications').query().where('id', params.id).first()

    if (notification) {
      await notification.markAsRead()
    }

    const notifications = await user?.unreadNotifications()

    return response.json(notifications)
  }

  public async destroy({ auth }: HttpContextContract) {
    const user = auth.user
    await user?.markNotificationsAsRead()
  }
}
