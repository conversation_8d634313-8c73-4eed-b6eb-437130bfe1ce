version: '3.8'

services:
  postgres:
    container_name: postgres
    image: postgres:15
    volumes:
      - aia_db:/var/lib/postgresql/data
      - ./dockerConfig/postgres-dev-init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - 5432:5432

  redis:
    container_name: redis
    image: redis:6-alpine
    volumes:
      - aia_cache:/data
    ports:
      - 6379:6379

  aia:
    container_name: aia
    restart: always
    depends_on:
      - postgres
      - redis
    build:
      context: .
      target: dependencies
    ports:
      - ${PORT}:${PORT}
      - 9229:9229
    env_file:
      - .env
    environment:
      - PG_HOST=postgres
      - REDIS_HOST=redis
    volumes:
      - ./:/app
    command: dumb-init node ace serve --watch --node-args="--inspect=0.0.0.0"

volumes:
  aia_db:
  aia_cache:
