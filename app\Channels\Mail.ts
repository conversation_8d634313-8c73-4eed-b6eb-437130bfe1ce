import { NotificationChannelContract } from '@ioc:Verful/Notification'
import { NotifiableModel } from '@ioc:Verful/Notification'
import Mail from '@ioc:Adonis/Addons/Mail'
import User from 'App/Models/User'
import NotificationTracking from 'App/Services/NotificationTracking'

export default class MailChannel implements NotificationChannelContract {
  public async send(notification: any, notifiable: NotifiableModel) {
    try {
      // Track notification attempt
      await NotificationTracking.track(
        notification,
        notifiable as User,
        'sent',
        'mail',
        { email: (notifiable as User).email }
      )

      // Send the email
      await Mail.send((message) => {
        notification.prepare(message)
      })

      // Track successful delivery
      await NotificationTracking.track(
        notification,
        notifiable as User,
        'delivered',
        'mail',
        { email: (notifiable as User).email }
      )
    } catch (error) {
      console.error('Failed to send email:', error)
      
      // Track failed delivery
      await NotificationTracking.track(
        notification,
        notifiable as User,
        'failed',
        'mail',
        { 
          email: (notifiable as User).email,
          error: error.message
        }
      )

      throw error
    }
  }
} 