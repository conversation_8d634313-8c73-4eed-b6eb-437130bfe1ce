import Database from '@ioc:Adonis/Lucid/Database'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Order from 'App/Models/Order'
import User from 'App/Models/User'

export interface AccessLevel {
  level: number
  name: string
  description: string
  canAccess: string[]
}

export interface HierarchicalAccess {
  userId: string
  accessLevel: AccessLevel
  supervisedUsers: string[]
  managedBranches: string[]
  managedVendors: string[]
  departmentIds: string[]
  branchId?: string
  vendorId?: string
}

export default class HierarchicalAccessControlService {
  /**
   * Define access levels in hierarchical order
   */
  private static readonly ACCESS_LEVELS: Record<string, AccessLevel> = {
    staff: {
      level: 1,
      name: 'Staff',
      description: 'Individual staff member - sees only assigned orders',
      canAccess: ['own_orders'],
    },
    supervisor: {
      level: 2,
      name: 'Supervisor',
      description: 'Department supervisor - sees department orders + own orders',
      canAccess: ['own_orders', 'department_orders', 'supervised_staff_orders'],
    },
    manager: {
      level: 3,
      name: 'Manager',
      description: 'Branch manager - sees all branch orders',
      canAccess: ['own_orders', 'department_orders', 'supervised_staff_orders', 'branch_orders'],
    },
    owner: {
      level: 4,
      name: 'Owner',
      description: 'Vendor owner/manager - sees all vendor orders',
      canAccess: [
        'own_orders',
        'department_orders',
        'supervised_staff_orders',
        'branch_orders',
        'vendor_orders',
      ],
    },
    admin: {
      level: 5,
      name: 'Admin',
      description: 'System administrator - sees all orders',
      canAccess: ['all_orders'],
    },
  }

  /**
   * Get hierarchical access information for a user with comprehensive error handling
   */
  public static async getUserHierarchicalAccess(user: User): Promise<HierarchicalAccess> {
    try {
      // Load user roles with error handling
      await user.load('roles')
      const userRoles = user.roles.map((role) => role.name.toLowerCase())

      // Check if user is admin
      if (userRoles.includes('admin')) {
        return {
          userId: user.id,
          accessLevel: this.ACCESS_LEVELS.admin,
          supervisedUsers: [],
          managedBranches: [],
          managedVendors: [],
          departmentIds: [],
        }
      }

      // Check if user is vendor with fallback handling
      if (userRoles.includes('vendor')) {
        try {
          const vendor = await Database.from('vendors').where('user_id', user.id).first()
          if (vendor) {
            return {
              userId: user.id,
              accessLevel: this.ACCESS_LEVELS.owner,
              supervisedUsers: [],
              managedBranches: [],
              managedVendors: [vendor.id],
              departmentIds: [],
              vendorId: vendor.id,
            }
          } else {
            // Vendor role but no vendor record - fallback to staff level
            console.warn(`User ${user.id} has vendor role but no vendor record found`)
          }
        } catch (vendorError) {
          console.error('Error fetching vendor record:', vendorError)
          // Continue to staff record check
        }
      }

      // Get staff record with hierarchical information
      const staffRecord = await Database.from('staff').where('user_id', user.id).first()

      if (!staffRecord) {
        // Check if user has any staff-related roles
        const staffRoles = ['waiter', 'chef', 'barman', 'cashier', 'rider']
        const hasStaffRole = userRoles.some((role) => staffRoles.includes(role))

        if (hasStaffRole) {
          throw new Error('Staff record not found for user with staff role')
        } else {
          // User has no staff role and no staff record - default to minimal access
          console.warn(
            `User ${user.id} has no staff record and no staff roles - providing minimal access`
          )
          return {
            userId: user.id,
            accessLevel: this.ACCESS_LEVELS.staff,
            supervisedUsers: [],
            managedBranches: [],
            managedVendors: [],
            departmentIds: [],
            branchId: undefined,
            vendorId: undefined,
          }
        }
      }

      // Determine access level based on staff record with validation
      let accessLevel = this.ACCESS_LEVELS.staff

      try {
        if (staffRecord.can_manage_vendor) {
          accessLevel = this.ACCESS_LEVELS.owner
        } else if (staffRecord.can_manage_branch) {
          accessLevel = this.ACCESS_LEVELS.manager
        } else if (staffRecord.can_supervise_department) {
          accessLevel = this.ACCESS_LEVELS.supervisor
        }
      } catch (accessLevelError) {
        console.warn('Error determining access level, defaulting to staff:', accessLevelError)
        accessLevel = this.ACCESS_LEVELS.staff
      }

      // Get supervised users (if any) with error handling
      let supervisedUsers: string[] = []
      try {
        supervisedUsers = await Database.from('staff')
          .where('supervisor_id', user.id)
          .pluck('user_id')
      } catch (supervisedError) {
        console.warn('Error fetching supervised users:', supervisedError)
        supervisedUsers = []
      }

      // Get managed departments with JSON parsing error handling
      let departmentIds: string[] = []
      try {
        departmentIds = staffRecord.supervised_departments
          ? JSON.parse(staffRecord.supervised_departments)
          : []
      } catch (jsonError) {
        console.warn('Error parsing supervised_departments JSON:', jsonError)
        departmentIds = []
      }

      // Get managed branches with JSON parsing error handling
      let managedBranches: string[] = []
      try {
        managedBranches = staffRecord.managed_branches
          ? JSON.parse(staffRecord.managed_branches)
          : []
      } catch (jsonError) {
        console.warn('Error parsing managed_branches JSON:', jsonError)
        managedBranches = []
      }

      return {
        userId: user.id,
        accessLevel,
        supervisedUsers,
        managedBranches,
        managedVendors: [],
        departmentIds,
        branchId: staffRecord.branch_id,
        vendorId: staffRecord.vendor_id,
      }
    } catch (error) {
      console.error('Critical error in getUserHierarchicalAccess:', error)

      // Fallback to minimal access in case of any critical error
      return {
        userId: user.id,
        accessLevel: this.ACCESS_LEVELS.staff,
        supervisedUsers: [],
        managedBranches: [],
        managedVendors: [],
        departmentIds: [],
        branchId: undefined,
        vendorId: undefined,
      }
    }
  }

  /**
   * Apply hierarchical filtering to an order query
   */
  public static async applyHierarchicalFilter(
    query: ModelQueryBuilderContract<typeof Order, Order>,
    user: User
  ): Promise<ModelQueryBuilderContract<typeof Order, Order>> {
    const access = await this.getUserHierarchicalAccess(user)

    switch (access.accessLevel.level) {
      case 5: // Admin - see all orders
        // No filtering needed
        break

      case 4: // Owner - see all vendor orders
        if (access.vendorId) {
          query.where('vendorId', access.vendorId)
        }
        break

      case 3: // Manager - see all branch orders
        if (access.managedBranches.length > 0) {
          query.whereIn('branchId', access.managedBranches)
        } else if (access.branchId) {
          query.where('branchId', access.branchId)
        }
        break

      case 2: // Supervisor - see department orders + supervised staff orders
        await this.applySupervisorFilter(query, access)
        break

      case 1: // Staff - see only own assigned orders
      default:
        query.where('staffId', user.id)
        break
    }

    return query
  }

  /**
   * Apply supervisor-specific filtering logic
   */
  private static async applySupervisorFilter(
    query: ModelQueryBuilderContract<typeof Order, Order>,
    access: HierarchicalAccess
  ): Promise<void> {
    const conditions: string[] = []
    const bindings: any[] = []

    // Own orders
    conditions.push('staff_id = ?')
    bindings.push(access.userId)

    // Supervised staff orders
    if (access.supervisedUsers.length > 0) {
      const placeholders = access.supervisedUsers.map(() => '?').join(',')
      conditions.push(`staff_id IN (${placeholders})`)
      bindings.push(...access.supervisedUsers)
    }

    // Department orders (orders assigned to items in supervised departments)
    if (access.departmentIds.length > 0) {
      const departmentPlaceholders = access.departmentIds.map(() => '?').join(',')
      conditions.push(`
        id IN (
          SELECT DISTINCT order_id
          FROM order_items
          WHERE department_id IN (${departmentPlaceholders})
        )
      `)
      bindings.push(...access.departmentIds)
    }

    if (conditions.length > 0) {
      query.whereRaw(`(${conditions.join(' OR ')})`, bindings)
    }
  }

  /**
   * Get accessible order statistics for a user
   */
  public static async getAccessibleOrderStats(user: User): Promise<{
    totalOrders: number
    accessLevel: string
    accessDescription: string
    canAccess: string[]
  }> {
    const access = await this.getUserHierarchicalAccess(user)

    // Build a count query with the same filtering
    const countQuery = Order.query()
    await this.applyHierarchicalFilter(countQuery, user)
    const totalOrders = await countQuery.count('* as total')

    return {
      totalOrders: Number(totalOrders[0].$extras.total),
      accessLevel: access.accessLevel.name,
      accessDescription: access.accessLevel.description,
      canAccess: access.accessLevel.canAccess,
    }
  }

  /**
   * Check if a user can access a specific order
   */
  public static async canAccessOrder(user: User, orderId: string): Promise<boolean> {
    try {
      const query = Order.query().where('id', orderId)
      await this.applyHierarchicalFilter(query, user)
      const order = await query.first()
      return !!order
    } catch (error) {
      return false
    }
  }

  /**
   * Get the reporting chain for a user
   */
  public static async getReportingChain(userId: string): Promise<
    {
      level: number
      userId: string
      name: string
      email: string
      accessLevel: string
    }[]
  > {
    const chain: any[] = []
    let currentUserId = userId
    let level = 0

    while (currentUserId && level < 10) {
      // Prevent infinite loops
      const staffRecord = await Database.from('staff')
        .join('users', 'staff.user_id', 'users.id')
        .where('staff.user_id', currentUserId)
        .select('staff.*', 'users.name', 'users.email')
        .first()

      if (!staffRecord) break

      chain.push({
        level,
        userId: currentUserId,
        name: staffRecord.name || 'Unknown',
        email: staffRecord.email,
        accessLevel: staffRecord.access_level || 'staff',
      })

      currentUserId = staffRecord.supervisor_id
      level++
    }

    return chain
  }

  /**
   * Specialized Query Builders for Each Role Level
   */

  /**
   * Staff Level Query Builder - Only assigned orders
   */
  public static buildStaffQuery(
    query: ModelQueryBuilderContract<typeof Order, Order>,
    userId: string
  ): ModelQueryBuilderContract<typeof Order, Order> {
    return query.where('staffId', userId)
  }

  /**
   * Supervisor Level Query Builder - Own + supervised staff + department orders
   */
  public static async buildSupervisorQuery(
    query: ModelQueryBuilderContract<typeof Order, Order>,
    access: HierarchicalAccess
  ): Promise<ModelQueryBuilderContract<typeof Order, Order>> {
    const conditions: string[] = []
    const bindings: any[] = []

    // Own orders
    conditions.push('staff_id = ?')
    bindings.push(access.userId)

    // Supervised staff orders
    if (access.supervisedUsers.length > 0) {
      const placeholders = access.supervisedUsers.map(() => '?').join(',')
      conditions.push(`staff_id IN (${placeholders})`)
      bindings.push(...access.supervisedUsers)
    }

    // Department orders - orders containing items from supervised departments
    if (access.departmentIds.length > 0) {
      const departmentPlaceholders = access.departmentIds.map(() => '?').join(',')
      conditions.push(`
        id IN (
          SELECT DISTINCT order_id
          FROM order_items
          WHERE department_id IN (${departmentPlaceholders})
        )
      `)
      bindings.push(...access.departmentIds)
    }

    // Unassigned orders in the same branch (supervisors can claim them)
    if (access.branchId) {
      conditions.push('(staff_id IS NULL AND branch_id = ?)')
      bindings.push(access.branchId)
    }

    if (conditions.length > 0) {
      query.whereRaw(`(${conditions.join(' OR ')})`, bindings)
    }

    return query
  }

  /**
   * Manager Level Query Builder - All branch orders
   */
  public static buildManagerQuery(
    query: ModelQueryBuilderContract<typeof Order, Order>,
    access: HierarchicalAccess
  ): ModelQueryBuilderContract<typeof Order, Order> {
    if (access.managedBranches.length > 0) {
      return query.whereIn('branchId', access.managedBranches)
    } else if (access.branchId) {
      return query.where('branchId', access.branchId)
    }
    return query
  }

  /**
   * Owner Level Query Builder - All vendor orders
   */
  public static buildOwnerQuery(
    query: ModelQueryBuilderContract<typeof Order, Order>,
    access: HierarchicalAccess
  ): ModelQueryBuilderContract<typeof Order, Order> {
    if (access.managedVendors.length > 0) {
      return query.whereIn('vendorId', access.managedVendors)
    } else if (access.vendorId) {
      return query.where('vendorId', access.vendorId)
    }
    return query
  }

  /**
   * Admin Level Query Builder - All orders (no filtering)
   */
  public static buildAdminQuery(
    query: ModelQueryBuilderContract<typeof Order, Order>
  ): ModelQueryBuilderContract<typeof Order, Order> {
    // No filtering for admin - they see everything
    return query
  }

  /**
   * Enhanced filtering with role-specific query builders and comprehensive error handling
   */
  public static async applyRoleBasedFilter(
    query: ModelQueryBuilderContract<typeof Order, Order>,
    user: User
  ): Promise<ModelQueryBuilderContract<typeof Order, Order>> {
    try {
      const access = await this.getUserHierarchicalAccess(user)

      switch (access.accessLevel.level) {
        case 5: // Admin
          return this.buildAdminQuery(query)

        case 4: // Owner
          return this.buildOwnerQuery(query, access)

        case 3: // Manager
          return this.buildManagerQuery(query, access)

        case 2: // Supervisor
          return await this.buildSupervisorQuery(query, access)

        case 1: // Staff
        default:
          return this.buildStaffQuery(query, user.id)
      }
    } catch (error) {
      console.error('Error in applyRoleBasedFilter, falling back to staff-level filtering:', error)
      // Fallback to staff-level filtering in case of any error
      return this.buildStaffQuery(query, user.id)
    }
  }

  /**
   * Validate hierarchical access configuration for a user
   */
  public static async validateUserAccess(user: User): Promise<{
    isValid: boolean
    issues: string[]
    recommendations: string[]
  }> {
    const issues: string[] = []
    const recommendations: string[] = []

    try {
      await user.load('roles')
      const userRoles = user.roles.map((role) => role.name.toLowerCase())

      // Check for role consistency
      if (userRoles.length === 0) {
        issues.push('User has no assigned roles')
        recommendations.push('Assign appropriate role to user')
      }

      // Check staff record consistency
      const staffRoles = ['waiter', 'chef', 'barman', 'cashier', 'rider']
      const hasStaffRole = userRoles.some((role) => staffRoles.includes(role))

      if (hasStaffRole) {
        const staffRecord = await Database.from('staff').where('user_id', user.id).first()
        if (!staffRecord) {
          issues.push('User has staff role but no staff record')
          recommendations.push('Create staff record for user')
        } else {
          // Check for orphaned supervisor references
          if (staffRecord.supervisor_id) {
            const supervisorExists = await Database.from('users')
              .where('id', staffRecord.supervisor_id)
              .first()
            if (!supervisorExists) {
              issues.push('Staff record references non-existent supervisor')
              recommendations.push('Update supervisor_id or remove invalid reference')
            }
          }

          // Check department assignments
          if (staffRecord.supervised_departments) {
            try {
              const deptIds = JSON.parse(staffRecord.supervised_departments)
              if (Array.isArray(deptIds) && deptIds.length > 0) {
                const validDepts = await Database.from('departments')
                  .whereIn('id', deptIds)
                  .pluck('id')
                if (validDepts.length !== deptIds.length) {
                  issues.push('Some supervised departments do not exist')
                  recommendations.push('Update supervised_departments with valid department IDs')
                }
              }
            } catch (jsonError) {
              issues.push('Invalid JSON in supervised_departments field')
              recommendations.push('Fix JSON format in supervised_departments')
            }
          }
        }
      }

      // Check vendor record consistency
      if (userRoles.includes('vendor')) {
        const vendorRecord = await Database.from('vendors').where('user_id', user.id).first()
        if (!vendorRecord) {
          issues.push('User has vendor role but no vendor record')
          recommendations.push('Create vendor record for user')
        }
      }

      return {
        isValid: issues.length === 0,
        issues,
        recommendations,
      }
    } catch (error) {
      return {
        isValid: false,
        issues: ['Error validating user access configuration'],
        recommendations: ['Check user data integrity and database connectivity'],
      }
    }
  }

  /**
   * Get filtered order count for a specific role level
   */
  public static async getOrderCountByRole(
    user: User,
    filters: any = {}
  ): Promise<{
    total: number
    byStatus: Record<string, number>
    byDeliveryType: Record<string, number>
    accessLevel: string
  }> {
    const access = await this.getUserHierarchicalAccess(user)

    // Build base query with filters
    const baseQuery = Order.filter(filters)
    await this.applyRoleBasedFilter(baseQuery, user)

    // Get total count
    const totalResult = await baseQuery.clone().count('* as total')
    const total = Number(totalResult[0].$extras.total)

    // Get count by status
    const statusCounts = await baseQuery
      .clone()
      .groupBy('status')
      .select('status')
      .count('* as count')

    const byStatus: Record<string, number> = {}
    statusCounts.forEach((row) => {
      byStatus[row.status] = Number(row.$extras.count)
    })

    // Get count by delivery type
    const deliveryCounts = await baseQuery
      .clone()
      .groupBy('delivery')
      .select('delivery')
      .count('* as count')

    const byDeliveryType: Record<string, number> = {}
    deliveryCounts.forEach((row) => {
      byDeliveryType[row.delivery] = Number(row.$extras.count)
    })

    return {
      total,
      byStatus,
      byDeliveryType,
      accessLevel: access.accessLevel.name,
    }
  }

  /**
   * Get orders accessible to user with advanced filtering options
   */
  public static async getAccessibleOrders(
    user: User,
    options: {
      page?: number
      perPage?: number
      orderBy?: string
      sortOrder?: 'asc' | 'desc'
      filters?: any
      includeStats?: boolean
    } = {}
  ): Promise<{
    orders: any
    stats?: any
    accessInfo: any
  }> {
    const {
      page = 1,
      perPage = 30,
      orderBy = 'createdAt',
      sortOrder = 'desc',
      filters = {},
      includeStats = false,
    } = options

    // Build query with role-based filtering
    const query = Order.filter(filters)
      .preload('customer')
      .preload('branch')
      .preload('vendor')
      .preload('items', (itemQuery) => {
        itemQuery.preload('product')
        itemQuery.preload('modifiers')
      })
      .preload('staff')
      .preload('section')
      .preload('invoices', (iq) => iq.preload('payments'))

    await this.applyRoleBasedFilter(query, user)

    // Execute query with pagination
    const orders = await query.orderBy(orderBy, sortOrder).paginate(page, perPage)

    // Get access information
    const accessStats = await this.getAccessibleOrderStats(user)

    // Get detailed stats if requested
    let stats = undefined
    if (includeStats) {
      stats = await this.getOrderCountByRole(user, filters)
    }

    return {
      orders,
      stats,
      accessInfo: {
        level: accessStats.accessLevel,
        description: accessStats.accessDescription,
        totalAccessible: accessStats.totalOrders,
        canAccess: accessStats.canAccess,
      },
    }
  }
}
