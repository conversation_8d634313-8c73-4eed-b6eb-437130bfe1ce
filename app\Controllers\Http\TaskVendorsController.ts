import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Task from '../../Models/Task'
import Vendor from '../../Models/Vendor'

export default class TaskVendorsController {
  @bind()
  /**
   * @index
   * @summary Show all vendors
   * @version 1.0.0
   * @description Vendor management for the application
   * @paramUse(filterable)
   * @paramPath taskId required number - Task ID
   */
  public async index({ request }: HttpContextContract, task: Task) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const vendorQuery = task.related('vendors').query().filter(filters)

    return await vendorQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()
  /**
   * @store
   * @summary Create a vendor
   * @description Create a vendor with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @paramPath taskId required number - Task ID
   */
  public async store({ request, response }: HttpContextContract, task: Task) {
    const { name, details } = request.all()
    const vendor = await task.related('vendors').create({
      name,
      details,
    })

    const image = request.file('image')

    if (image) {
      await vendor.merge({ logo: Attachment.fromFile(image) }).save()
    }

    return response.json(vendor)
  }

  @bind()

  /**
   * @show
   * @summary Show a single Vendor
   * @description Show a Vendor with their details (name and details)
   * @responseBody 200 - <Vendor>
   * @response 404 - Vendor not found
   */
  public async show({ response }: HttpContextContract, task: Task, vendor: Vendor) {
    return response.json({ ...vendor, task })
  }

  @bind()

  /**
   * @update
   * @summary Update a Vendor
   * @description Update a Tag with their details (name and details)
   * @requestBody <Vendor>
   * @responseBody 200 - <Vendor>
   * @response 404 - Vendor not found
   */
  public async update({ request, response }: HttpContextContract, task: Task, vendor: Vendor) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await vendor.merge(input).save()

    return response.json({ ...vendor, task })
  }

  @bind()

  /**
   * @destroy
   * @summary delete a Vendor
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, vendor: Vendor) {
    return await vendor.delete()
  }
}
