import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'vendors'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Delivery preferences
      table.json('delivery_preferences').nullable()
      table.json('availability_settings').nullable()
      table.json('pricing_structure').nullable()
      table.enum('verification_status', ['pending', 'verified', 'rejected']).defaultTo('pending')
      table.text('verification_notes').nullable()
      table.timestamp('verified_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('delivery_preferences')
      table.dropColumn('availability_settings')
      table.dropColumn('pricing_structure')
      table.dropColumn('verification_status')
      table.dropColumn('verification_notes')
      table.dropColumn('verified_at')
    })
  }
}
