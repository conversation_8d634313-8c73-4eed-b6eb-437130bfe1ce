import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import UserBillingConfig from 'App/Models/UserBillingConfig'

export default class extends BaseSeeder {
  public async run() {
    const billingConfigs = [
      {
        name: 'Pay As You Go',
        code: 'PAYG',
        description: 'Pay only for what you use with no monthly commitment',
        base_price: 0,
        currency: 'KES',
        billing_type: 'usage',
        is_active: true,
        meta: {
          usage_rates: {
            orders: {
              base_rate: 5, // KES per order
              volume_discounts: [
                { threshold: 100, rate: 4.5 },
                { threshold: 500, rate: 4 },
                { threshold: 1000, rate: 3.5 }
              ]
            },
            notifications: {
              sms: 1.50,
              email: 0.10,
              push: 0.05
            },
            api_calls: {
              base_rate: 0.50, // KES per 1000 API calls
              volume_discounts: [
                { threshold: 10000, rate: 0.45 },
                { threshold: 50000, rate: 0.40 },
                { threshold: 100000, rate: 0.35 }
              ]
            },
            storage: {
              base_rate: 10, // KES per GB per month
              volume_discounts: [
                { threshold: 10, rate: 9 },
                { threshold: 50, rate: 8 },
                { threshold: 100, rate: 7 }
              ]
            }
          },
          features: [
            'No monthly commitment',
            'Pay only for what you use',
            'Volume-based discounts',
            'Real-time usage tracking',
            'Flexible scaling'
          ]
        }
      },
      {
        name: 'Volume Discount',
        code: 'VOLUME',
        description: 'Commit to monthly volume for better rates',
        base_price: 0,
        currency: 'KES',
        billing_type: 'usage',
        is_active: true,
        meta: {
          usage_rates: {
            orders: {
              base_rate: 4, // KES per order
              volume_discounts: [
                { threshold: 1000, rate: 3.5 },
                { threshold: 5000, rate: 3 },
                { threshold: 10000, rate: 2.5 }
              ]
            },
            notifications: {
              sms: 1.25,
              email: 0.08,
              push: 0.04
            },
            api_calls: {
              base_rate: 0.40, // KES per 1000 API calls
              volume_discounts: [
                { threshold: 50000, rate: 0.35 },
                { threshold: 100000, rate: 0.30 },
                { threshold: 500000, rate: 0.25 }
              ]
            },
            storage: {
              base_rate: 8, // KES per GB per month
              volume_discounts: [
                { threshold: 50, rate: 7 },
                { threshold: 100, rate: 6 },
                { threshold: 500, rate: 5 }
              ]
            }
          },
          features: [
            'Volume-based pricing',
            'Lower rates with commitment',
            'Priority support',
            'Usage analytics',
            'Custom reporting'
          ]
        }
      },
      {
        name: 'Enterprise Usage',
        code: 'ENTERPRISE_USAGE',
        description: 'Custom usage-based pricing for large enterprises',
        base_price: 0,
        currency: 'KES',
        billing_type: 'usage',
        is_active: true,
        meta: {
          usage_rates: {
            orders: {
              base_rate: 3, // KES per order
              volume_discounts: [
                { threshold: 10000, rate: 2.5 },
                { threshold: 50000, rate: 2 },
                { threshold: 100000, rate: 1.5 }
              ]
            },
            notifications: {
              sms: 1.00,
              email: 0.05,
              push: 0.03
            },
            api_calls: {
              base_rate: 0.30, // KES per 1000 API calls
              volume_discounts: [
                { threshold: 100000, rate: 0.25 },
                { threshold: 500000, rate: 0.20 },
                { threshold: 1000000, rate: 0.15 }
              ]
            },
            storage: {
              base_rate: 6, // KES per GB per month
              volume_discounts: [
                { threshold: 100, rate: 5 },
                { threshold: 500, rate: 4 },
                { threshold: 1000, rate: 3 }
              ]
            }
          },
          features: [
            'Custom volume pricing',
            'Dedicated account manager',
            '24/7 priority support',
            'Advanced analytics',
            'Custom integrations',
            'SLA guarantees',
            'Usage forecasting',
            'Custom reporting'
          ]
        }
      }
    ]

    // Create billing configurations
    for (const config of billingConfigs) {
      await UserBillingConfig.create(config)
    }
  }
} 