import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { DateTime } from 'luxon'

interface Bill {
  id: string | number
  amount: number
  currency: string
  dueDate: Date
  description?: string
  isOverdue?: boolean
}

export default class PendingBillNotification implements NotificationContract {
  constructor(
    private bill: Bill,

  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    const dueDate = DateTime.fromJSDate(this.bill.dueDate)
    const now = DateTime.now()
    const daysUntilDue = Math.ceil(dueDate.diff(now, 'days').days)
    
    // Determine priority and message based on due date
    let priority = NotificationPriority.MEDIUM
    let urgencyMessage = ''
    
    if (this.bill.isOverdue || daysUntilDue < 0) {
      priority = NotificationPriority.HIGH
      urgencyMessage = ' - OVERDUE'
    } else if (daysUntilDue <= 1) {
      priority = NotificationPriority.HIGH
      urgencyMessage = ' - Due today'
    } else if (daysUntilDue <= 3) {
      priority = NotificationPriority.HIGH
      urgencyMessage = ` - Due in ${daysUntilDue} days`
    } else if (daysUntilDue <= 7) {
      urgencyMessage = ` - Due in ${daysUntilDue} days`
    }

    const title = `Payment Due${urgencyMessage}`
    const body = this.bill.isOverdue 
      ? `Your bill of ${this.bill.currency} ${this.bill.amount} is overdue. Pay now to avoid service interruption.`
      : `Your bill of ${this.bill.currency} ${this.bill.amount} is due ${daysUntilDue <= 1 ? 'today' : `in ${daysUntilDue} days`}. ${this.bill.description || ''}`

    return NotificationHelper.createNotificationData(
      title,
      body,
      NotificationHelper.createBillingActions(this.bill.id, 'bill'),
      {
        category: NotificationCategory.BILLING,
        notificationType: this.bill.isOverdue ? NotificationType.BILL_OVERDUE : NotificationType.PENDING_BILL,
        priority,
        entityId: this.bill.id,
        entityType: 'bill',
        amount: this.bill.amount,
        currency: this.bill.currency,
        dueDate: this.bill.dueDate.toISOString(),
        daysUntilDue,
        isOverdue: this.bill.isOverdue || false
      },
      'https://cdn.verful.com/icons/bill-icon.png'
    )
  }
}
