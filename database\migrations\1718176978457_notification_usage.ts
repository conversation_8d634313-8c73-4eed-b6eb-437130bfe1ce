import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'notification_usage'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      
      // Vendor and tier info
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.integer('tier_id').references('id').inTable('notification_billing_tiers').onDelete('SET NULL')
      
      // Notification details
      table.integer('notification_id').references('id').inTable('notifications').onDelete('SET NULL')
      table.string('notification_type').notNullable()
      table.string('channel').notNullable()
      table.string('recipient').notNullable()
      
      // Cost and status
      table.decimal('cost', 10, 2).notNullable()
      table.string('currency').notNullable().defaultTo('KES')
      table.enum('status', ['pending', 'billed', 'failed']).notNullable().defaultTo('pending')
      
      // Billing info
      table.timestamp('billed_at', { useTz: true }).nullable()
      table.string('billing_period').notNullable() // Format: YYYY-MM
      
      // Metadata
      table.jsonb('meta').nullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Indexes
      table.index(['vendor_id', 'billing_period'])
      table.index(['notification_id'])
      table.index(['status'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 