import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from '../../Models/Order'
import { OrderValidationHelper } from 'App/Helpers/OrderValidationHelper'
import OrderPricingService from 'App/Services/OrderPricingService'
import { Queue } from '@ioc:Rlanz/Queue'
import PackagingChargeService from 'App/Services/PackagingChargeService'

/**
 * UnifiedOrdersController
 * 
 * Handles direct order creation that bypasses the temp-order phase
 * while maintaining full backward compatibility with existing workflows.
 * 
 * This controller supports the new unified approach where orders can be
 * created directly with 'Placed' status, storing items in both the new
 * 'items' column and maintaining the legacy 'meta.temp_items' structure.
 */
export default class UnifiedOrdersController {
  /**
   * @store
   * @summary Create a Direct Order (bypasses temp-order phase)
   * @description Create an order directly with 'Placed' status, supporting both new and legacy item formats
   * @requestBody {"userId": "", "vendorId": "", "branchId": "", "sectionId": "", "lotId": "", "action": "", "type": "", "delivery": "", "items": {}, "meta": {}} - <DirectOrder>
   * @responseBody 200 - <Order>
   */
  public async store({ request, response, auth }: HttpContextContract) {
    try {
      const {
        userId,
        vendorId,
        branchId,
        sectionId,
        lotId,
        action = 'Purchase',
        type = 'Instant',
        delivery = 'Dinein',
        status = 'Placed', // Direct orders default to 'Placed'
        items = {},
        meta = {},
        staffId: postedStaffId,
        total,
      } = request.all()

      // Validate required fields
      if (!vendorId) {
        return response.badRequest({ error: 'Vendor ID is required' })
      }

      if (!branchId) {
        return response.badRequest({ error: 'Branch ID is required' })
      }

      // Validate items - support both formats
      let validatedItems = {}
      if (Array.isArray(items)) {
        // Direct order format: [{"productId": quantity}, ...]
        const validation = OrderValidationHelper.validateDirectOrderItems(items)
        if (!validation.isValid) {
          return OrderValidationHelper.createItemValidationErrorResponse(response, validation)
        }
        // Convert array format to object format
        validatedItems = items.reduce((acc, item) => {
          Object.keys(item).forEach(productId => {
            acc[productId] = item[productId]
          })
          return acc
        }, {})
      } else {
        // Temp-order format: {"productId": quantity, ...}
        const validation = OrderValidationHelper.validateTempOrderItems(items)
        if (!validation.isValid) {
          return OrderValidationHelper.createItemValidationErrorResponse(response, validation)
        }
        validatedItems = items
      }

      // Ensure we have items
      if (Object.keys(validatedItems).length === 0) {
        return response.badRequest({
          error: 'Order must contain at least one item',
          details: 'Cannot create an order without items'
        })
      }

      // Determine staff ID
      const staffId = postedStaffId || auth.user?.id

      // Prepare unified meta structure
      const unifiedMeta = {
        ...meta,
        // Store items in temp_items for backward compatibility
        temp_items: Object.keys(validatedItems).reduce(
          (acc, productId) => ({
            ...acc,
            [productId]: {
              quantity: typeof validatedItems[productId] === 'object' 
                ? validatedItems[productId].quantity 
                : validatedItems[productId],
            },
          }),
          {}
        ),
      }

      // Create order with unified approach
      console.log('🔄 Creating direct order...')
      const order = await Order.create({
        userId: userId || auth.user?.id,
        staffId,
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status, // Can be 'Placed' or other statuses
        items: validatedItems, // Store in new items column
        meta: unifiedMeta, // Include temp_items for backward compatibility
      })

      console.log('✅ Direct order created:', order.id)

      // If status is 'Placed', convert temp_items to OrderItems immediately
      if (status === 'Placed') {
        await order.convertTempItemsToOrderItems()
        console.log('✅ Items converted to OrderItems table')

        // Generate order number
        if (!order.orderNumber) {
          order.orderNumber = await this.generateOrderNumber()
          await order.save()
        }

        // Calculate pricing and create invoice
        await this.createInvoiceForOrder(order)
      } else {
        // For non-placed orders, ensure pricing is calculated
        await order.ensureTempOrderPricing()
      }

      // Load relationships for response
      await order.load('vendor')
      await order.load('branch')
      await order.load('section')
      await order.load('lot')
      await order.load('customer')
      await order.load('staff')

      if (status === 'Placed') {
        await order.load('items', (itemsQuery) => {
          itemsQuery.preload('product', (productQuery) => {
            productQuery.preload('gallery')
            productQuery.preload('modifiers')
          })
        })
        await order.load('invoices')
      }

      // Dispatch queue job for processing
      try {
        await Queue.dispatch('App/Jobs/ProcessOrder', {
          orderId: order.id,
        })
        console.log('✅ Queue job dispatched for direct order:', order.id)
      } catch (error) {
        console.warn('⚠️ Queue dispatch failed:', error.message)
      }

      return response.json(order)
    } catch (error) {
      console.error('❌ Direct order creation failed:', error)
      return response.badRequest({ error: error.message })
    }
  }

  /**
   * Generate order number
   */
  private async generateOrderNumber(): Promise<string> {
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')
    return `ORD-${timestamp}-${random}`
  }

  /**
   * Create invoice for placed order
   */
  private async createInvoiceForOrder(order: Order): Promise<void> {
    await order.load('items')
    let amount = 0

    // Calculate amount from OrderItems
    if (order.items && order.items.length > 0) {
      await Promise.all(
        order.items.map(async (item) => {
          await item.load('product')
          const price = Number(item.product?.price || 0)
          const quantity = Number(item.quantity || 0)
          amount += price * quantity
        })
      )
    }

    // Apply packaging charges automatically for applicable delivery types
    await PackagingChargeService.applyPackagingCharges(order)

    // Reload order to get updated meta with packaging charges
    await order.refresh()

    // Add charges from meta (including packaging charges)
    if (order.meta?.charges) {
      amount += Object.values(order.meta.charges as Record<string, number>).reduce(
        (acc, charge) => acc + Number(charge || 0),
        0
      )
    }

    // Create invoice
    await order.related('invoices').create({
      amount,
      status: 'Pending',
    })

    console.log('✅ Invoice created for direct order:', order.id, 'Amount:', amount)
  }
}
