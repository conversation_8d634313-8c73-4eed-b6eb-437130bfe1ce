import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'
import { DateTime } from 'luxon'

/**
 * @name Lots management
 * @version 1.0.0
 * @description Lots management for the application
 */
export default class UserLotsController {
  /**
   * @index
   * @summary List all lots
   * @description List all lots, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, user: User) {
    const {
      per = 10,
      page = 1,
      order = 'createdAt',
      sort = 'asc',
      startAt = null,
      endAt = null,
      ...filters
    } = request.qs()
    const lotsQuery = user.related('lots').query().filter(filters)

    if (startAt) {
      lotsQuery.where('startAt', '>=', DateTime.fromISO(startAt).toISO())
    }

    if (endAt) {
      lotsQuery.where('endAt', '<=', DateTime.fromISO(endAt).toISO())
    }

    return await lotsQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a lots
   * @description Create a lots with their details (name and details)
   * @requestBody {"name": "", "details": "", "location": {"name": "", "lots": "", "regions": {"country": ""}, "coordinates": {"lat": 0, "lng": 0}, "place_id": ""}, "phone": ""}
   * @responseBody 200 - <Lots>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, user: User) {
    const { lots, startAt, endAt } = request.all()

    await user.related('lots').sync(
      lots.reduce(
        (acc: Record<string, unknown>, lot: string) => ({
          ...acc,
          [lot]: {
            start_at: DateTime.fromISO(startAt).toISO(),
            end_at: DateTime.fromISO(endAt).toISO(),
          },
        }),
        {}
      ) as any
    )

    return response.json(lots)
  }
}
