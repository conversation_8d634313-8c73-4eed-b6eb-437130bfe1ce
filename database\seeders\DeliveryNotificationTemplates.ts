import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Database from '@ioc:Adonis/Lucid/Database'

export default class DeliveryNotificationTemplatesSeeder extends BaseSeeder {
  public async run() {
    const templates = [
      {
        type: 'delivery',
        name: 'order_assigned',
        subject: 'New Delivery Assignment',
        body: 'You have been assigned a new delivery order #{{orderNumber}}. Please review the details and confirm acceptance.',
        variables: JSON.stringify(['orderNumber', 'pickupLocation', 'deliveryLocation', 'estimatedDeliveryTime']),
        channels: JSON.stringify(['email', 'sms', 'push']),
        is_active: true,
      },
      {
        type: 'delivery',
        name: 'order_picked_up',
        subject: 'Order Picked Up',
        body: 'Your order #{{orderNumber}} has been picked up and is on its way. Estimated delivery time: {{estimatedDeliveryTime}} minutes.',
        variables: JSON.stringify(['orderNumber', 'estimatedDeliveryTime', 'trackingUrl']),
        channels: JSON.stringify(['email', 'sms', 'push']),
        is_active: true,
      },
      {
        type: 'delivery',
        name: 'order_in_transit',
        subject: 'Order In Transit',
        body: 'Your order #{{orderNumber}} is currently being delivered. Track your order here: {{trackingUrl}}',
        variables: JSON.stringify(['orderNumber', 'trackingUrl', 'currentLocation']),
        channels: JSON.stringify(['email', 'sms', 'push']),
        is_active: true,
      },
      {
        type: 'delivery',
        name: 'order_delivered',
        subject: 'Order Delivered',
        body: 'Your order #{{orderNumber}} has been delivered. Thank you for using our service!',
        variables: JSON.stringify(['orderNumber', 'deliveryTime', 'signatureImage']),
        channels: JSON.stringify(['email', 'sms', 'push']),
        is_active: true,
      },
      {
        type: 'delivery',
        name: 'order_failed',
        subject: 'Delivery Failed',
        body: 'We were unable to deliver your order #{{orderNumber}}. Reason: {{failureReason}}. Please contact support for assistance.',
        variables: JSON.stringify(['orderNumber', 'failureReason', 'supportContact']),
        channels: JSON.stringify(['email', 'sms', 'push']),
        is_active: true,
      },
      {
        type: 'delivery',
        name: 'order_cancelled',
        subject: 'Delivery Cancelled',
        body: 'Your delivery for order #{{orderNumber}} has been cancelled. Reason: {{cancellationReason}}',
        variables: JSON.stringify(['orderNumber', 'cancellationReason']),
        channels: JSON.stringify(['email', 'sms', 'push']),
        is_active: true,
      },
    ]

    // Insert templates in a transaction
    const trx = await Database.transaction()

    try {
      for (const template of templates) {
        const exists = await trx
          .from('notification_templates')
          .where('type', template.type)
          .where('name', template.name)
          .first()

        if (!exists) {
          await trx.table('notification_templates').insert(template)
        }
      }

      await trx.commit()
    } catch (error) {
      await trx.rollback()
      throw error
    }
  }
} 