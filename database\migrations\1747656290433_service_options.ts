import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'service_options'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE').nullable().index()
      table.string('name').notNullable()
      table.enum('type', [
        'duration',
        'location', 
        'personnel',
        'equipment',
        'delivery_method',
        'expertise_level',
        'add_on',
        'scheduling',
        'custom'
      ]).notNullable().index()
      table.text('description').nullable()
      table.decimal('default_price_adjustment', 10, 2).notNullable().defaultTo(0.00)
      
      // Calendar integration
      table.string('duration_id').references('id').inTable('durations').onDelete('SET NULL').nullable().index()
      
      // Option properties
      table.json('constraints').notNullable().defaultTo('{}')
      table.boolean('active').notNullable().defaultTo(true).index()
      
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index(['vendor_id', 'type', 'active'])
      table.index(['type', 'active'])
      table.index(['name', 'active'])
      
      // Unique constraint for name per vendor (null vendor_id means global)
      table.unique(['name', 'vendor_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
