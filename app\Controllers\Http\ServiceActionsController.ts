import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'
import ServiceAction from 'App/Models/ServiceAction'

export default class ServiceActionsController {
  @bind()
  public async index({ request }: HttpContextContract, service: Service) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const actionsQuery = service.related('actions').query()

    // Apply basic filters manually
    if (filters.action) {
      actionsQuery.where('action', filters.action)
    }
    if (filters.isActive !== undefined) {
      actionsQuery.where('isActive', filters.isActive)
    }

    return await actionsQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()
  public async store({ request, response }: HttpContextContract, service: Service) {
    const { action, config = null } = request.all()
    
    // Check if action already exists
    const existingAction = await service.related('actions')
      .query()
      .where('action', action)
      .first()
    
    if (existingAction) {
      return response.badRequest({ 
        error: 'Action already exists for this service' 
      })
    }

    const serviceAction = await service.related('actions').create({
      action,
      config,
      isActive: true
    })

    return response.created(serviceAction)
  }

  @bind()
  public async update({ request, response }: HttpContextContract, _service: Service, action: ServiceAction) {
    const { config, isActive } = request.all()
    
    action.merge({ config, isActive })
    await action.save()
    
    return response.ok(action)
  }

  @bind()
  public async destroy({ response }: HttpContextContract, _service: Service, action: ServiceAction) {
    await action.delete()
    return response.noContent()
  }
} 