import { NotificationContract } from '@ioc:Verful/Notification'
// Updated: Use unified Order model instead of TempOrder
import Order from 'App/Models/Order'
import User from 'App/Models/User'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

export default class CustomerNewTempOrder implements NotificationContract {
  constructor(
    // Updated: Accept unified Order model (will be temp order with status='Pending')
    private order: Order,
    public message: string = 'Hello {firstName}. A request has been sent to {vendor}. Status: {status}. Please wait for a response.',
    public title: string = 'Order Request Sent'
  ) {
    // Validate that this is actually a temp order
    if (order.status !== 'Pending') {
      console.warn(
        'CustomerNewTempOrder notification received non-temp order:',
        order.id,
        order.status
      )
    }
  }

  public via(_notifiable) {
    return ['database' as const, 'fcm' as const]
  }

  public toDatabase() {
    const personalizedMessage = this.message
      .replace('{firstName}', this.order.customer.firstName)
      .replace('{status}', this.order.status)
      .replace('{vendor}', this.order.vendor.name)

    return NotificationHelper.createNotificationData(
      this.title,
      personalizedMessage,
      NotificationHelper.createTempOrderActions(this.order.id, 'customer'),
      {
        category: NotificationCategory.ORDER,
        notificationType: NotificationType.TEMP_ORDER_CREATED,
        priority: NotificationPriority.MEDIUM,
        entityId: this.order.id,
        entityType: 'temp_order',
        orderStatus: this.order.status,
        vendorId: this.order.vendor?.id,
        vendorName: this.order.vendor?.name,
        customerId: this.order.customer?.id,
        requestDate: this.order.createdAt?.toISO(),
      },
      'https://cdn.verful.com/icons/temp-order-icon.png'
    )
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const body = this.message
      .replace('{firstName}', notifiable.firstName)
      .replace('{status}', this.order.status)
      .replace('{vendor}', this.order.vendor.name)

    return {
      title: this.title,
      body,
      // url: `aiauser://temp-orders/${this.order.id}`,
      // icon: 'https://cdn.verful.com/icons/verful-512x512.png',
    }
  }
}
