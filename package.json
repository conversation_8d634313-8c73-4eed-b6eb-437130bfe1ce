{"name": "api", "version": "1.0.0", "private": true, "scripts": {"dev": "export DEBUG=\"grammy*\" && node ace serve --watch", "build": "node ace docs:generate && node ace build --production && cp .env.production dist/.env && cp swagger.yml dist/ && cp ecosystem.config.js dist/", "start": "node server.js", "startp": "node ace migration:run --force && node server.js", "startr": "node ace migration:fresh --force --seed && node server.js", "test": "node ace test", "lint": "eslint . --ext=.ts", "format": "prettier --write ."}, "eslintConfig": {"extends": ["plugin:adonis/typescriptApp", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": ["error"]}}, "eslintIgnore": ["build"], "prettier": {"trailingComma": "es5", "semi": false, "singleQuote": true, "useTabs": false, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100}, "devDependencies": {"@adonisjs/assembler": "^5.9.6", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^3.0.5", "@types/node": "18.18.0", "@types/proxy-addr": "^2.0.3", "@types/source-map-support": "^0.5.8", "adonis-preset-ts": "^2.1.0", "bun-types": "^1.2.13", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-adonis": "^2.1.1", "eslint-plugin-prettier": "^5.0.1", "pino-pretty": "^10.2.3", "prettier": "^3.1.0", "typescript": "~4.6.4", "youch": "^3.3.3", "youch-terminal": "^2.2.3"}, "dependencies": {"@adonisjs/ally": "^4.1.5", "@adonisjs/attachment-lite": "^1.0.8", "@adonisjs/auth": "^8.2.3", "@adonisjs/core": "^5.9.0", "@adonisjs/drive-s3": "^1.3.3", "@adonisjs/http-server": "^7.6.0", "@adonisjs/i18n": "^1.6.0", "@adonisjs/lucid": "^18.4.2", "@adonisjs/mail": "^8.2.1", "@adonisjs/redis": "^7.3.4", "@adonisjs/repl": "^3.1.11", "@adonisjs/route-model-binding": "^1.0.1", "@adonisjs/view": "^6.2.0", "@bitkidd/adonis-ally-apple": "^1.1.0", "@grammyjs/conversations": "^1.2.0", "@japa/api-client": "^2.0.1", "@melchyore/adonis-lucid-observer": "^1.0.2", "@michpl/telegram-calendar": "^1.1.0", "@osenco/mpesa": "^0.1.0", "@rlanz/bull-queue": "1.1.0", "@trpc/server": "^10.43.4", "@types/qrcode": "^1.5.5", "@verful/notifications": "^2.3.0", "@verful/permissions": "^1.0.0", "adonis-autoswagger": "3.26.0", "adonis-fcm": "^1.0.1", "adonis-lucid-filter": "^4.1.1", "adonis-lucid-soft-deletes": "^1.4.4", "adonisjs-scheduler": "^0.0.25", "axios": "^1.6.8", "bwip-js": "^4.3.2", "csv-parser": "^3.2.0", "execa": "^8.0.1", "expo-camera": "^14.1.2", "firebase-admin": "^11.11.1", "google-auth-library": "^9.15.1", "grammy": "^1.22.4", "knex-postgis": "^0.14.3", "luxon": "^3.4.4", "mpesa-node": "^0.1.3", "mysql2": "^3.9.2", "pg": "^8.11.3", "proxy-addr": "^2.0.7", "qrcode": "^1.5.3", "reflect-metadata": "^0.1.13", "socket.io": "^4.7.2", "source-map-support": "^0.5.21", "sybase": "^1.2.3", "ulidx": "^2.2.1", "uuid": "^9.0.1"}, "engines": {"node": ">=21.0.0"}}