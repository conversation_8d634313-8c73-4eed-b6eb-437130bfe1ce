import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'cart'

  public async up() {
    // Create sequence if it doesn't exist
    await this.raw('CREATE SEQUENCE IF NOT EXISTS cart_id_seq')

    // Create table using raw SQL with IF NOT EXISTS
    await this.raw(`
      CREATE TABLE IF NOT EXISTS ${this.tableName} (
        id integer NOT NULL DEFAULT nextval('cart_id_seq'::regclass),
        user_id varchar(255) NOT NULL,
        cart_items text NOT NULL,
        total varchar(50) NOT NULL,
        CONSTRAINT cart_pkey PRIMARY KEY (id)
      )
    `)
  }

  public async down() {
    // WARNING: Uncommenting the below lines will permanently delete the cart table and all its data
    // Make sure to backup any important data before rolling back this migration
    
    // await this.raw(`DROP TABLE IF EXISTS ${this.tableName}`)
    // await this.raw('DROP SEQUENCE IF EXISTS cart_id_seq')
    
    // Instead, we'll do nothing in the down method to preserve data
  }
} 