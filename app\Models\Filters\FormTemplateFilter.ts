import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import FormTemplate from '../FormTemplate'

export default class FormTemplateFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof FormTemplate, FormTemplate>

  public active(value: boolean): void {
    this.$query.where('active', value)
  }

  public s(value: any): void {
    this.$query.whereILike('name', value)
  }
}
