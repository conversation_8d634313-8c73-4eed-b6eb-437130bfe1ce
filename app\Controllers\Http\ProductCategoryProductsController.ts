import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import ProductCategory from 'App/Models/ProductCategory'

/**
 * @name Product management
 * @version 1.0.0
 * @description Product management for the application
 */
export default class ProductCategoryProductsController {
  /**
   * @index
   * @summary List all products
   * @description List all products, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request, response }: HttpContextContract, category: ProductCategory) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const productQuery = category.related('products').query().preload('vendor').filter(filters)
    const products = await productQuery.orderBy(order, sort).paginate(page, per)

    return response.json(products)
  }

  /**
   * @store
   * @summary Create a product
   * @description Create a product with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Product>
   */
  public async store({ request, response }: HttpContextContract, category: ProductCategory) {
    const { name, details, serviceId } = request.all()
    const product = await category
      .related('products')
      .create({ name, details, serviceId, productCategoryId: category.id })

    const image = request.file('image')

    if (image) {
      await product.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(product)
  }
}
