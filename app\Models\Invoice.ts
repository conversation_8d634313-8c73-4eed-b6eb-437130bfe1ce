import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  HasMany,
  beforeCreate,
  belongsTo,
  column,
  hasMany,
} from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import Order from './Order'
import Payment from './Payment'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import InvoiceFilter from './Filters/InvoiceFilter'

export default class Invoice extends compose(BaseModel, Filterable) {
  public static $filter = () => InvoiceFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public orderId: string

  @column()
  public number: string

  @column()
  public amount: number

  @column()
  public status: 'Pending' | 'Paid' | 'Failed' | 'Cancelled'

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(invoice: Invoice) {
    invoice.id = ulid().toLowerCase()
    invoice.number = 'AIA-' + ulid().slice(0, 6) + '-' + ulid().slice(21, 26)
  }

  @belongsTo(() => Order)
  public order: BelongsTo<typeof Order>

  @hasMany(() => Payment)
  public payments: HasMany<typeof Payment>
}
