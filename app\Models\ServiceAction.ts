import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import Service from './Service'
import { ulid } from 'ulidx'

export default class ServiceAction extends BaseModel {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public serviceId: string

  @column()
  public action: string

  @column()
  public config: Record<string, any> | null

  @column()
  public isActive: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Service)
  public service: BelongsTo<typeof Service>

  @beforeCreate()
  public static async generateUlid(serviceAction: ServiceAction) {
    serviceAction.id = ulid().toLowerCase()
  }
} 