import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'notification_templates'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('type').notNullable()
      table.string('name').notNullable()
      table.string('subject').notNullable()
      table.text('body').notNullable()
      table.json('variables').nullable()
      table.json('channels').notNullable()
      table.boolean('is_active').defaultTo(true)
      table.json('meta').nullable()
      table.index('type')
      table.index('name')
      table.unique(['type', 'name'])
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 