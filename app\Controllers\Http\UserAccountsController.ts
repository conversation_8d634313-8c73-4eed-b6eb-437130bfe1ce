import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'

/**
 * @name Account management
 * @version 1.0.0
 * @description Account management for the application
 */
export default class AccountsController {
  /**
   * @index
   * @summary List all accounts
   * @description List all accounts, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, user: User) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const accountQuery = user.related('accounts').query().filter(filters)

    return await accountQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a account
   * @description Create a account with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Account>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, user: User) {
    const { providerId, provider, active } = request.all()
    const account = await user.related('accounts').create({ providerId, provider, active })

    return response.json(account)
  }
}
