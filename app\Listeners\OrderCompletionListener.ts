import { EventsList } from '@ioc:Adonis/Core/Event'
import User from '../Models/User'
import Order from '../Models/Order'
import OrderItem from '../Models/OrderItem'
import OrderItemModifier from '../Models/OrderItemModifier'
import { DateTime } from 'luxon'
import StatusChangeBroadcaster from '../Services/StatusChangeBroadcaster'
import OrderCompletionDetector from '../Services/OrderCompletionDetector'
import RealTimeNotificationService from '../Services/RealTimeNotificationService'

/**
 * Event listener for order completion events
 */
export default class OrderCompletionListener {

  /**
   * Handle order completion event
   */
  public async onOrderCompleted(data: EventsList['order:completed']) {
    const { order, completionTime, analysis } = data

    try {
      console.log(`Order ${order.id} completed at ${completionTime.toISO()}`)

      // Update order metadata with completion analytics
      const meta = order.meta || {}
      meta.completion_analytics = {
        completed_at: completionTime.toISO(),
        total_items: analysis.totalItems,
        department_breakdown: analysis.departmentBreakdown,
        completion_duration: completionTime.diff(order.createdAt, 'minutes').minutes
      }
      order.meta = meta
      await order.save()

      // Send completion notification
      await RealTimeNotificationService.notifyOrderCompletion(order, analysis)

      // Log for analytics and reporting
      this.logOrderCompletion(order, analysis)

    } catch (error) {
      console.error('Error handling order completion event:', error)
    }
  }

  /**
   * Handle customer notification for order ready
   */
  public async onCustomerOrderReady(data: EventsList['customer:order:ready']) {
    const { orderId, customerId, orderType, delivery, estimatedReadyTime } = data

    try {
      const customer = await User.find(customerId)
      if (!customer) {
        console.warn(`Customer ${customerId} not found for order ${orderId}`)
        return
      }

      const order = await Order.find(orderId)
      if (!order) {
        console.warn(`Order ${orderId} not found for customer notification`)
        return
      }

      // Send customer notification based on delivery type
      if (delivery === 'Pickup') {
        await this.sendPickupReadyNotification(customer, order)
      } else if (delivery === 'Delivery') {
        await this.sendDeliveryReadyNotification(customer, order)
      } else if (delivery === 'Dine-in') {
        await this.sendDineInReadyNotification(customer, order)
      }

      // Update customer notification history
      await this.updateCustomerNotificationHistory(customer, order, 'order_ready')

    } catch (error) {
      console.error('Error sending customer order ready notification:', error)
    }
  }

  /**
   * Handle staff notification for order ready
   */
  public async onStaffOrderReady(data: EventsList['staff:order:ready']) {
    const { orderId, vendorId, branchId, orderType, delivery } = data

    try {
      const order = await Order.find(orderId)
      if (!order) {
        console.warn(`Order ${orderId} not found for staff notification`)
        return
      }

      // Get relevant staff members
      const staffMembers = await this.getRelevantStaff(vendorId, branchId, delivery)

      // Send notifications to relevant staff
      for (const staff of staffMembers) {
        await this.sendStaffOrderReadyNotification(staff, order)
      }

      // Update order with notification status
      const meta = order.meta || {}
      meta.staff_notified_at = DateTime.now().toISO()
      meta.notified_staff_count = staffMembers.length
      order.meta = meta
      await order.save()

    } catch (error) {
      console.error('Error sending staff order ready notification:', error)
    }
  }

  /**
   * Send pickup ready notification to customer
   */
  private async sendPickupReadyNotification(customer: User, order: Order): Promise<void> {
    // This would integrate with your notification system (SMS, push, email)
    console.log(`Sending pickup ready notification to ${customer.name} for order ${order.id}`)

    // Example notification content
    const message = `Hi ${customer.name}! Your order #${order.id} is ready for pickup. Please come to the restaurant when convenient.`

    // Here you would call your notification service
    // await NotificationService.sendSMS(customer.phone, message)
    // await NotificationService.sendPushNotification(customer.id, 'Order Ready', message)

    console.log('Pickup notification sent:', message)
  }

  /**
   * Send delivery ready notification to customer
   */
  private async sendDeliveryReadyNotification(customer: User, order: Order): Promise<void> {
    console.log(`Sending delivery ready notification to ${customer.name} for order ${order.id}`)

    const message = `Hi ${customer.name}! Your order #${order.id} is ready and will be delivered shortly. Estimated delivery time: 15-30 minutes.`

    // Here you would call your notification service
    console.log('Delivery notification sent:', message)
  }

  /**
   * Send dine-in ready notification to customer
   */
  private async sendDineInReadyNotification(customer: User, order: Order): Promise<void> {
    console.log(`Sending dine-in ready notification to ${customer.name} for order ${order.id}`)

    const tableNumber = order.meta?.table_number
    const message = tableNumber
      ? `Hi ${customer.name}! Your order #${order.id} is ready and will be served to table ${tableNumber}.`
      : `Hi ${customer.name}! Your order #${order.id} is ready for serving.`

    // Here you would call your notification service
    console.log('Dine-in notification sent:', message)
  }

  /**
   * Send order ready notification to staff
   */
  private async sendStaffOrderReadyNotification(staff: User, order: Order): Promise<void> {
    console.log(`Sending order ready notification to staff ${staff.name} for order ${order.id}`)

    const delivery = order.delivery
    let message = `Order #${order.id} is ready for ${delivery.toLowerCase()}.`

    if (delivery === 'Pickup') {
      message += ' Customer can be notified for pickup.'
    } else if (delivery === 'Delivery') {
      message += ' Ready for delivery assignment.'
    } else if (delivery === 'Dine-in') {
      const tableNumber = order.meta?.table_number
      message += tableNumber ? ` Serve to table ${tableNumber}.` : ' Ready for serving.'
    }

    // Here you would call your staff notification service
    console.log('Staff notification sent:', message)
  }

  /**
   * Get relevant staff members for order ready notifications
   */
  private async getRelevantStaff(vendorId: string, branchId: string, delivery: string): Promise<User[]> {
    let roleFilter = []

    // Determine which staff roles should be notified based on delivery type
    switch (delivery) {
      case 'Pickup':
        roleFilter = ['manager', 'cashier', 'front_of_house']
        break
      case 'Delivery':
        roleFilter = ['manager', 'delivery_coordinator', 'delivery_driver']
        break
      case 'Dine-in':
        roleFilter = ['manager', 'waiter', 'server', 'front_of_house']
        break
      default:
        roleFilter = ['manager']
    }

    // Get staff members with relevant roles
    const staff = await User.query()
      .whereHas('employers', (employerQuery) => {
        employerQuery.where('vendor_id', vendorId)
        if (branchId) {
          employerQuery.where('branch_id', branchId)
        }
      })
      .whereHas('roles', (roleQuery) => {
        roleQuery.whereIn('name', roleFilter)
      })
      .where('active', true)
      .exec()

    return staff
  }

  /**
   * Update customer notification history
   */
  private async updateCustomerNotificationHistory(customer: User, order: Order, notificationType: string): Promise<void> {
    // This would update a notification history table or customer metadata
    console.log(`Updated notification history for customer ${customer.id}, order ${order.id}, type: ${notificationType}`)

    // Example: Update customer metadata with notification history
    const meta = customer.meta || {}
    if (!meta.notification_history) {
      meta.notification_history = []
    }

    meta.notification_history.push({
      order_id: order.id,
      type: notificationType,
      sent_at: DateTime.now().toISO(),
      delivery_type: order.delivery
    })

    // Keep only last 50 notifications
    if (meta.notification_history.length > 50) {
      meta.notification_history = meta.notification_history.slice(-50)
    }

    customer.meta = meta
    await customer.save()
  }

  /**
   * Log order completion for analytics
   */
  private logOrderCompletion(order: Order, analysis: any): void {
    const logData = {
      order_id: order.id,
      vendor_id: order.vendorId,
      branch_id: order.branchId,
      order_type: order.type,
      delivery_type: order.delivery,
      completion_time: DateTime.now().toISO(),
      total_items: analysis.totalItems,
      department_breakdown: analysis.departmentBreakdown,
      completion_duration_minutes: DateTime.now().diff(order.createdAt, 'minutes').minutes
    }

    // This would typically go to your analytics/logging service
    console.log('Order completion analytics:', JSON.stringify(logData, null, 2))

    // Example: Send to analytics service
    // AnalyticsService.track('order_completed', logData)
  }

  /**
   * Handle order status change event
   */
  public async onOrderStatusChanged(data: EventsList['order:status:changed']) {
    const { order, previousStatus, updatedBy, metadata, context } = data

    try {
      console.log(`Order ${order.id} status changed: ${previousStatus} → ${order.status}`)

      // Update order metadata with status change history
      const meta = order.meta || {}
      if (!meta.status_history) {
        meta.status_history = []
      }

      meta.status_history.push({
        from: previousStatus,
        to: order.status,
        updated_by: updatedBy,
        updated_at: DateTime.now().toISO(),
        metadata
      })

      // Keep only last 20 status changes
      if (meta.status_history.length > 20) {
        meta.status_history = meta.status_history.slice(-20)
      }

      order.meta = meta
      await order.save()

      // Send order status change notification
      await RealTimeNotificationService.notifyOrderStatusChange(order, previousStatus, updatedBy)

      // Trigger department workload update if needed
      if (context.departmentId) {
        await StatusChangeBroadcaster.broadcastDepartmentWorkloadUpdate(
          context.departmentId,
          context.vendorId,
          context.branchId
        )
      }

    } catch (error) {
      console.error('Error handling order status change event:', error)
    }
  }

  /**
   * Handle item status change event
   */
  public async onItemStatusChanged(data: EventsList['item:status:changed']) {
    const { orderItem, previousStatus, updatedBy, metadata, context } = data

    try {
      console.log(`Item ${orderItem.id} status changed: ${previousStatus} → ${orderItem.status}`)

      // Update item metadata with status change history
      const meta = orderItem.meta || {}
      if (!meta.status_history) {
        meta.status_history = []
      }

      meta.status_history.push({
        from: previousStatus,
        to: orderItem.status,
        updated_by: updatedBy,
        updated_at: DateTime.now().toISO(),
        metadata
      })

      orderItem.meta = meta
      await orderItem.save()

      // Send item status change notification
      await RealTimeNotificationService.notifyItemStatusChange(orderItem, previousStatus, updatedBy)

      // Check for order completion when item becomes ready
      if (['ready', 'served'].includes(orderItem.status)) {
        await OrderCompletionDetector.checkItemCompletion(orderItem.id)
      }

      // Trigger department workload update
      if (context.departmentId) {
        await StatusChangeBroadcaster.broadcastDepartmentWorkloadUpdate(
          context.departmentId,
          context.vendorId,
          context.branchId
        )
      }

    } catch (error) {
      console.error('Error handling item status change event:', error)
    }
  }

  /**
   * Handle modifier status change event
   */
  public async onModifierStatusChanged(data: EventsList['modifier:status:changed']) {
    const { modifier, previousStatus, updatedBy, metadata, context } = data

    try {
      console.log(`Modifier ${modifier.id} status changed: ${previousStatus} → ${modifier.status}`)

      // Update modifier metadata with status change history
      const meta = modifier.meta || {}
      if (!meta.status_history) {
        meta.status_history = []
      }

      meta.status_history.push({
        from: previousStatus,
        to: modifier.status,
        updated_by: updatedBy,
        updated_at: DateTime.now().toISO(),
        metadata
      })

      modifier.meta = meta
      await modifier.save()

      // Check for item completion when modifier is completed
      if (['completed', 'skipped'].includes(modifier.status)) {
        await OrderCompletionDetector.checkModifierCompletion(modifier.id)
      }

    } catch (error) {
      console.error('Error handling modifier status change event:', error)
    }
  }

  /**
   * Handle item overdue event
   */
  public async onItemOverdue(data: EventsList['item:overdue']) {
    const { orderItem, overdueMinutes, priorityLevel, context } = data

    try {
      console.log(`Item ${orderItem.id} is overdue by ${overdueMinutes} minutes (${priorityLevel} priority)`)

      // Update item with overdue flag
      const meta = orderItem.meta || {}
      meta.overdue_alert = {
        detected_at: DateTime.now().toISO(),
        overdue_minutes: overdueMinutes,
        priority_level: priorityLevel,
        alert_count: (meta.overdue_alert?.alert_count || 0) + 1
      }

      orderItem.meta = meta
      await orderItem.save()

      // Send overdue notification
      await RealTimeNotificationService.notifyOverdueItem(orderItem, overdueMinutes, priorityLevel)

      // Log for analytics
      console.log('Overdue item analytics:', {
        order_id: context.orderId,
        item_id: orderItem.id,
        department_id: context.departmentId,
        assigned_staff_id: context.assignedStaffId,
        overdue_minutes: overdueMinutes,
        priority_level: priorityLevel,
        timestamp: DateTime.now().toISO()
      })

    } catch (error) {
      console.error('Error handling item overdue event:', error)
    }
  }

  /**
   * Handle staff assignment event
   */
  public async onStaffAssigned(data: EventsList['staff:assigned']) {
    const { orderItem, assignedStaffId, assignedBy, metadata } = data

    try {
      console.log(`Staff ${assignedStaffId} assigned to item ${orderItem.id} by ${assignedBy}`)

      // Update item metadata with assignment history
      const meta = orderItem.meta || {}
      if (!meta.assignment_history) {
        meta.assignment_history = []
      }

      meta.assignment_history.push({
        staff_id: assignedStaffId,
        assigned_by: assignedBy,
        assigned_at: DateTime.now().toISO(),
        metadata
      })

      orderItem.meta = meta
      await orderItem.save()

      // Send staff assignment notification
      await RealTimeNotificationService.notifyStaffAssignment(orderItem, assignedStaffId, assignedBy)

    } catch (error) {
      console.error('Error handling staff assignment event:', error)
    }
  }

  /**
   * Handle department workload update event
   */
  public async onDepartmentWorkloadUpdated(data: EventsList['department:workload:updated']) {
    const { departmentId, vendorId, branchId, workloadData, metadata } = data

    try {
      console.log(`Department ${departmentId} workload updated:`, workloadData)

      // Log workload analytics
      console.log('Department workload analytics:', {
        department_id: departmentId,
        vendor_id: vendorId,
        branch_id: branchId,
        workload_data: workloadData,
        timestamp: DateTime.now().toISO(),
        metadata
      })

    } catch (error) {
      console.error('Error handling department workload update event:', error)
    }
  }
}
