import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { DateTime } from 'luxon'
import BookingOrderService from 'App/Services/BookingOrderService'

export default class BookingOrderController {
  /**
   * Create a booking with associated order (complete booking flow)
   */
  public async createBookingOrder({ request, response, auth }: HttpContextContract) {
    try {
      const {
        customerId,
        vendorId,
        branchId,
        productId,
        appointmentStart,
        appointmentEnd,
        selectedServiceOptions = [],
        staffAssignments = [],
        equipmentReservations = [],
        bookingNotes = null,
        sectionId = null,
        lotId = null
      } = request.only([
        'customerId',
        'vendorId',
        'branchId',
        'productId',
        'appointmentStart',
        'appointmentEnd',
        'selectedServiceOptions',
        'staffAssignments',
        'equipmentReservations',
        'bookingNotes',
        'sectionId',
        'lotId'
      ])

      // Validate required fields
      if (!customerId || !vendorId || !branchId || !productId || !appointmentStart || !appointmentEnd) {
        return response.status(400).json({
          success: false,
          message: 'Customer ID, Vendor ID, Branch ID, Product ID, appointment start and end times are required'
        })
      }

      // Parse appointment times
      let parsedStartTime: DateTime
      let parsedEndTime: DateTime
      
      try {
        parsedStartTime = DateTime.fromISO(appointmentStart)
        parsedEndTime = DateTime.fromISO(appointmentEnd)
        
        if (!parsedStartTime.isValid || !parsedEndTime.isValid) {
          throw new Error('Invalid time format')
        }
      } catch (error) {
        return response.status(400).json({
          success: false,
          message: 'Invalid appointment time format. Use ISO format'
        })
      }

      // Create booking with order
      const result = await BookingOrderService.createBookingOrder({
        customerId,
        vendorId,
        branchId,
        productId,
        appointmentStart: parsedStartTime,
        appointmentEnd: parsedEndTime,
        selectedServiceOptions,
        staffAssignments,
        equipmentReservations,
        bookingNotes,
        sectionId,
        lotId,
        staffId: auth.user?.id
      })

      return response.status(201).json({
        success: true,
        message: 'Booking created successfully',
        data: {
          booking: result.booking,
          order: result.order,
          totalPrice: result.totalPrice,
          confirmationCode: result.confirmationCode
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to create booking',
        error: error.message
      })
    }
  }

  /**
   * Update a booking and its associated order
   */
  public async updateBookingOrder({ params, request, response }: HttpContextContract) {
    try {
      const { bookingId } = params
      const updates = request.only([
        'appointmentStart',
        'appointmentEnd',
        'selectedServiceOptions',
        'staffAssignments',
        'equipmentReservations',
        'bookingNotes'
      ])

      // Parse appointment times if provided
      if (updates.appointmentStart) {
        const parsedStartTime = DateTime.fromISO(updates.appointmentStart)
        if (!parsedStartTime.isValid) {
          return response.status(400).json({
            success: false,
            message: 'Invalid appointment start time format'
          })
        }
        updates.appointmentStart = parsedStartTime
      }

      if (updates.appointmentEnd) {
        const parsedEndTime = DateTime.fromISO(updates.appointmentEnd)
        if (!parsedEndTime.isValid) {
          return response.status(400).json({
            success: false,
            message: 'Invalid appointment end time format'
          })
        }
        updates.appointmentEnd = parsedEndTime
      }

      // Update booking with order
      const result = await BookingOrderService.updateBookingOrder(bookingId, updates)

      return response.json({
        success: true,
        message: 'Booking updated successfully',
        data: {
          booking: result.booking,
          order: result.order,
          totalPrice: result.totalPrice,
          confirmationCode: result.confirmationCode
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to update booking',
        error: error.message
      })
    }
  }

  /**
   * Cancel a booking and its associated order
   */
  public async cancelBookingOrder({ params, request, response, auth }: HttpContextContract) {
    try {
      const { bookingId } = params
      const { reason } = request.only(['reason'])

      const result = await BookingOrderService.cancelBookingOrder(
        bookingId,
        auth.user?.id || 'system',
        reason
      )

      return response.json({
        success: true,
        message: 'Booking cancelled successfully',
        data: {
          booking: result.booking,
          order: result.order
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to cancel booking',
        error: error.message
      })
    }
  }

  /**
   * Confirm a booking and update order status
   */
  public async confirmBookingOrder({ params, response }: HttpContextContract) {
    try {
      const { bookingId } = params

      const result = await BookingOrderService.confirmBookingOrder(bookingId)

      return response.json({
        success: true,
        message: 'Booking confirmed successfully',
        data: {
          booking: result.booking,
          order: result.order
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to confirm booking',
        error: error.message
      })
    }
  }

  /**
   * Get booking with order details
   */
  public async getBookingWithOrder({ params, response }: HttpContextContract) {
    try {
      const { bookingId } = params

      const result = await BookingOrderService.getBookingWithOrder(bookingId)

      return response.json({
        success: true,
        data: {
          booking: result.booking,
          order: result.order
        }
      })

    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Booking not found',
        error: error.message
      })
    }
  }

  /**
   * Get customer's booking history with orders
   */
  public async getCustomerBookingHistory({ params, request, response }: HttpContextContract) {
    try {
      const { customerId } = params
      const {
        page = 1,
        per = 20,
        status,
        startDate,
        endDate
      } = request.qs()

      // This would be implemented to get customer's booking history
      // For now, return a placeholder response
      return response.json({
        success: true,
        message: 'Customer booking history endpoint - to be implemented',
        data: {
          customerId,
          filters: { page, per, status, startDate, endDate }
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get customer booking history',
        error: error.message
      })
    }
  }

  /**
   * Get vendor's booking summary
   */
  public async getVendorBookingSummary({ params, request, response }: HttpContextContract) {
    try {
      const { vendorId } = params
      const {
        branchId,
        startDate,
        endDate,
        groupBy = 'day'
      } = request.qs()

      // This would be implemented to get vendor's booking summary
      // For now, return a placeholder response
      return response.json({
        success: true,
        message: 'Vendor booking summary endpoint - to be implemented',
        data: {
          vendorId,
          filters: { branchId, startDate, endDate, groupBy }
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get vendor booking summary',
        error: error.message
      })
    }
  }
}
