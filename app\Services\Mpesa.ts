import { Mpesa } from '@osenco/mpesa'
import Payment from 'App/Models/Payment'
import MpesaConfigValidator from './MpesaConfigValidator'

export default class KeMpesa {
  private mpesa: Mpesa

  constructor(_payment: Payment) {
    // Validate configuration first
    console.log('=== MPESA INITIALIZATION ===')
    MpesaConfigValidator.logConfigStatus()
    MpesaConfigValidator.validateOrThrow()

    const baseUrl = process.env.MPESA_CALLBACK_DOMAIN || process.env.APP_URL || 'https://uat-api.appinapp.ke'
    const mpesaBasePath = '/v1/mpsa/e59ed6a68b83'

    console.log('Environment:', process.env.MPESA_ENVIRONMENT)
    console.log('Base URL:', baseUrl)
    console.log('M-Pesa Base Path:', mpesaBasePath)
    console.log('Store:', process.env.MPESA_STORE)
    console.log('Shortcode:', process.env.MPESA_SHORTCODE)

    // Construct proper callback URLs that match actual routes
    const callbackUrls = {
      callbackUrl: `${baseUrl}${mpesaBasePath}/confirm`,
      confirmationUrl: `${baseUrl}${mpesaBasePath}/confirm`,
      validationUrl: `${baseUrl}${mpesaBasePath}/validate`,
      timeoutUrl: `${baseUrl}${mpesaBasePath}/timeout`,
      resultUrl: `${baseUrl}${mpesaBasePath}/result`,
    }

    // IMPORTANT: These URLs must be registered in your M-Pesa Dashboard (Daraja Portal)
    // Validation URL: ${baseUrl}${mpesaBasePath}/validate
    // Confirmation URL: ${baseUrl}${mpesaBasePath}/confirm

    console.log('=== MPESA CALLBACK URLS ===')
    console.log('Callback URL:', callbackUrls.callbackUrl)
    console.log('Confirmation URL:', callbackUrls.confirmationUrl)
    console.log('Validation URL:', callbackUrls.validationUrl)
    console.log('Timeout URL:', callbackUrls.timeoutUrl)
    console.log('Result URL:', callbackUrls.resultUrl)

    this.mpesa = new Mpesa({
      type: 4,
      key: process.env.MPESA_CONSUMER_KEY!,
      secret: process.env.MPESA_CONSUMER_SECRET!,
      env: process.env.MPESA_ENVIRONMENT! as 'sandbox' | 'live',
      store: Number(process.env.MPESA_STORE!),
      shortcode: Number(process.env.MPESA_SHORTCODE!),
      username: process.env.MPESA_INITIATOR_NAME || '',
      password: process.env.MPESA_INITIATOR_PASSWORD || '',
      passkey: process.env.MPESA_PASSKEY!,
      ...callbackUrls,
    })
    console.log('MPESA instance created successfully')
  }

  public async stkPush(phone: string, amount: string, invoiceId: string, orderId: string) {
    console.log('=== MPESA STK PUSH REQUEST ===')
    console.log('Request Details:', {
      phone: phone.replace(/(\d{3})\d{6}(\d{3})/, '$1******$2'), // Mask phone number for security
      amount,
      invoiceId,
      orderId,
      environment: process.env.MPESA_ENVIRONMENT,
      timestamp: new Date().toISOString()
    })

    // Validate inputs
    if (!phone || !amount || !invoiceId) {
      throw new Error('Missing required parameters for STK Push')
    }

    // Validate phone number format (Kenyan format)
    const phoneRegex = /^254\d{9}$/
    if (!phoneRegex.test(phone)) {
      throw new Error('Invalid phone number format. Expected format: 254XXXXXXXXX')
    }

    // Validate amount
    const numericAmount = parseInt(amount)
    if (isNaN(numericAmount) || numericAmount <= 0) {
      throw new Error('Invalid amount. Amount must be a positive number')
    }

    try {
      const response = await this.mpesa.stkPush(phone, numericAmount, invoiceId, orderId)

      console.log('=== MPESA STK PUSH RESPONSE ===')
      console.log('Response Status:', response.error ? 'ERROR' : 'SUCCESS')

      if (response.error) {
        console.error('STK Push Error Response:', JSON.stringify(response.error, null, 2))
      } else {
        console.log('STK Push Success Response:', JSON.stringify(response.data, null, 2))
      }

      return response
    } catch (error) {
      console.error('=== MPESA STK PUSH ERROR ===')
      console.error('Error Type:', error.constructor.name)
      console.error('Error Message:', error.message)
      console.error('Error Stack:', error.stack)

      // Re-throw with more context
      throw new Error(`M-Pesa STK Push failed: ${error.message}`)
    }
  }
}
