import { DateTime } from 'luxon'
import { <PERSON>Model, BelongsTo, beforeCreate, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { compose } from '@ioc:Adonis/Core/Helpers'
import Vendor from './Vendor'
import Branch from './Branch'
import User from './User'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import CampaignFilter from './Filters/CampaignFilter'
import { ulid } from 'ulidx'

export default class Campaign extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => CampaignFilter
  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public status: 'Draft' | 'Pending' | 'Approved' | 'Expired'

  @column()
  public link: string

  @column()
  public meta: Record<string, any>

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column.dateTime()
  public startDate: DateTime

  @column.dateTime()
  public endDate: DateTime

  @column()
  public displayDuration: number

  @column()
  public position: number | null

  @column()
  public billingType: 'fixed' | 'percentage'

  @column()
  public billingAmount: number

  @column()
  public currency: string

  @column({ columnName: 'approved_by' })
  public approvedById: string | null

  @column.dateTime()
  public approvedAt: DateTime | null

  @column()
  public rejectionReason: string | null

  @attachment({ folder: 'campaigns', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(campaign: Campaign) {
    campaign.id = ulid().toLowerCase()
  }

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @belongsTo(() => User, {
    foreignKey: 'approvedById',
  })
  public approvedBy: BelongsTo<typeof User>
}
