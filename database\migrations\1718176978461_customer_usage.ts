import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'customer_usage'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      
      // Relationships
      table.string('customer_id').references('id').inTable('users').onDelete('CASCADE').notNullable()
      table.integer('subscription_id').references('id').inTable('customer_subscriptions').onDelete('SET NULL').nullable()
      
      // Usage details
      table.enum('type', ['order', 'feature', 'notification']).notNullable()
      table.string('feature_code').nullable()
      table.integer('quantity').notNullable().defaultTo(1)
      table.decimal('amount', 10, 2).notNullable()
      table.string('currency').notNullable().defaultTo('KES')
      table.string('billing_period').notNullable() // Format: YYYY-MM
      
      // Status
      table.enum('status', ['pending', 'billed']).defaultTo('pending')
      table.timestamp('billed_at', { useTz: true }).nullable()
      
      // Reference
      table.string('reference_id').nullable() // ID of the related order/feature usage
      table.string('reference_type').nullable() // Type of the reference (order_id, etc.)
      
      // Metadata
      table.jsonb('meta').nullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}