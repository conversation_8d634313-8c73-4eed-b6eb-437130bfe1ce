import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import VendorCategory from '../../Models/VendorCategory'
import { bind } from '@adonisjs/route-model-binding'

export default class VendorCategoriesController {
  /**
   * @name VendorCategory management
   * @index
   * @summary List all VendorCategory
   * @description List all VendorCategory, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */

  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const categoryQuery = VendorCategory.filter(filters)

    return await categoryQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a VendorCategory
   * @description Create a VendorCategory with their details (name and details)
   * @requestBody {"name": "", "details": "", "VendorTypeId": ""}
   * @responseBody 200 - <VendorCategory>
   */

  public async store({ request }: HttpContextContract) {
    const { name, details, vendorTypeId } = request.all()
    const category = new VendorCategory()

    category.fill({ name, details, vendorTypeId })

    const image = request.file('image')

    if (image) {
      category.image = Attachment.fromFile(image)
    }

    return await category.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a single VendorCategory
   * @description Show a VendorCategory with their details (name and details)
   * @paramPath id required number - VendorType ID
   * @responseBody 200 - <VendorCategory>
   * @response 404 - VendorCategory not found
   */
  public async show({ response }: HttpContextContract, category: VendorCategory) {
    return response.json(category)
  }

  @bind()

  /**
   * @update
   * @summary Update a VendorCategory
   * @description Update a VendorCategory with their details (name and details)
   * @paramPath id required number - VendorType ID
   * @requestBody <VendorCategory>
   * @responseBody 200 - <VendorCategory>
   * @response 404 - VendorCategory not found
   */
  public async update({ request, response }: HttpContextContract, category: VendorCategory) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await category.merge(input).save()

    return response.json(category)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a VendorCategory
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, category: VendorCategory) {
    return await category.delete()
  }
}
