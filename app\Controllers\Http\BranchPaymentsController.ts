import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from '../../Models/Order'
import { bind } from '@adonisjs/route-model-binding'
import CustomerNewOrder from 'App/Notifications/Customer/CustomerNewOrder'
import User from 'App/Models/User'
import StaffNewOrder from 'App/Notifications/Staff/StaffNewOrder'
import Lot from 'App/Models/Lot'
import Branch from 'App/Models/Branch'
import { OrderValidationHelper } from 'App/Helpers/OrderValidationHelper'

/**
 * @name Order management
 * @version 1.0.0
 * @description Order management for the application
 */
export default class BranchPaymentsController {
  /**
   * @index
   * @summary List all Orders
   * @description List all Orders, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const OrderQuery = Order.filter(filters)
      .where('branchId', branch.id)
      .preload('customer')
      .preload('invoices', (iq) =>
        iq
          .preload('payments')
          .whereHas('payments', (pq) => pq.whereNotIn('status', ['Failed']))
          .withAggregate('payments', (q) => q.sum('amount').as('total'))
      )

    return await OrderQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a Order
   * @description Create a Order with their details (name and details)
   * @requestBody {"staffId": "", "vendorId": "", "branchId": "", "sectionId": "", "action": "", "type": "", "delivery": "", "status": "", "meta": {}} - <Order>
   * @responseBody 200 - <Order>
   */
  @bind()
  public async store({ request, response, auth }: HttpContextContract, branch: Branch) {
    try {
      const {
        staffId,
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
        userId = null,
        items = [],
      } = request.all()

      // Validate items before creating order
      const itemValidation = OrderValidationHelper.validateDirectOrderItems(items)
      if (!itemValidation.isValid) {
        return OrderValidationHelper.createItemValidationErrorResponse(response, itemValidation)
      }

      const order = await branch.related('orders').create({
        userId: userId ? userId : auth.user?.id,
        staffId,
        vendorId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
      })

      // Create order items using HasMany relationship
      await Promise.all(
        items.map(async (item: Record<string, number>) => {
          return Promise.all(
            Object.keys(item).map(async (productId) => {
              return await order.related('items').create({
                productId: productId,
                quantity: item[productId],
              })
            })
          )
        })
      )

      await order.load('items')

      let amount = order.items?.reduce((acc, item) => acc + item.price * item.quantity, 0)

      if (order.meta && order.meta.charges) {
        amount += Object.values(order.meta.charges as Record<string, number>)?.reduce(
          (acc, charge) => acc + charge,
          0
        )
      }

      await order.related('invoices').create({
        amount,
        status: 'Pending',
      })

      await order.load('items')
      await order.load('customer')
      await order.load('vendor')
      await order.load('branch')
      await order.load('invoices', (iq) => iq.preload('order', (oq) => oq.preload('payments')))

      const customer = await User.findOrFail(userId)
      await customer.notify(new CustomerNewOrder(order))

      customer
        .related('branches')
        .sync({ [branchId]: { active: true, vendor_id: vendorId, branch_id: branchId } })

      if (lotId) {
        const lot = await Lot.findOrFail(lotId)

        const staff = await lot.related('staff').query().orderBy('created_at', 'desc').firstOrFail()

        await staff.notify(new StaffNewOrder(order))
      } else {
        const staff = await User.query().whereHas('employers', (q) => {
          q.where('branch_id', branchId)
        })

        staff.map(async (u) => await u.notify(new StaffNewOrder(order)))
      }

      return response.json(order)
    } catch (error) {
      console.error(error)

      return response.badRequest({ error: error.message })
    }
  }
}
