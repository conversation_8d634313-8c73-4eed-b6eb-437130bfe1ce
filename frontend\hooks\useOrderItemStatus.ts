import { useState, useEffect, useCallback } from 'react'
import { OrderItemData, StaffMember } from '@/components/OrderItemStatusManager'

interface UseOrderItemStatusReturn {
  orderItem: OrderItemData | null
  availableStaff: StaffMember[]
  loading: boolean
  error: string | null
  updateStatus: (status: string, metadata?: Record<string, any>) => Promise<void>
  assignStaff: (staffId: string) => Promise<void>
  updateNotes: (notes: string) => Promise<void>
  startPreparation: () => Promise<void>
  pausePreparation: () => Promise<void>
  completePreparation: () => Promise<void>
  refreshData: () => Promise<void>
}

interface ApiResponse<T> {
  data: T
  status: string
  message?: string
}

export const useOrderItemStatus = (
  orderItemId: number,
  initialData?: OrderItemData
): UseOrderItemStatusReturn => {
  const [orderItem, setOrderItem] = useState<OrderItemData | null>(initialData || null)
  const [availableStaff, setAvailableStaff] = useState<StaffMember[]>([])
  const [loading, setLoading] = useState(!initialData)
  const [error, setError] = useState<string | null>(null)

  // API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333/api/v1'

  // Get auth token
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('auth_token') || ''
  }, [])

  // Generic API call function
  const apiCall = useCallback(async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const token = getAuthToken()
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      },
      ...options
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Network error' }))
      throw new Error(errorData.message || `HTTP ${response.status}`)
    }

    return response.json()
  }, [API_BASE_URL, getAuthToken])

  // Fetch order item data
  const fetchOrderItem = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await apiCall<ApiResponse<OrderItemData>>(
        `/order-items/${orderItemId}?include=product,department,assignedStaff,modifiers,statusHistory`
      )

      setOrderItem(response.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch order item'
      setError(errorMessage)
      console.error('Error fetching order item:', err)
    } finally {
      setLoading(false)
    }
  }, [orderItemId, apiCall])

  // Fetch available staff
  const fetchAvailableStaff = useCallback(async () => {
    if (!orderItem?.department_id) return

    try {
      const response = await apiCall<ApiResponse<StaffMember[]>>(
        `/departments/${orderItem.department_id}/staff?available=true`
      )

      setAvailableStaff(response.data || [])
    } catch (err) {
      console.error('Error fetching available staff:', err)
    }
  }, [orderItem?.department_id, apiCall])

  // Update order item status
  const updateStatus = useCallback(async (
    status: string,
    metadata: Record<string, any> = {}
  ) => {
    try {
      const response = await apiCall<ApiResponse<OrderItemData>>(
        `/order-items/${orderItemId}/status`,
        {
          method: 'PATCH',
          body: JSON.stringify({
            status,
            metadata: {
              ...metadata,
              updated_via: 'status_manager',
              timestamp: new Date().toISOString()
            }
          })
        }
      )

      setOrderItem(response.data)

      // Update local state optimistically
      if (orderItem) {
        setOrderItem(prev => prev ? {
          ...prev,
          status: status as any,
          preparation_started_at: status === 'preparing' && !prev.preparation_started_at 
            ? new Date().toISOString() 
            : prev.preparation_started_at,
          preparation_completed_at: status === 'ready' 
            ? new Date().toISOString() 
            : prev.preparation_completed_at
        } : null)
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
      setError(errorMessage)
      console.error('Error updating status:', err)
      throw err
    }
  }, [orderItemId, orderItem, apiCall])

  // Assign staff to order item
  const assignStaff = useCallback(async (staffId: string) => {
    try {
      const response = await apiCall<ApiResponse<OrderItemData>>(
        `/order-items/${orderItemId}/assign`,
        {
          method: 'PATCH',
          body: JSON.stringify({
            assigned_staff_id: staffId || null,
            metadata: {
              assigned_via: 'status_manager',
              timestamp: new Date().toISOString()
            }
          })
        }
      )

      setOrderItem(response.data)

      // Update staff workload
      if (staffId) {
        setAvailableStaff(prev => prev.map(staff => {
          if (staff.id === staffId) {
            return {
              ...staff,
              current_workload: staff.current_workload + 1
            }
          }
          return staff
        }))
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to assign staff'
      setError(errorMessage)
      console.error('Error assigning staff:', err)
      throw err
    }
  }, [orderItemId, apiCall])

  // Update preparation notes
  const updateNotes = useCallback(async (notes: string) => {
    try {
      const response = await apiCall<ApiResponse<OrderItemData>>(
        `/order-items/${orderItemId}/notes`,
        {
          method: 'PATCH',
          body: JSON.stringify({
            preparation_notes: notes,
            metadata: {
              updated_via: 'status_manager',
              timestamp: new Date().toISOString()
            }
          })
        }
      )

      setOrderItem(response.data)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update notes'
      setError(errorMessage)
      console.error('Error updating notes:', err)
      throw err
    }
  }, [orderItemId, apiCall])

  // Start preparation
  const startPreparation = useCallback(async () => {
    await updateStatus('preparing', {
      action: 'start_preparation',
      started_at: new Date().toISOString()
    })
  }, [updateStatus])

  // Pause preparation
  const pausePreparation = useCallback(async () => {
    try {
      await apiCall(`/order-items/${orderItemId}/pause`, {
        method: 'PATCH',
        body: JSON.stringify({
          metadata: {
            paused_at: new Date().toISOString(),
            action: 'pause_preparation'
          }
        })
      })

      // Refresh data to get updated state
      await fetchOrderItem()

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pause preparation'
      setError(errorMessage)
      console.error('Error pausing preparation:', err)
      throw err
    }
  }, [orderItemId, apiCall, fetchOrderItem])

  // Complete preparation
  const completePreparation = useCallback(async () => {
    await updateStatus('ready', {
      action: 'complete_preparation',
      completed_at: new Date().toISOString()
    })
  }, [updateStatus])

  // Refresh all data
  const refreshData = useCallback(async () => {
    await Promise.all([
      fetchOrderItem(),
      fetchAvailableStaff()
    ])
  }, [fetchOrderItem, fetchAvailableStaff])

  // Initial data fetch
  useEffect(() => {
    if (!initialData) {
      fetchOrderItem()
    }
  }, [fetchOrderItem, initialData])

  // Fetch staff when department changes
  useEffect(() => {
    if (orderItem?.department_id) {
      fetchAvailableStaff()
    }
  }, [orderItem?.department_id, fetchAvailableStaff])

  // Handle real-time updates
  useEffect(() => {
    const handleWebSocketMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data)
        
        if (data.type === 'item_status_update' && data.item_id === orderItemId) {
          // Update item status in real-time
          setOrderItem(prev => prev ? {
            ...prev,
            status: data.status,
            assigned_staff_id: data.assigned_staff_id,
            assigned_staff_name: data.assigned_staff_name,
            preparation_started_at: data.preparation_started_at || prev.preparation_started_at,
            preparation_completed_at: data.preparation_completed_at || prev.preparation_completed_at
          } : null)
        }

        if (data.type === 'staff_assignment' && data.item_id === orderItemId) {
          // Update staff assignment in real-time
          setOrderItem(prev => prev ? {
            ...prev,
            assigned_staff_id: data.assigned_staff_id,
            assigned_staff_name: data.assigned_staff_name
          } : null)

          // Update staff workload
          setAvailableStaff(prev => prev.map(staff => {
            if (staff.id === data.assigned_staff_id) {
              return {
                ...staff,
                current_workload: staff.current_workload + (data.workload_change || 1)
              }
            }
            return staff
          }))
        }

      } catch (error) {
        console.error('Error processing WebSocket message:', error)
      }
    }

    // This would be connected to your WebSocket instance
    // window.addEventListener('websocket-message', handleWebSocketMessage)
    
    return () => {
      // window.removeEventListener('websocket-message', handleWebSocketMessage)
    }
  }, [orderItemId])

  return {
    orderItem,
    availableStaff,
    loading,
    error,
    updateStatus,
    assignStaff,
    updateNotes,
    startPreparation,
    pausePreparation,
    completePreparation,
    refreshData
  }
}

export default useOrderItemStatus
