import KiaArrivalFlight from 'App/Models/Flights/KiaArrivalFlight'
import KiaDepartureFlight from 'App/Models/Flights/KiaDepartureFlight'
import MiaArrivalFlight from 'App/Models/Flights/MiaArrivalFlight'
import Order from 'App/Models/Order'
import Product from 'App/Models/Product'

export default class ProcessKaa {
  public static async process(item: Product, order: Order) {
    let apiRes: {
      arrivals: any[]
      departures: any[]
    } = {
      arrivals: [],
      departures: [],
    }

    // Default limit for flight data, can be overridden by filters
    const DEFAULT_FLIGHT_LIMIT = 50
    const MAX_FLIGHT_LIMIT = 200

    switch (item.ref) {
      case 'FLIGHTQUERY':
        const response = order.meta.responses

        const itemResponse = response[item.id]

        Object.keys(itemResponse).forEach(async (section) => {
          const { account: airport, limit, ...filters } = itemResponse[section]

          // Use configurable limit with reasonable defaults and maximum
          const flightLimit = Math.min(
            parseInt(limit) || DEFAULT_FLIGHT_LIMIT,
            MAX_FLIGHT_LIMIT
          )

          switch (airport) {
            case 'KIA':
              apiRes = {
                arrivals: await KiaArrivalFlight.query().filter(filters).limit(flightLimit).exec(),
                departures: await KiaDepartureFlight.query().filter(filters).limit(flightLimit).exec(),
              }
              break

            default:
              break
          }
        })
        break
      default:
        // Default case with configurable limits
        const defaultLimit = Math.min(DEFAULT_FLIGHT_LIMIT, MAX_FLIGHT_LIMIT)
        apiRes = {
          arrivals: [
            ...(await KiaArrivalFlight.query().limit(defaultLimit).exec()),
            ...(await MiaArrivalFlight.query().limit(defaultLimit).exec()),
          ],
          departures: [],
        }
        break
    }

    console.log({ apiRes })

    return apiRes
  }
}
