import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Product from 'App/Models/Product'

export default class ProductFormsController {
  /**
   * @index
   * @summary Show all forms
   * @version 1.0.0
   * @description Form management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, product: Product) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const formQuery = product.related('forms').query().filter(filters)

    return await formQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a form
   * @description Create a form with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   */
  @bind()
  public async store({ request }: HttpContextContract, product: Product) {
    const { name, details, sections } = request.all()
    const form = await product
      .related('forms')
      .create({ name, details, sections: sections.reduce((p, s) => ({ ...p, [s.id]: s }), {}) })

    const image = request.file('image')

    if (image) {
      form.merge({ image: Attachment.fromFile(image) }).save()
    }
  }
}
