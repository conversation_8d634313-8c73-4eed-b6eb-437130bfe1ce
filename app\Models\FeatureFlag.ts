import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class FeatureFlag extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public code: string

  @column()
  public description: string | null

  @column()
  public isEnabled: boolean

  @column()
  public conditions: Record<string, any>

  @column()
  public scope: 'global' | 'vendor' | 'branch' | 'customer'

  @column()
  public scopeId: string | null

  @column()
  public meta: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  /**
   * Check if feature is enabled for a specific scope
   */
  public isEnabledForScope(scopeId: string | null = null): boolean {
    if (!this.isEnabled) return false
    if (this.scope === 'global') return true
    return this.scopeId === scopeId
  }

  /**
   * Check if feature meets all conditions
   */
  public meetsConditions(context: Record<string, any>): boolean {
    if (!this.conditions || Object.keys(this.conditions).length === 0) {
      return true
    }

    for (const [key, value] of Object.entries(this.conditions)) {
      if (context[key] !== value) {
        return false
      }
    }

    return true
  }
}