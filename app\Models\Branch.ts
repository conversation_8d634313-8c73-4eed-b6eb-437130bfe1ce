import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  HasMany,
  ManyToMany,
  beforeCreate,
  beforeSave,
  belongsTo,
  column,
  computed,
  hasMany,
  manyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Vendor from './Vendor'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import BranchFilter from './Filters/BranchFilter'
import { ulid } from 'ulidx'
import Section from './Section'
import User from './User'
import Product from './Product'
import Database from '@ioc:Adonis/Lucid/Database'
import Group from './Group'
import Order from './Order'
import Setting from './Setting'

export default class Branch extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => BranchFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public code: string

  @column()
  public email: string

  @column()
  public phone: string

  @column()
  public details: string

  @column()
  public vendorId: string

  @column()
  public location: {
    name?: string
    address: string
    regions: {
      administrative_area_level_3?: string | null
      administrative_area_level_1?: string | null
      country: string
    }
    coordinates: {
      lat: number
      lng: number
    }
    place_id: string
  } | null

  @column()
  public hours: Record<string, any>

  @column()
  public geom?: any

  @attachment({ folder: 'branches', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(branch: Branch) {
    branch.id = ulid().toLowerCase()
  }

  @beforeSave()
  public static async setGeom(branch: Branch) {
    if (branch.location?.coordinates) {
      branch.geom = Database.st().geomFromText(
        `POINT(${branch.location.coordinates.lng} ${branch.location.coordinates.lat})`,
        4326
      )
    }
  }

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @hasMany(() => Section)
  public sections: HasMany<typeof Section>

  @manyToMany(() => User, {
    pivotTable: 'staff',
    pivotTimestamps: true,
    pivotColumns: ['identifier', 'vendor_id'],
    onQuery(query) {
      query.pivotColumns(['identifier', 'vendor_id'])
    },
  })
  public staff: ManyToMany<typeof User>

  @manyToMany(() => User, {
    pivotTable: 'customers',
    pivotTimestamps: true,
    pivotColumns: ['active', 'vendor_id', 'registered_by_staff_id'],
    onQuery(query) {
      query.pivotColumns(['active', 'vendor_id', 'registered_by_staff_id'])
    },
  })
  public customers: ManyToMany<typeof User>

  @hasMany(() => Product)
  public products: HasMany<typeof Product>

  @hasMany(() => Order)
  public orders: HasMany<typeof Order>

  @hasMany(() => Group)
  public groups: HasMany<typeof Group>

  @hasMany(() => Setting)
  public settings: HasMany<typeof Setting>

  @computed()
  public async orderCount() {
    return this.$extras.orders_count
  }

  @computed()
  public async productCount() {
    return this.$extras.products_count
  }

  @computed()
  public async sectionCount() {
    return this.$extras.sections_count
  }

  @computed()
  public async staffCount() {
    return this.$extras.staff_count
  }
}
