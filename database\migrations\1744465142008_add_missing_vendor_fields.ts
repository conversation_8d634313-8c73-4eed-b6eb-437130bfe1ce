import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'vendors'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add code field if it doesn't exist
      if (!this.schema.hasColumn(this.tableName, 'code')) {
        table.string('code').nullable()
      }
      
      // Add location field if it doesn't exist
      if (!this.schema.hasColumn(this.tableName, 'location')) {
        table.json('location').nullable()
      }
      
      // Add geom field if it doesn't exist
      if (!this.schema.hasColumn(this.tableName, 'geom')) {
        table.geometry('geom').nullable()
      }
      
      // Add remember_me_token if it doesn't exist
      if (!this.schema.hasColumn(this.tableName, 'remember_me_token')) {
        table.string('remember_me_token').nullable()
      }

      // Modify constraints to match the new schema
      table.string('email', 255).nullable().alter()
      table.string('phone', 13).nullable().alter()
      table.string('reg').nullable().alter()
      table.string('kra').nullable().alter()
      table.string('permit').nullable().alter()
      table.boolean('active').defaultTo(true).alter()
      table.boolean('featured').defaultTo(false).alter()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Revert changes if needed
      table.string('email', 255).notNullable().alter()
      table.string('phone', 13).notNullable().alter()
      table.string('reg', 20).nullable().unique().alter()
      table.string('kra').notNullable().unique().alter()
      table.string('permit').nullable().unique().alter()
      table.boolean('active').defaultTo(0).alter()
      table.boolean('featured').defaultTo(0).alter()
      
      // Drop added columns
      table.dropColumn('code')
      table.dropColumn('location')
      table.dropColumn('geom')
      table.dropColumn('remember_me_token')
    })
  }
} 