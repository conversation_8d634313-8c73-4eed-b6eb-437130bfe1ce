import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import ProductCategory from '../../Models/ProductCategory'
import { bind } from '@adonisjs/route-model-binding'

export default class ProductCategoriesController {
  /**
   * @name ProductCategory management
   * @index
   * @summary List all ProductCategories
   * @description List all ProductCategories, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */

  public async index({ request, response }: HttpContextContract) {
    const { per = 50, page = 1, order = 'createdAt', sort = 'desc', productsPerCategory = 20, ...filters } = request.qs()
    const categoryQuery = ProductCategory.filter(filters)

    const { vendor, branch, service = null } = request.qs()

    if (vendor) {
      categoryQuery
        .whereHas('products', (pq) => {
          pq.where('vendor_id', vendor)

          if (service) {
            pq.where('service_id', service)
          }

          if (branch) {
            pq.where('branch_id', branch)
          }
        })
        .preload('products', (pq) => {
          pq.where('vendor_id', vendor)

          if (service) {
            pq.where('service_id', service)
          }

          if (branch) {
            pq.where('branch_id', branch)
          }

          // Use configurable limit instead of hard-coded 5
          pq.groupLimit(Math.min(parseInt(productsPerCategory) || 20, 100))
        })
    }

    const categories = await categoryQuery.orderBy(order, sort).paginate(page, per)

    return response.json(categories)
  }

  /**
   * @store
   * @summary Create a ProductCategory
   * @description Create a ProductCategory with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <ProductCategory>
   */

  public async store({ request }: HttpContextContract) {
    const { name, details, productTypeId } = request.all()
    const category = new ProductCategory()

    category.fill({ name, details, productTypeId })

    const image = request.file('image')

    if (image) {
      category.image = Attachment.fromFile(image)
    }

    return await category.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a single ProductCategory
   * @description Show a ProductCategory with their details (name and details)
   * @responseBody 200 - <ProductCategory>
   * @response 404 - ProductCategory not found
   */
  public async show({ response }: HttpContextContract, category: ProductCategory) {
    return response.json(category)
  }

  @bind()

  /**
   * @update
   * @summary Update a ProductCategory
   * @description Update a ProductCategory with their details (name and details)
   * @requestBody <ProductCategory>
   * @responseBody 200 - <ProductCategory>
   * @response 404 - ProductCategory not found
   */
  public async update({ request, response }: HttpContextContract, category: ProductCategory) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await category.merge(input).save()

    return response.json(category)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a ProductCategory
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, category: ProductCategory) {
    return await category.delete()
  }
}
