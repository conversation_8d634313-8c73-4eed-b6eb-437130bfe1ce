import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'products'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('service_configuration_id')
        .references('id')
        .inTable('service_configurations')
        .onDelete('SET NULL')
        .nullable()
        .index()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('service_configuration_id')
    })
  }
}
