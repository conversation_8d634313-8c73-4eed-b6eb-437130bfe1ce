import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import Booking from 'App/Models/Booking'
import User from 'App/Models/User'

export default class BookingCancelledNotification implements NotificationContract {
  constructor(
    private booking: Booking,
    private cancelledBy: 'customer' | 'vendor' | 'system' = 'customer',
    private cancellationReason?: string
  ) {}

  public via(_notifiable: User) {
    return ['database' as const, 'fcm' as const, 'mail' as const, 'sms' as const]
  }

  public toDatabase(notifiable: User) {
    const title = this.getCancellationTitle()
    const message = this.getCancellationMessage()

    return NotificationHelper.createDatabaseNotification({
      category: NotificationCategory.BOOKING,
      type: NotificationType.BOOKING_CANCELLATION,
      priority: NotificationPriority.HIGH,
      title,
      message,
      actionType: NotificationActionType.VIEW_BOOKING,
      actionUrl: `/bookings/${this.booking.id}`,
      actionLabel: 'View Details',
      data: {
        booking_id: this.booking.id,
        confirmation_code: this.booking.confirmationCode,
        cancelled_by: this.cancelledBy,
        cancellation_reason: this.cancellationReason,
        original_appointment_date: this.booking.appointmentStart.toFormat('yyyy-MM-dd'),
        original_appointment_time: this.booking.appointmentStart.toFormat('HH:mm'),
        service_name: this.booking.product?.name,
        vendor_name: this.booking.vendor?.name,
        branch_name: this.booking.branch?.name,
        refund_amount: this.booking.totalPrice,
        cancelled_at: this.booking.cancelledAt?.toISO()
      }
    })
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const title = this.getCancellationTitle()
    const body = this.getCancellationMessage()

    return {
      title,
      body: body.replace('{firstName}', notifiable.firstName || notifiable.name),
      url: `aiauser://bookings/${this.booking.id}`,
      icon: 'https://cdn.verful.com/icons/verful-512x512.png',
      data: {
        booking_id: this.booking.id,
        cancelled_by: this.cancelledBy,
        type: 'booking_cancellation'
      }
    }
  }

  public toMail(notifiable: User) {
    const subject = this.getCancellationTitle()
    const appointmentDate = this.booking.appointmentStart.toFormat('EEEE, MMMM dd, yyyy')
    const appointmentTime = this.booking.appointmentStart.toFormat('h:mm a')

    const emailData = {
      greeting: `Hello ${notifiable.firstName || notifiable.name}!`,
      subject,
      intro: this.getEmailIntro(),
      cancellation_details: {
        confirmation_code: this.booking.confirmationCode,
        service_name: this.booking.product?.name,
        original_appointment_date: appointmentDate,
        original_appointment_time: appointmentTime,
        duration: `${this.booking.durationMinutes} minutes`,
        vendor_name: this.booking.vendor?.name,
        branch_name: this.booking.branch?.name,
        cancelled_by: this.getCancelledByText(),
        cancellation_reason: this.cancellationReason || 'No reason provided',
        cancelled_at: this.booking.cancelledAt?.toFormat('EEEE, MMMM dd, yyyy \'at\' h:mm a'),
        total_amount: `$${this.booking.totalPrice.toFixed(2)}`
      },
      refund_info: this.getRefundInfo(),
      rebooking_info: this.getRebookingInfo(),
      action_url: this.cancelledBy === 'customer' ? '/bookings/new' : `/bookings/${this.booking.id}`,
      action_label: this.cancelledBy === 'customer' ? 'Book Again' : 'View Details',
      footer_text: this.getFooterText()
    }

    return new CustomerMailer(notifiable, 'mails/booking/cancellation', emailData)
  }

  public toSms(notifiable: User) {
    if (!notifiable.phone) {
      throw new Error('Cannot send SMS to user without phone number')
    }

    const serviceName = this.booking.product?.name || 'service'
    const date = this.booking.appointmentStart.toFormat('MMM dd')
    const time = this.booking.appointmentStart.toFormat('h:mm a')
    const confirmationCode = this.booking.confirmationCode

    let text: string
    if (this.cancelledBy === 'vendor') {
      text = `CANCELLED: Hi ${notifiable.firstName || notifiable.name}, your appointment for ${serviceName} on ${date} at ${time} has been cancelled by the provider. Confirmation: ${confirmationCode}. You will receive a full refund.`
    } else if (this.cancelledBy === 'system') {
      text = `CANCELLED: Hi ${notifiable.firstName || notifiable.name}, your appointment for ${serviceName} on ${date} at ${time} has been cancelled due to system issues. Confirmation: ${confirmationCode}. Full refund will be processed.`
    } else {
      text = `CANCELLED: Hi ${notifiable.firstName || notifiable.name}, your appointment for ${serviceName} on ${date} at ${time} has been cancelled. Confirmation: ${confirmationCode}. Refund will be processed if applicable.`
    }

    return {
      text,
      phone: notifiable.phone.includes('+')
        ? notifiable.phone.replace(/^\+/, '')
        : notifiable.phone,
    }
  }

  private getCancellationTitle(): string {
    switch (this.cancelledBy) {
      case 'vendor':
        return 'Appointment Cancelled by Provider'
      case 'system':
        return 'Appointment Cancelled - System Issue'
      case 'customer':
      default:
        return 'Booking Cancellation Confirmed'
    }
  }

  private getCancellationMessage(): string {
    const serviceName = this.booking.product?.name || 'service'
    const date = this.booking.appointmentStart.toFormat('MMM dd')
    const time = this.booking.appointmentStart.toFormat('h:mm a')

    switch (this.cancelledBy) {
      case 'vendor':
        return `Your appointment for ${serviceName} on ${date} at ${time} has been cancelled by the provider. You will receive a full refund.`
      case 'system':
        return `Your appointment for ${serviceName} on ${date} at ${time} has been cancelled due to a system issue. A full refund will be processed automatically.`
      case 'customer':
      default:
        return `Your appointment for ${serviceName} on ${date} at ${time} has been successfully cancelled.`
    }
  }

  private getEmailIntro(): string {
    switch (this.cancelledBy) {
      case 'vendor':
        return 'We regret to inform you that your appointment has been cancelled by the service provider. We sincerely apologize for any inconvenience this may cause.'
      case 'system':
        return 'We regret to inform you that your appointment has been cancelled due to an unexpected system issue. We sincerely apologize for the inconvenience.'
      case 'customer':
      default:
        return 'Your booking cancellation has been processed successfully. We\'re sorry to see you go!'
    }
  }

  private getCancelledByText(): string {
    switch (this.cancelledBy) {
      case 'vendor':
        return 'Service Provider'
      case 'system':
        return 'System (Technical Issue)'
      case 'customer':
      default:
        return 'Customer'
    }
  }

  private getRefundInfo(): any {
    if (this.cancelledBy === 'vendor' || this.cancelledBy === 'system') {
      return {
        eligible: true,
        amount: `$${this.booking.totalPrice.toFixed(2)}`,
        processing_time: '3-5 business days',
        method: 'Original payment method',
        note: 'Full refund will be processed automatically'
      }
    } else {
      // Customer cancellation - refund policy applies
      return {
        eligible: true,
        amount: `$${this.booking.totalPrice.toFixed(2)}`,
        processing_time: '3-5 business days',
        method: 'Original payment method',
        note: 'Refund subject to cancellation policy terms'
      }
    }
  }

  private getRebookingInfo(): any {
    return {
      available: true,
      discount_offered: this.cancelledBy !== 'customer',
      discount_amount: this.cancelledBy !== 'customer' ? '10%' : null,
      contact_info: {
        phone: this.booking.branch?.phone || 'Contact customer service',
        email: '<EMAIL>'
      },
      note: this.cancelledBy !== 'customer' 
        ? 'As an apology, we\'re offering a 10% discount on your next booking.'
        : 'We\'d love to serve you again! Book your next appointment anytime.'
    }
  }

  private getFooterText(): string {
    switch (this.cancelledBy) {
      case 'vendor':
        return 'We deeply apologize for this cancellation and any inconvenience caused. Please contact us if you have any questions about your refund or would like to reschedule.'
      case 'system':
        return 'We apologize for the technical issue that caused this cancellation. Our team is working to prevent such issues in the future. Please contact us for immediate assistance.'
      case 'customer':
      default:
        return 'Thank you for choosing our services. We hope to serve you again in the future!'
    }
  }
}
