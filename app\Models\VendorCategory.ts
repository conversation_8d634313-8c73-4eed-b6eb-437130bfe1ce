import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { BaseModel, ManyToMany, beforeCreate, column, manyToMany } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import VendorCategoryFilter from './Filters/VendorCategoryFilter'
import CamelCaseStrategy from '../Strategies/CamelCaseStrategy'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Vendor from './Vendor'
import { ulid } from 'ulidx'

export default class VendorCategory extends compose(BaseModel, Filterable) {
  public static $filter = () => VendorCategoryFilter
  public static namingStrategy = new CamelCaseStrategy()

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public slug: string

  @column()
  public details: string

  @column()
  public vendorTypeId: string

  @attachment({ folder: 'categories', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(model: VendorCategory) {
    model.id = ulid().toLowerCase()
  }

  @manyToMany(() => Vendor, {
    pivotTable: 'vendor_set_categories',
    pivotTimestamps: true,
  })
  public vendors: ManyToMany<typeof Vendor>
}
