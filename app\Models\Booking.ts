import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate, belongsTo, BelongsTo, computed } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import Order from './Order'
import User from './User'
import Vendor from './Vendor'
import Branch from './Branch'
import Product from './Product'

export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

export interface SelectedServiceOption {
  id: string
  name: string
  type: string
  priceAdjustment: number
  durationId?: string
}

export interface StaffAssignment {
  userId: string
  role: string
  required: boolean
}

export interface EquipmentReservation {
  equipmentId: string
  name: string
  required: boolean
}

export default class Booking extends BaseModel {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public orderId: string | null

  @column()
  public customerId: string

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public productId: string

  @column.dateTime()
  public appointmentStart: DateTime

  @column.dateTime()
  public appointmentEnd: DateTime

  @column()
  public durationMinutes: number

  @column()
  public bufferMinutes: number

  @column()
  public status: BookingStatus

  @column()
  public staffAssignments: StaffAssignment[]

  @column()
  public equipmentReservations: EquipmentReservation[]

  @column()
  public selectedServiceOptions: SelectedServiceOption[]

  @column()
  public basePrice: number

  @column()
  public optionsTotal: number

  @column()
  public totalPrice: number

  @column()
  public bookingNotes: Record<string, any> | null

  @column()
  public internalNotes: Record<string, any> | null

  @column()
  public constraintsApplied: Record<string, any> | null

  @column.dateTime()
  public confirmedAt: DateTime | null

  @column()
  public confirmationCode: string | null

  @column()
  public reminderSent: boolean

  @column.dateTime()
  public reminderSentAt: DateTime | null

  @column.dateTime()
  public cancelledAt: DateTime | null

  @column()
  public cancelledBy: string | null

  @column()
  public cancellationReason: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(booking: Booking) {
    booking.id = ulid().toLowerCase()

    // Generate confirmation code
    if (!booking.confirmationCode) {
      booking.confirmationCode = this.generateConfirmationCode()
    }
  }

  // Relationships
  @belongsTo(() => Order)
  public order: BelongsTo<typeof Order>

  @belongsTo(() => User, { foreignKey: 'customerId' })
  public customer: BelongsTo<typeof User>

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @belongsTo(() => Product)
  public product: BelongsTo<typeof Product>

  @belongsTo(() => User, { foreignKey: 'cancelledBy' })
  public cancelledByUser: BelongsTo<typeof User>

  // Computed properties
  @computed()
  public get isActive(): boolean {
    return ![BookingStatus.CANCELLED, BookingStatus.COMPLETED, BookingStatus.NO_SHOW].includes(this.status)
  }

  @computed()
  public get isPending(): boolean {
    return this.status === BookingStatus.PENDING
  }

  @computed()
  public get isConfirmed(): boolean {
    return this.status === BookingStatus.CONFIRMED
  }

  @computed()
  public get isInProgress(): boolean {
    return this.status === BookingStatus.IN_PROGRESS
  }

  @computed()
  public get isCompleted(): boolean {
    return this.status === BookingStatus.COMPLETED
  }

  @computed()
  public get isCancelled(): boolean {
    return this.status === BookingStatus.CANCELLED
  }

  @computed()
  public get totalDurationMinutes(): number {
    return this.durationMinutes + this.bufferMinutes
  }

  @computed()
  public get appointmentDate(): string {
    return this.appointmentStart.toFormat('yyyy-MM-dd')
  }

  @computed()
  public get appointmentTimeRange(): string {
    const start = this.appointmentStart.toFormat('HH:mm')
    const end = this.appointmentEnd.toFormat('HH:mm')
    return `${start} - ${end}`
  }

  @computed()
  public get isUpcoming(): boolean {
    return this.appointmentStart > DateTime.now()
  }

  @computed()
  public get isPast(): boolean {
    return this.appointmentEnd < DateTime.now()
  }

  @computed()
  public get requiresStaff(): boolean {
    return this.staffAssignments && this.staffAssignments.length > 0
  }

  @computed()
  public get requiresEquipment(): boolean {
    return this.equipmentReservations && this.equipmentReservations.length > 0
  }

  // Static methods
  public static generateConfirmationCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // Business logic methods
  public async confirm(): Promise<void> {
    if (this.status !== BookingStatus.PENDING) {
      throw new Error(`Cannot confirm booking with status: ${this.status}`)
    }

    this.status = BookingStatus.CONFIRMED
    this.confirmedAt = DateTime.now()
    await this.save()
  }

  public async startService(): Promise<void> {
    if (this.status !== BookingStatus.CONFIRMED) {
      throw new Error(`Cannot start service for booking with status: ${this.status}`)
    }

    this.status = BookingStatus.IN_PROGRESS
    await this.save()
  }

  public async completeService(): Promise<void> {
    if (this.status !== BookingStatus.IN_PROGRESS) {
      throw new Error(`Cannot complete service for booking with status: ${this.status}`)
    }

    this.status = BookingStatus.COMPLETED
    await this.save()
  }

  public async cancel(cancelledBy: string, reason?: string): Promise<void> {
    if ([BookingStatus.COMPLETED, BookingStatus.CANCELLED].includes(this.status)) {
      throw new Error(`Cannot cancel booking with status: ${this.status}`)
    }

    this.status = BookingStatus.CANCELLED
    this.cancelledAt = DateTime.now()
    this.cancelledBy = cancelledBy
    this.cancellationReason = reason || null
    await this.save()
  }

  public async markNoShow(): Promise<void> {
    if (this.status !== BookingStatus.CONFIRMED) {
      throw new Error(`Cannot mark no-show for booking with status: ${this.status}`)
    }

    this.status = BookingStatus.NO_SHOW
    await this.save()
  }

  public canBeCancelled(): boolean {
    return ![BookingStatus.COMPLETED, BookingStatus.CANCELLED, BookingStatus.NO_SHOW].includes(this.status)
  }

  public canBeModified(): boolean {
    return [BookingStatus.PENDING, BookingStatus.CONFIRMED].includes(this.status) && this.isUpcoming
  }

  public getRequiredStaffIds(): string[] {
    return this.staffAssignments?.filter(assignment => assignment.required).map(assignment => assignment.userId) || []
  }

  public getRequiredEquipmentIds(): string[] {
    return this.equipmentReservations?.filter(reservation => reservation.required).map(reservation => reservation.equipmentId) || []
  }

  public getDurationOptionIds(): string[] {
    return this.selectedServiceOptions?.filter(option => option.type === 'duration').map(option => option.id) || []
  }

  public calculateOptionsTotal(): number {
    return this.selectedServiceOptions?.reduce((total, option) => total + option.priceAdjustment, 0) || 0
  }

  public async validateBooking(): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Validate appointment times
    if (this.appointmentStart >= this.appointmentEnd) {
      errors.push('Appointment start time must be before end time')
    }

    // Validate duration
    const calculatedDuration = this.appointmentEnd.diff(this.appointmentStart, 'minutes').minutes
    if (Math.abs(calculatedDuration - this.durationMinutes) > 1) {
      errors.push('Duration minutes does not match appointment time range')
    }

    // Validate pricing
    const calculatedOptionsTotal = this.calculateOptionsTotal()
    if (Math.abs(this.optionsTotal - calculatedOptionsTotal) > 0.01) {
      errors.push('Options total does not match selected service options')
    }

    const calculatedTotal = this.basePrice + this.optionsTotal
    if (Math.abs(this.totalPrice - calculatedTotal) > 0.01) {
      errors.push('Total price calculation is incorrect')
    }

    // Validate future appointments
    if (this.appointmentStart <= DateTime.now() && this.status === BookingStatus.PENDING) {
      errors.push('Cannot create booking for past appointment time')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }
}
