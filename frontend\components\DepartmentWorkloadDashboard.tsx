import React, { useState, useEffect, useCallback } from 'react'
import { Drag<PERSON>rop<PERSON>ontext, Droppable, Draggable, DropR<PERSON>ult } from 'react-beautiful-dnd'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Clock, Users, AlertTriangle, CheckCircle, Timer, User } from 'lucide-react'
import { useWebSocket } from '@/hooks/useWebSocket'
import { useDepartmentWorkload } from '@/hooks/useDepartmentWorkload'
import { formatDistanceToNow } from 'date-fns'

interface OrderItem {
  id: number
  order_id: string
  product_name: string
  status: 'pending' | 'preparing' | 'ready' | 'served'
  assigned_staff_id?: string
  assigned_staff_name?: string
  estimated_preparation_time?: number
  preparation_started_at?: string
  priority_level: number
  special_instructions?: string
  is_overdue: boolean
  overdue_minutes?: number
  modifiers: OrderItemModifier[]
}

interface OrderItemModifier {
  id: number
  name: string
  status: 'pending' | 'preparing' | 'completed' | 'skipped' | 'failed'
  complexity_level: number
  requires_special_skill: boolean
}

interface StaffMember {
  id: string
  name: string
  current_workload: number
  max_workload: number
  skill_level: number
  active_items: number
  efficiency_score: number
}

interface DepartmentStats {
  total_items: number
  pending_items: number
  preparing_items: number
  ready_items: number
  overdue_items: number
  average_preparation_time: number
  completion_rate: number
  capacity_utilization: number
}

interface DepartmentWorkloadDashboardProps {
  departmentId: string
  departmentName: string
  vendorId: string
  branchId?: string
  refreshInterval?: number
}

export const DepartmentWorkloadDashboard: React.FC<DepartmentWorkloadDashboardProps> = ({
  departmentId,
  departmentName,
  vendorId,
  branchId,
  refreshInterval = 30000
}) => {
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedStaff, setSelectedStaff] = useState<string | null>(null)
  const [draggedItem, setDraggedItem] = useState<OrderItem | null>(null)

  // Custom hooks for data management
  const {
    pendingItems,
    preparingItems,
    readyItems,
    staffMembers,
    departmentStats,
    loading,
    error,
    updateItemStatus,
    assignItemToStaff,
    refreshData
  } = useDepartmentWorkload(departmentId, refreshInterval)

  // WebSocket integration for real-time updates
  const { subscribe, unsubscribe } = useWebSocket()

  useEffect(() => {
    // Subscribe to department-specific updates
    const subscriptions = [
      subscribe('department', departmentId),
      subscribe('vendor', vendorId, 'staff'),
    ]

    if (branchId) {
      subscriptions.push(subscribe('branch', branchId, 'staff'))
    }

    return () => {
      subscriptions.forEach(unsubscribe)
    }
  }, [departmentId, vendorId, branchId, subscribe, unsubscribe])

  // Handle drag and drop for status updates
  const handleDragEnd = useCallback(async (result: DropResult) => {
    const { destination, source, draggableId } = result

    if (!destination) return

    const itemId = parseInt(draggableId.split('-')[1])
    const newStatus = destination.droppableId as OrderItem['status']
    const oldStatus = source.droppableId as OrderItem['status']

    if (newStatus === oldStatus) return

    try {
      await updateItemStatus(itemId, newStatus, {
        updated_via: 'drag_drop',
        assigned_staff_id: selectedStaff
      })
    } catch (error) {
      console.error('Failed to update item status:', error)
      // Handle error - could show toast notification
    }
  }, [updateItemStatus, selectedStaff])

  // Handle staff assignment
  const handleStaffAssignment = useCallback(async (itemId: number, staffId: string) => {
    try {
      await assignItemToStaff(itemId, staffId)
      setSelectedStaff(null)
    } catch (error) {
      console.error('Failed to assign staff:', error)
    }
  }, [assignItemToStaff])

  // Get priority color
  const getPriorityColor = (priority: number, isOverdue: boolean) => {
    if (isOverdue) return 'destructive'
    if (priority === 1) return 'destructive'
    if (priority === 2) return 'warning'
    return 'default'
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary'
      case 'preparing': return 'warning'
      case 'ready': return 'success'
      case 'served': return 'default'
      default: return 'default'
    }
  }

  // Render order item card
  const renderOrderItem = (item: OrderItem, index: number) => (
    <Draggable key={`item-${item.id}`} draggableId={`item-${item.id}`} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-2 ${snapshot.isDragging ? 'opacity-50' : ''}`}
        >
          <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-3">
              <div className="flex justify-between items-start mb-2">
                <div className="flex-1">
                  <h4 className="font-medium text-sm">{item.product_name}</h4>
                  <p className="text-xs text-muted-foreground">Order #{item.order_id}</p>
                </div>
                <div className="flex gap-1">
                  <Badge variant={getPriorityColor(item.priority_level, item.is_overdue)}>
                    {item.is_overdue ? `${item.overdue_minutes}m overdue` : `P${item.priority_level}`}
                  </Badge>
                  {item.estimated_preparation_time && (
                    <Badge variant="outline">
                      <Timer className="w-3 h-3 mr-1" />
                      {item.estimated_preparation_time}m
                    </Badge>
                  )}
                </div>
              </div>

              {item.assigned_staff_name && (
                <div className="flex items-center gap-1 mb-2">
                  <User className="w-3 h-3" />
                  <span className="text-xs">{item.assigned_staff_name}</span>
                </div>
              )}

              {item.preparation_started_at && (
                <div className="flex items-center gap-1 mb-2">
                  <Clock className="w-3 h-3" />
                  <span className="text-xs">
                    Started {formatDistanceToNow(new Date(item.preparation_started_at))} ago
                  </span>
                </div>
              )}

              {item.special_instructions && (
                <p className="text-xs text-orange-600 mb-2">{item.special_instructions}</p>
              )}

              {item.modifiers.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {item.modifiers.map(modifier => (
                    <Badge
                      key={modifier.id}
                      variant={getStatusColor(modifier.status)}
                      className="text-xs"
                    >
                      {modifier.name}
                    </Badge>
                  ))}
                </div>
              )}

              {!item.assigned_staff_id && item.status === 'pending' && (
                <div className="mt-2 flex flex-wrap gap-1">
                  {staffMembers.map(staff => (
                    <Button
                      key={staff.id}
                      size="sm"
                      variant="outline"
                      onClick={() => handleStaffAssignment(item.id, staff.id)}
                      disabled={staff.current_workload >= staff.max_workload}
                    >
                      {staff.name} ({staff.current_workload}/{staff.max_workload})
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </Draggable>
  )

  // Render status column
  const renderStatusColumn = (title: string, items: OrderItem[], status: string, icon: React.ReactNode) => (
    <div className="flex-1 min-w-0">
      <div className="flex items-center gap-2 mb-3">
        {icon}
        <h3 className="font-medium">{title}</h3>
        <Badge variant="secondary">{items.length}</Badge>
      </div>
      <Droppable droppableId={status}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`min-h-[200px] p-2 rounded-lg border-2 border-dashed transition-colors ${
              snapshot.isDraggingOver ? 'border-primary bg-primary/5' : 'border-muted'
            }`}
          >
            {items.map((item, index) => renderOrderItem(item, index))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-destructive mb-4">Error loading department data: {error}</p>
        <Button onClick={refreshData}>Retry</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Department Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">{departmentName} Dashboard</h1>
          <p className="text-muted-foreground">Real-time workload management</p>
        </div>
        <Button onClick={refreshData} variant="outline">
          Refresh Data
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Total Items</p>
                <p className="text-2xl font-bold">{departmentStats.total_items}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-red-500" />
              <div>
                <p className="text-sm text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-500">{departmentStats.overdue_items}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Staff Active</p>
                <p className="text-2xl font-bold">{staffMembers.filter(s => s.active_items > 0).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Completion Rate</p>
                <p className="text-2xl font-bold">{departmentStats.completion_rate}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Capacity Utilization */}
      <Card>
        <CardHeader>
          <h3 className="font-medium">Department Capacity</h3>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Capacity Utilization</span>
              <span>{departmentStats.capacity_utilization}%</span>
            </div>
            <Progress value={departmentStats.capacity_utilization} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Main Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Workflow Overview</TabsTrigger>
          <TabsTrigger value="staff">Staff Management</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <DragDropContext onDragEnd={handleDragEnd}>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {renderStatusColumn(
                'Pending',
                pendingItems,
                'pending',
                <Clock className="w-4 h-4 text-gray-500" />
              )}
              {renderStatusColumn(
                'Preparing',
                preparingItems,
                'preparing',
                <Timer className="w-4 h-4 text-yellow-500" />
              )}
              {renderStatusColumn(
                'Ready',
                readyItems,
                'ready',
                <CheckCircle className="w-4 h-4 text-green-500" />
              )}
              {renderStatusColumn(
                'Served',
                [],
                'served',
                <CheckCircle className="w-4 h-4 text-blue-500" />
              )}
            </div>
          </DragDropContext>
        </TabsContent>

        <TabsContent value="staff" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {staffMembers.map(staff => (
              <Card key={staff.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium">{staff.name}</h4>
                      <p className="text-sm text-muted-foreground">Skill Level {staff.skill_level}</p>
                    </div>
                    <Badge variant={staff.current_workload >= staff.max_workload ? 'destructive' : 'default'}>
                      {staff.current_workload}/{staff.max_workload}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Workload</span>
                      <span>{Math.round((staff.current_workload / staff.max_workload) * 100)}%</span>
                    </div>
                    <Progress
                      value={(staff.current_workload / staff.max_workload) * 100}
                      className="h-2"
                    />
                  </div>

                  <div className="mt-3 flex justify-between text-sm">
                    <span>Active Items: {staff.active_items}</span>
                    <span>Efficiency: {staff.efficiency_score}%</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <h3 className="font-medium">Performance Metrics</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Average Prep Time</span>
                  <span>{departmentStats.average_preparation_time} min</span>
                </div>
                <div className="flex justify-between">
                  <span>Completion Rate</span>
                  <span>{departmentStats.completion_rate}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Items per Hour</span>
                  <span>{Math.round(departmentStats.total_items / 8)}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <h3 className="font-medium">Current Status</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Pending Items</span>
                  <Badge variant="secondary">{departmentStats.pending_items}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Preparing Items</span>
                  <Badge variant="warning">{departmentStats.preparing_items}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Ready Items</span>
                  <Badge variant="success">{departmentStats.ready_items}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default DepartmentWorkloadDashboard

// Export types for use in other components
export type {
  OrderItem,
  OrderItemModifier,
  StaffMember,
  DepartmentStats,
  DepartmentWorkloadDashboardProps
}
