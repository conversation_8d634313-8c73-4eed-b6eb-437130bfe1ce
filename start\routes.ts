/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes.ts` as follows
|
| import './routes/cart'
| import './routes/customer'
|
*/


import './routes/v1/admin'
import './routes/v1/docs'
import './routes/v1/auth'
import './routes/v1/kplc'
import './routes/v1/resources'
import './routes/v1/flights'
import './routes/v1/websocket'
