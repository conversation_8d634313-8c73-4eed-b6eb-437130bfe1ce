{"extends": "adonis-preset-ts/tsconfig.json", "include": ["**/*"], "exclude": ["node_modules", "dist"], "compilerOptions": {"outDir": "dist", "rootDir": "./", "sourceMap": true, "resolveJsonModule": true, "paths": {"App/*": ["./app/*"], "Config/*": ["./config/*"], "Contracts/*": ["./contracts/*"], "Database/*": ["./database/*"], "Commands/*": ["./commands/*"]}, "types": ["@adonisjs/core", "@adonisjs/repl", "@japa/preset-adonis/build/adonis-typings", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/ally", "@adonisjs/route-model-binding/build/adonis-typings", "@adonisjs/mail", "@adonisjs/view", "@adonisjs/attachment-lite", "@verful/notifications", "@verful/permissions", "@melchyore/adonis-lucid-observer", "adonis-lucid-filter", "adonis-lucid-soft-deletes", "adonis-fcm", "@adonisjs/redis", "@adonisjs/drive-s3", "@adonisjs/i18n", "adonisjs-scheduler", "@bitkidd/adonis-ally-apple", "@rlanz/bull-queue"]}}