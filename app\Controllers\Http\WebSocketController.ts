import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import WebSocketManager from '../../Services/WebSocketManager'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { DateTime } from 'luxon'

/**
 * Controller for WebSocket management and monitoring
 */
export default class WebSocketController {

  /**
   * @summary Get WebSocket server status
   * @description Get current status and statistics of the WebSocket server
   * @responseBody 200 - WebSocket server status
   */
  public async status({ response }: HttpContextContract) {
    try {
      const stats = WebSocketManager.getStats()
      
      return response.json({
        status: 'success',
        websocket_server: stats,
        timestamp: DateTime.now().toISO()
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to get WebSocket status',
        details: error.message
      })
    }
  }

  /**
   * @summary Broadcast test message
   * @description Send a test message to WebSocket clients (admin only)
   * @requestBody Test message data
   * @responseBody 200 - Broadcast result
   */
  public async broadcastTest({ request, response, auth }: HttpContextContract) {
    // Check if user is admin
    const user = auth.user
    if (!user) {
      return response.unauthorized({ error: 'Authentication required' })
    }

    // Load user roles
    await user.load('roles')
    const isAdmin = user.roles.some(role => role.name === 'admin')
    
    if (!isAdmin) {
      return response.forbidden({ error: 'Admin access required' })
    }

    const validationSchema = schema.create({
      room: schema.string({ trim: true }),
      event: schema.string({ trim: true }),
      message: schema.string({ trim: true }),
      data: schema.object.optional().anyMembers()
    })

    const { room, event, message, data = {} } = await request.validate({ schema: validationSchema })

    try {
      const testData = {
        type: 'test_message',
        message,
        sent_by: user.name,
        sent_by_id: user.id,
        timestamp: DateTime.now().toISO(),
        ...data
      }

      WebSocketManager.broadcastToRoom(room, event, testData)

      return response.json({
        status: 'success',
        message: 'Test message broadcasted successfully',
        broadcast_details: {
          room,
          event,
          message,
          sent_by: user.name,
          timestamp: testData.timestamp
        }
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to broadcast test message',
        details: error.message
      })
    }
  }

  /**
   * @summary Send test message to user
   * @description Send a test message to a specific user (admin only)
   * @requestBody Test message data with user ID
   * @responseBody 200 - Send result
   */
  public async sendTestToUser({ request, response, auth }: HttpContextContract) {
    // Check if user is admin
    const user = auth.user
    if (!user) {
      return response.unauthorized({ error: 'Authentication required' })
    }

    await user.load('roles')
    const isAdmin = user.roles.some(role => role.name === 'admin')
    
    if (!isAdmin) {
      return response.forbidden({ error: 'Admin access required' })
    }

    const validationSchema = schema.create({
      user_id: schema.string({ trim: true }),
      event: schema.string({ trim: true }),
      message: schema.string({ trim: true }),
      data: schema.object.optional().anyMembers()
    })

    const { user_id, event, message, data = {} } = await request.validate({ schema: validationSchema })

    try {
      const testData = {
        type: 'test_message',
        message,
        sent_by: user.name,
        sent_by_id: user.id,
        timestamp: DateTime.now().toISO(),
        ...data
      }

      WebSocketManager.sendToUser(user_id, event, testData)

      return response.json({
        status: 'success',
        message: 'Test message sent successfully',
        send_details: {
          user_id,
          event,
          message,
          sent_by: user.name,
          timestamp: testData.timestamp
        }
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to send test message',
        details: error.message
      })
    }
  }

  /**
   * @summary Trigger order status broadcast
   * @description Manually trigger an order status update broadcast (testing/admin)
   * @requestBody Order status update data
   * @responseBody 200 - Broadcast result
   */
  public async triggerOrderStatusBroadcast({ request, response, auth }: HttpContextContract) {
    const user = auth.user
    if (!user) {
      return response.unauthorized({ error: 'Authentication required' })
    }

    const validationSchema = schema.create({
      order_id: schema.string({ trim: true }),
      vendor_id: schema.string({ trim: true }),
      branch_id: schema.string.optional({ trim: true }),
      customer_id: schema.string.optional({ trim: true }),
      status: schema.string({ trim: true }),
      previous_status: schema.string({ trim: true })
    })

    const { 
      order_id, 
      vendor_id, 
      branch_id, 
      customer_id, 
      status, 
      previous_status 
    } = await request.validate({ schema: validationSchema })

    try {
      WebSocketManager.broadcastOrderStatusUpdate({
        orderId: order_id,
        vendorId: vendor_id,
        branchId: branch_id,
        customerId: customer_id,
        status,
        previousStatus: previous_status,
        updatedBy: user.name,
        timestamp: DateTime.now()
      })

      return response.json({
        status: 'success',
        message: 'Order status broadcast triggered successfully',
        broadcast_details: {
          order_id,
          status,
          previous_status,
          triggered_by: user.name,
          timestamp: DateTime.now().toISO()
        }
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to trigger order status broadcast',
        details: error.message
      })
    }
  }

  /**
   * @summary Trigger item status broadcast
   * @description Manually trigger an item status update broadcast (testing/admin)
   * @requestBody Item status update data
   * @responseBody 200 - Broadcast result
   */
  public async triggerItemStatusBroadcast({ request, response, auth }: HttpContextContract) {
    const user = auth.user
    if (!user) {
      return response.unauthorized({ error: 'Authentication required' })
    }

    const validationSchema = schema.create({
      order_id: schema.string({ trim: true }),
      item_id: schema.number(),
      vendor_id: schema.string({ trim: true }),
      branch_id: schema.string.optional({ trim: true }),
      department_id: schema.string.optional({ trim: true }),
      status: schema.string({ trim: true }),
      previous_status: schema.string({ trim: true }),
      assigned_staff_id: schema.string.optional({ trim: true })
    })

    const { 
      order_id, 
      item_id, 
      vendor_id, 
      branch_id, 
      department_id, 
      status, 
      previous_status,
      assigned_staff_id
    } = await request.validate({ schema: validationSchema })

    try {
      WebSocketManager.broadcastItemStatusUpdate({
        orderId: order_id,
        itemId: item_id,
        vendorId: vendor_id,
        branchId: branch_id,
        departmentId: department_id,
        status,
        previousStatus: previous_status,
        assignedStaffId: assigned_staff_id,
        updatedBy: user.name,
        timestamp: DateTime.now()
      })

      return response.json({
        status: 'success',
        message: 'Item status broadcast triggered successfully',
        broadcast_details: {
          order_id,
          item_id,
          status,
          previous_status,
          triggered_by: user.name,
          timestamp: DateTime.now().toISO()
        }
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to trigger item status broadcast',
        details: error.message
      })
    }
  }

  /**
   * @summary Trigger overdue alert
   * @description Manually trigger an overdue item alert (testing/admin)
   * @requestBody Overdue alert data
   * @responseBody 200 - Alert result
   */
  public async triggerOverdueAlert({ request, response, auth }: HttpContextContract) {
    const user = auth.user
    if (!user) {
      return response.unauthorized({ error: 'Authentication required' })
    }

    const validationSchema = schema.create({
      order_id: schema.string({ trim: true }),
      item_id: schema.number(),
      vendor_id: schema.string({ trim: true }),
      branch_id: schema.string.optional({ trim: true }),
      department_id: schema.string.optional({ trim: true }),
      assigned_staff_id: schema.string.optional({ trim: true }),
      overdue_minutes: schema.number(),
      priority_level: schema.enum(['low', 'medium', 'high', 'critical'])
    })

    const { 
      order_id, 
      item_id, 
      vendor_id, 
      branch_id, 
      department_id, 
      assigned_staff_id,
      overdue_minutes,
      priority_level
    } = await request.validate({ schema: validationSchema })

    try {
      WebSocketManager.broadcastOverdueAlert({
        orderId: order_id,
        itemId: item_id,
        vendorId: vendor_id,
        branchId: branch_id,
        departmentId: department_id,
        assignedStaffId: assigned_staff_id,
        overdueMinutes: overdue_minutes,
        priorityLevel: priority_level,
        timestamp: DateTime.now()
      })

      return response.json({
        status: 'success',
        message: 'Overdue alert triggered successfully',
        alert_details: {
          order_id,
          item_id,
          overdue_minutes,
          priority_level,
          triggered_by: user.name,
          timestamp: DateTime.now().toISO()
        }
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to trigger overdue alert',
        details: error.message
      })
    }
  }

  /**
   * @summary Get WebSocket health check
   * @description Simple health check endpoint for WebSocket server
   * @responseBody 200 - Health status
   */
  public async health({ response }: HttpContextContract) {
    try {
      const isReady = WebSocketManager.isReady()
      
      return response.json({
        status: isReady ? 'healthy' : 'unhealthy',
        websocket_initialized: isReady,
        timestamp: DateTime.now().toISO()
      })
    } catch (error) {
      return response.status(500).json({
        status: 'unhealthy',
        error: error.message,
        timestamp: DateTime.now().toISO()
      })
    }
  }
}
