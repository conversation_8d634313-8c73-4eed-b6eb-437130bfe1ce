import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption from 'App/Models/ServiceConfigurationOption'
// import CreateServiceConfigurationValidator from 'App/Validators/CreateServiceConfigurationValidator'
// import UpdateServiceConfigurationValidator from 'App/Validators/UpdateServiceConfigurationValidator'

export default class ServiceConfigurationsController {
  /**
   * Display a list of service configurations with filtering and pagination
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 20)
      const serviceId = request.input('serviceId')
      const active = request.input('active')
      const search = request.input('search')

      const query = ServiceConfiguration.query()

      // Apply filters
      if (serviceId) {
        query.where('serviceId', serviceId)
      }

      if (active !== undefined) {
        query.where('active', active === 'true')
      }

      if (search) {
        query.where((builder) => {
          builder
            .where('name', 'ILIKE', `%${search}%`)
            .orWhere('description', 'ILIKE', `%${search}%`)
        })
      }

      // Include related data and counts
      query
        .preload('service')
        .withCount('options')
        .withCount('options', (query) => {
          query.where('active', true).as('activeOptionCount')
        })
        .withCount('options', (query) => {
          query.where('type', 'duration').as('durationOptionCount')
        })
        .orderBy('name')

      const configurations = await query.paginate(page, limit)

      return response.json({
        success: true,
        data: configurations.toJSON(),
        meta: {
          total: configurations.total,
          perPage: configurations.perPage,
          currentPage: configurations.currentPage,
          lastPage: configurations.lastPage,
          hasMorePages: configurations.hasMorePages
        }
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch service configurations',
        error: error.message
      })
    }
  }

  /**
   * Create a new service configuration
   */
  public async store({ request, response }: HttpContextContract) {
    try {
      const payload = request.only(['name', 'description', 'serviceId', 'active'])
      
      const configuration = await ServiceConfiguration.create(payload)

      // Load related data for response
      await configuration.load('service')

      return response.status(201).json({
        success: true,
        message: 'Service configuration created successfully',
        data: configuration
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create service configuration',
        error: error.message
      })
    }
  }

  /**
   * Display a specific service configuration with options
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const configuration = await ServiceConfiguration.query()
        .where('id', params.id)
        .preload('service')
        .preload('options', (query) => {
          query.where('active', true).preload('duration').orderBy('sortOrder')
        })
        .firstOrFail()

      const optionsByType = await configuration.getOptionsByType()
      const statistics = await configuration.getStatistics()
      const validation = await configuration.validateConfiguration()

      return response.json({
        success: true,
        data: {
          ...configuration.toJSON(),
          optionsByType,
          statistics,
          validation
        }
      })
    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Service configuration not found',
        error: error.message
      })
    }
  }

  /**
   * Update a service configuration
   */
  public async update({ params, request, response }: HttpContextContract) {
    try {
      const configuration = await ServiceConfiguration.findOrFail(params.id)
      const payload = request.only(['name', 'description', 'active'])

      configuration.merge(payload)
      await configuration.save()

      // Load related data for response
      await configuration.load('service')

      return response.json({
        success: true,
        message: 'Service configuration updated successfully',
        data: configuration
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to update service configuration',
        error: error.message
      })
    }
  }

  /**
   * Delete a service configuration
   */
  public async destroy({ params, response }: HttpContextContract) {
    try {
      const configuration = await ServiceConfiguration.findOrFail(params.id)
      
      // Check if configuration is being used by products
      // TODO: Add check for products using this configuration
      
      // Soft delete by setting active to false
      configuration.active = false
      await configuration.save()

      // Also deactivate all options
      await ServiceConfigurationOption.query()
        .where('serviceConfigurationId', configuration.id)
        .update({ active: false })

      return response.json({
        success: true,
        message: 'Service configuration deactivated successfully'
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to delete service configuration',
        error: error.message
      })
    }
  }

  /**
   * Clone a service configuration
   */
  public async clone({ params, request, response }: HttpContextContract) {
    try {
      const configuration = await ServiceConfiguration.findOrFail(params.id)
      const { name, description } = request.only(['name', 'description'])

      if (!name) {
        return response.status(400).json({
          success: false,
          message: 'Name is required for cloning'
        })
      }

      const clonedConfiguration = await configuration.clone(name, description)
      await clonedConfiguration.load('service')

      return response.status(201).json({
        success: true,
        message: 'Service configuration cloned successfully',
        data: clonedConfiguration
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to clone service configuration',
        error: error.message
      })
    }
  }

  /**
   * Create configuration from template
   */
  public async createFromTemplate({ request, response }: HttpContextContract) {
    try {
      const { templateType, name, serviceId, options } = request.only(['templateType', 'name', 'serviceId', 'options'])
      
      if (!templateType || !name || !serviceId) {
        return response.status(400).json({
          success: false,
          message: 'Template type, name, and service ID are required'
        })
      }

      const template = ServiceConfiguration.createTemplate(name, serviceId, templateType, options || {})
      const configuration = await ServiceConfiguration.create(template)

      await configuration.load('service')

      return response.status(201).json({
        success: true,
        message: 'Service configuration created from template successfully',
        data: configuration
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to create configuration from template',
        error: error.message
      })
    }
  }

  /**
   * Validate service configuration
   */
  public async validate({ params, response }: HttpContextContract) {
    try {
      const configuration = await ServiceConfiguration.findOrFail(params.id)
      const validation = await configuration.validateConfiguration()

      return response.json({
        success: true,
        data: validation
      })
    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Service configuration not found',
        error: error.message
      })
    }
  }

  /**
   * Get configuration statistics
   */
  public async statistics({ params, response }: HttpContextContract) {
    try {
      const configuration = await ServiceConfiguration.findOrFail(params.id)
      const statistics = await configuration.getStatistics()

      return response.json({
        success: true,
        data: statistics
      })
    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Service configuration not found',
        error: error.message
      })
    }
  }

  /**
   * Search configurations
   */
  public async search({ request, response }: HttpContextContract) {
    try {
      const { query, serviceId } = request.only(['query', 'serviceId'])

      if (!query) {
        return response.status(400).json({
          success: false,
          message: 'Search query is required'
        })
      }

      const configurations = await ServiceConfiguration.search(query, serviceId)

      return response.json({
        success: true,
        data: configurations
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to search configurations',
        error: error.message
      })
    }
  }
}
