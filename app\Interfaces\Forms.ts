export interface FormField {
  id: string | number
  name: string
  label: string
  defaultValue: string
  type: string
  placeholder: string
  required: boolean
  attrs?: Record<string, string | number | boolean>
  options?: Record<string, string | number>[]
  repeatable?: boolean
  repeats?: number
  min?: number
  max?: number
  hasCost?: boolean
  cost?: number
  multiple?: boolean
}

export interface FormSection {
  id: string | number
  name: string
  details: string
  skippable?: boolean
  repeatable?: boolean
  repeats?: number
  hasCost?: boolean
  cost?: number
  fields: FormField[]
}
