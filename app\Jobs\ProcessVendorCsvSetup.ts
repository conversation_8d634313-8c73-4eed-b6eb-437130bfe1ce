// app/Jobs/ProcessVendorCsvSetup.ts
import type { JobHandlerContract, Job } from '@ioc:Rlanz/Queue'
import Drive from '@ioc:Adonis/Core/Drive'
import csv from 'csv-parser'
import Database from '@ioc:Adonis/Lucid/Database'
import { schema, rules, validator } from '@ioc:Adonis/Core/Validator'
import User from 'App/Models/User'
import Vendor from 'App/Models/Vendor'
import Branch from 'App/Models/Branch'
import VendorCategory from 'App/Models/VendorCategory'
import Service from 'App/Models/Service'
import { string } from '@ioc:Adonis/Core/Helpers' // For header normalization
import { DateTime } from 'luxon'
import { ulid } from 'ulidx'

// Define the structure of the job payload
export type ProcessVendorCsvSetupPayload = {
  s3ObjectKey: string
  diskName: 's3'
  userId: string // Admin user who initiated
}

// Interface for the structured report returned by the job
interface JobReport {
  totalRows: number
  successfulRows: number
  failedRows: number
  errors: Array<{ row: number; errors: any; data: any }>
  timestamp: string
}

// Define expected headers (lowercase, snake_case)
const EXPECTED_HEADERS = [
  'first_name',
  'last_name',
  'email',
  'phone',
  'title',
  'gender',
  'dob',
  'idpass',
  'vendor_name',
  'vendor_kra',
  'vendor_details',
  'vendor_email',
  'vendor_phone',
  'vendor_reg',
  'vendor_permit',
  'vendor_category_ids',
  'vendor_category_slugs',
  'service_id',
  'service_slug',
  'branch_name',
  'branch_location_address',
  'branch_location_lat',
  'branch_location_lng',
  'branch_details',
  'branch_email',
  'branch_phone',
  'staff_identifier',
].map((h) => h.toLowerCase()) // Ensure comparison is case-insensitive later

export default class ProcessVendorCsvSetup implements JobHandlerContract {
  constructor(public job: Job) {}

  /**
   * Main job handler
   */
  public async handle(payload: ProcessVendorCsvSetupPayload): Promise<void> {
    const { s3ObjectKey, diskName } = payload
    console.log(`[Job ${this.job.id}] Starting CSV processing: ${s3ObjectKey}`)

    const report: JobReport = {
      totalRows: 0,
      successfulRows: 0,
      failedRows: 0,
      errors: [],
      timestamp: '',
    }

    let fileStream: NodeJS.ReadableStream
    try {
      fileStream = await Drive.use(diskName).getStream(s3ObjectKey)
    } catch (driveError) {
      console.error(
        `[Job ${this.job.id}] FATAL: Failed to get S3 stream ${s3ObjectKey}:`,
        driveError
      )
      report.errors.push({
        row: 0,
        errors: { file: `S3 stream error: ${driveError.message}` },
        data: {},
      })
      report.timestamp = new Date().toISOString()
      // Ensure cleanup happens even on stream failure
      await this.cleanupS3File(diskName, s3ObjectKey)
      // Re-throw to make the job fail in BullMQ
      throw new Error(`S3 stream error prevented job processing: ${driveError.message}`)
    }

    const results: any[] = []
    // let headersVerified = false // Currently not used in validation logic
    let headerErrors: string[] = []

    return new Promise((resolve, reject) => {
      fileStream
        .pipe(
          csv({
            mapHeaders: ({ header }) => string.snakeCase(header.trim().toLowerCase()), // Normalize headers to lowercase snake_case
            skipComments: true,
          })
        )
        .on('headers', (headers: string[]) => {
          console.log(`[Job ${this.job.id}] Parsed Headers:`, headers)
          // Basic header check: ensure all required headers are present
          const requiredHeaders = [
            'first_name',
            'last_name',
            'email',
            'phone',
            'vendor_name',
            'vendor_kra',
          ]
          const missingRequired = requiredHeaders.filter((h) => !headers.includes(h))

          if (missingRequired.length > 0) {
            headerErrors.push(`Missing required headers: ${missingRequired.join(', ')}`)
            console.error(
              `[Job ${this.job.id}] Header Validation Failed: Missing required headers: ${missingRequired.join(', ')}`
            )
            // Don't reject immediately, let it finish reading if possible, but mark as failed later
          } else {
            // Check for unexpected headers (optional, but good practice)
            const unexpectedHeaders = headers.filter((h) => !EXPECTED_HEADERS.includes(h))
            if (unexpectedHeaders.length > 0) {
              console.warn(
                `[Job ${this.job.id}] Warning: Unexpected headers found: ${unexpectedHeaders.join(', ')}. These columns will be ignored.`
              )
              // headerErrors.push(`Unexpected headers found: ${unexpectedHeaders.join(', ')}`); // Optionally report as error
            }
            // headersVerified = true // Mark headers as potentially valid for row processing
            console.log(`[Job ${this.job.id}] Header check passed (required headers present).`)
          }
        })
        .on('data', (data) => results.push(data))
        .on('end', async () => {
          console.log(
            `[Job ${this.job.id}] CSV parsing finished. Read ${results.length} data rows.`
          )

          // If headers were invalid from the start, report and resolve early
          if (headerErrors.length > 0) {
            report.errors.push({ row: 0, errors: { headers: headerErrors }, data: {} })
            report.totalRows = results.length // Still report total read attempts
            report.failedRows = results.length // Mark all as failed due to headers
            report.timestamp = new Date().toISOString()
            await this.cleanupS3File(diskName, s3ObjectKey)
            this.job.returnvalue = report // Store report in job's returnvalue property
            resolve() // Resolve without returning the report
            return
          }
          if (results.length === 0) {
            console.log(`[Job ${this.job.id}] CSV file is empty or contains no data rows.`)
            report.errors.push({
              row: 0,
              errors: { file: 'CSV file is empty or contains no data rows.' },
              data: {},
            })
            report.timestamp = new Date().toISOString()
            await this.cleanupS3File(diskName, s3ObjectKey)
            this.job.returnvalue = report // Store report in job's returnvalue property
            resolve() // Resolve without returning the report
            return
          }

          // --- Preload Categories and Services ---
          const categoriesMap = new Map<string, string>() // slug -> id
          const servicesMap = new Map<string, string>() // slug -> id
          const categoryIdsMap = new Map<string, string>() // id -> id (for id lookups)
          const serviceIdsMap = new Map<string, string>() // id -> id (for id lookups)

          try {
            const allCategorySlugs = [
              ...new Set(
                results
                  .map((r) => r.vendor_category_slugs?.trim())
                  .filter(Boolean)
                  .flatMap((s) => s.split(','))
              ),
            ]
            const allServiceSlugs = [
              ...new Set(results.map((r) => r.service_slug?.trim()).filter(Boolean)),
            ]
            const allCategoryIds = [
              ...new Set(
                results
                  .map((r) => r.vendor_category_ids?.trim())
                  .filter(Boolean)
                  .flatMap((s) => s.split(','))
              ),
            ]
            const allServiceIds = [
              ...new Set(results.map((r) => r.service_id?.trim()).filter(Boolean)),
            ]

            if (allCategorySlugs.length > 0) {
              const categories = await VendorCategory.query()
                .whereIn('slug', allCategorySlugs)
                .select(['id', 'slug'])
              categories.forEach((cat) => categoriesMap.set(cat.slug, cat.id))
            }
            if (allCategoryIds.length > 0) {
              const categoriesById = await VendorCategory.query()
                .whereIn('id', allCategoryIds)
                .select(['id'])
              categoriesById.forEach((cat) => categoryIdsMap.set(cat.id, cat.id))
            }
            if (allServiceSlugs.length > 0) {
              const services = await Service.query()
                .whereIn('slug', allServiceSlugs)
                .select(['id', 'slug'])
              services.forEach((srv) => servicesMap.set(srv.slug, srv.id))
            }
            if (allServiceIds.length > 0) {
              const servicesById = await Service.query().whereIn('id', allServiceIds).select(['id'])
              servicesById.forEach((srv) => serviceIdsMap.set(srv.id, srv.id))
            }
          } catch (preloadError) {
            console.error(
              `[Job ${this.job.id}] Error preloading categories/services:`,
              preloadError
            )
            // Decide if this is fatal. For now, we'll proceed but linking might fail.
            report.errors.push({
              row: 0,
              errors: { preload: `Error preloading lookup data: ${preloadError.message}` },
              data: {},
            })
          }
          // --- ---

          // --- Process Rows ---
          report.totalRows = results.length
          let currentRowNum = 0

          for (const rowData of results) {
            currentRowNum++ // Start row count from 1 for reporting

            // Define row validation schema dynamically inside the loop for clarity
            const rowSchema = schema.create({
              // User Info (Required)
              first_name: schema.string({ trim: true }, [rules.maxLength(100)]),
              last_name: schema.string({ trim: true }, [rules.maxLength(100)]),
              email: schema.string({ trim: true }, [
                rules.email(),
                rules.normalizeEmail({}),
                rules.unique({ table: 'users', column: 'email' }), // Check uniqueness here
              ]),
              phone: schema.string({ trim: true }, [
                // Custom validation for Kenyan phone numbers starting with 254 (with or without +)
                rules.regex(/^\+?254[0-9]{9}$/),
                rules.unique({ table: 'users', column: 'phone' }), // Check uniqueness
              ]),
              // User Info (Optional)
              title: schema.string.optional({ trim: true }, [rules.maxLength(50)]),
              gender: schema.enum.optional(['Male', 'Female', 'Other'] as const),
              dob: schema.date.optional({ format: 'yyyy-MM-dd' }),
              idpass: schema.string.optional({ trim: true }, [rules.maxLength(100)]),

              // Vendor Info (Required)
              vendor_name: schema.string({ trim: true }, [rules.maxLength(255)]),
              vendor_kra: schema.string({ trim: true }, [
                rules.maxLength(20), // Example length
                rules.unique({ table: 'vendors', column: 'kra' }), // Check KRA uniqueness
                // Add specific KRA format regex if available: rules.regex(/^[A-Z]\d{9}[A-Z]$/i)
              ]),
              // Vendor Info (Optional)
              vendor_details: schema.string.optional({ trim: true }),
              vendor_email: schema.string.optional({ trim: true }, [rules.email()]),
              vendor_phone: schema.string.optional({ trim: true }, [
                rules.regex(/^\+?254[0-9]{9}$/),
              ]),
              vendor_reg: schema.string.optional({ trim: true }, [rules.maxLength(100)]),
              vendor_permit: schema.string.optional({ trim: true }, [rules.maxLength(100)]),

              // Linking (Optional) - Validate format if present
              vendor_category_ids: schema.string.optional({}, [rules.regex(/^[\w-,]+$/)]), // ULIDs/comma
              vendor_category_slugs: schema.string.optional({}, [rules.regex(/^[a-z0-9\-,]+$/)]), // slugs/comma
              service_id: schema.string.optional({}, [rules.regex(/^[\w-]+$/)]), // ULID
              service_slug: schema.string.optional({}, [rules.regex(/^[a-z0-9-]+$/)]), // slug

              // Initial Branch Info (Optional)
              branch_name: schema.string.optional({ trim: true }, [rules.maxLength(255)]),
              branch_location_address: schema.string.optional({ trim: true }),
              branch_location_lat: schema.number.optional([rules.range(-90, 90)]),
              branch_location_lng: schema.number.optional([rules.range(-180, 180)]),
              branch_details: schema.string.optional({ trim: true }),
              branch_email: schema.string.optional({ trim: true }, [rules.email()]),
              branch_phone: schema.string.optional({ trim: true }, [
                rules.regex(/^\+?254[0-9]{9}$/),
              ]),

              // Initial Staff Info (Optional)
              staff_identifier: schema.string.optional({ trim: true }, [rules.maxLength(100)]),
            })

            const trx = await Database.transaction() // Start transaction for this row

            try {
              // Validate the current row's data
              const validatedRow = await validator.validate({
                schema: rowSchema,
                data: rowData,
                messages: {
                  // Custom messages if needed
                  'email.unique': 'Email address is already in use.',
                  'phone.unique': 'Phone number is already in use.',
                  'phone.mobile':
                    'Phone number must be a valid Kenyan mobile number (e.g., 2547...).',
                  'vendor_kra.unique': 'KRA PIN is already registered to another vendor.',
                  'required': 'The {{ field }} is required.',
                  'email': 'Invalid email format for {{ field }}.',
                  'number': 'The {{ field }} must be a number.',
                  'enum': 'Invalid value for {{ field }}.',
                  'date.format': 'Invalid date format for {{ field }}, use YYYY-MM-DD.',
                },
              })

              // 1. Create User
              const user = await User.create(
                {
                  firstName: validatedRow.first_name,
                  lastName: validatedRow.last_name,
                  email: validatedRow.email,
                  phone: validatedRow.phone.startsWith('+')
                    ? validatedRow.phone.substring(1)
                    : validatedRow.phone, // Ensure no leading +
                  password: validatedRow.phone, // Default password
                  title: validatedRow.title,
                  gender: validatedRow.gender,
                  dob: validatedRow.dob?.toISODate() ?? undefined, // Format date
                  idpass: validatedRow.idpass,
                  status: 'Active',
                  emailVerifiedAt: DateTime.now(), // Assume verified by admin upload
                  phoneVerifiedAt: DateTime.now(), // Assume verified by admin upload
                },
                { client: trx }
              )
              // Assign a default role if needed (e.g., 'vendor_user')
              await user.useTransaction(trx).assignRole('vendor') // Adjust role name if necessary

              // 2. Create Vendor
              const vendor = await Vendor.create(
                {
                  userId: user.id,
                  name: validatedRow.vendor_name,
                  slug:
                    string.toSlug(validatedRow.vendor_name, { lower: true }) +
                    `-${ulid().substring(20)}`, // Basic slug
                  kra: validatedRow.vendor_kra,
                  details: validatedRow.vendor_details,
                  email: validatedRow.vendor_email || validatedRow.email, // Default to user email
                  phone: (validatedRow.vendor_phone || validatedRow.phone).startsWith('+')
                    ? (validatedRow.vendor_phone || validatedRow.phone).substring(1)
                    : (validatedRow.vendor_phone || validatedRow.phone), // Default to user phone and remove +
                  reg: validatedRow.vendor_reg,
                  permit: validatedRow.vendor_permit,
                  active: true, // Default to active
                },
                { client: trx }
              )

              // 3. Create Branch (Default HQ)
              let branchLocation: {
                name: string
                address: string
                regions: { country: string }
                coordinates: { lat: number; lng: number }
                place_id: string
              } | null = null
              let branchGeom: any = null
              if (
                validatedRow.branch_location_address &&
                validatedRow.branch_location_lat &&
                validatedRow.branch_location_lng
              ) {
                branchLocation = {
                  // Construct location object
                  name: validatedRow.branch_name || `${validatedRow.vendor_name} HQ`,
                  address: validatedRow.branch_location_address,
                  regions: { country: 'Kenya' }, // Assuming Kenya, adjust as needed
                  coordinates: {
                    lat: validatedRow.branch_location_lat,
                    lng: validatedRow.branch_location_lng,
                  },
                  place_id: '', // Cannot determine place_id from CSV easily
                }
                try {
                  // Use Database.st() for geometry creation
                  branchGeom = Database.st().geomFromText(
                    `POINT(${validatedRow.branch_location_lng} ${validatedRow.branch_location_lat})`,
                    4326 // SRID for WGS 84
                  )
                } catch (geomError) {
                  console.warn(
                    `[Job ${this.job.id}] Row ${currentRowNum}: Could not create geometry for branch. Lat: ${validatedRow.branch_location_lat}, Lng: ${validatedRow.branch_location_lng}. Error: ${geomError.message}`
                  )
                  // Decide if this is a critical error - for now, continue without geom
                }
              }

              const branch = await Branch.create(
                {
                  vendorId: vendor.id,
                  name: validatedRow.branch_name || `${validatedRow.vendor_name} HQ`,
                  details: validatedRow.branch_details,
                  email: validatedRow.branch_email || vendor.email,
                  phone: (validatedRow.branch_phone || vendor.phone).startsWith('+')
                    ? (validatedRow.branch_phone || vendor.phone).substring(1)
                    : (validatedRow.branch_phone || vendor.phone), // Remove + if present
                  location: branchLocation, // Assign the constructed location object or null
                  geom: branchGeom, // Assign the created geometry or null
                },
                { client: trx }
              )

              // 4. Create Staff Link
              const staffIdentifier = validatedRow.staff_identifier || user.phone // Default identifier
              // Use the relationship to attach staff to the branch
              await branch.related('staff').attach(
                {
                  [user.id]: {
                    vendor_id: vendor.id, // Explicitly provide vendor_id for the pivot
                    identifier: staffIdentifier,
                  },
                },
                trx
              )

              // 5. Link Categories & Service
              const categoryIdsToLink: string[] = []
              const serviceIdsToLink: string[] = [] // Assuming only one service link possible based on schema

              // Process Category IDs/Slugs
              const rawCategoryIds =
                validatedRow.vendor_category_ids
                  ?.split(',')
                  .map((id) => id.trim())
                  .filter(Boolean) || []
              const rawCategorySlugs =
                validatedRow.vendor_category_slugs
                  ?.split(',')
                  .map((slug) => slug.trim())
                  .filter(Boolean) || []

              if (rawCategoryIds.length > 0) {
                rawCategoryIds.forEach((id) => {
                  if (categoryIdsMap.has(id)) {
                    categoryIdsToLink.push(id)
                  } else {
                    console.warn(
                      `[Job ${this.job.id}] Row ${currentRowNum}: Provided Category ID '${id}' not found.`
                    )
                  }
                })
              } else if (rawCategorySlugs.length > 0) {
                rawCategorySlugs.forEach((slug) => {
                  const foundId = categoriesMap.get(slug)
                  if (foundId) {
                    categoryIdsToLink.push(foundId)
                  } else {
                    console.warn(
                      `[Job ${this.job.id}] Row ${currentRowNum}: Provided Category Slug '${slug}' not found.`
                    )
                  }
                })
              }

              // Process Service ID/Slug
              if (validatedRow.service_id) {
                if (serviceIdsMap.has(validatedRow.service_id)) {
                  serviceIdsToLink.push(validatedRow.service_id)
                  await vendor
                    .merge({ serviceId: validatedRow.service_id })
                    .useTransaction(trx)
                    .save() // Update vendor's primary service
                } else {
                  console.warn(
                    `[Job ${this.job.id}] Row ${currentRowNum}: Provided Service ID '${validatedRow.service_id}' not found.`
                  )
                }
              } else if (validatedRow.service_slug) {
                const foundServiceId = servicesMap.get(validatedRow.service_slug)
                if (foundServiceId) {
                  serviceIdsToLink.push(foundServiceId)
                  await vendor.merge({ serviceId: foundServiceId }).useTransaction(trx).save() // Update vendor's primary service
                } else {
                  console.warn(
                    `[Job ${this.job.id}] Row ${currentRowNum}: Provided Service Slug '${validatedRow.service_slug}' not found.`
                  )
                }
              }

              // Attach categories if any were valid
              if (categoryIdsToLink.length > 0) {
                await vendor.related('categories').attach(categoryIdsToLink, trx)
              }
              // Attach services (Note: This links Vendor to Service M:M, distinct from vendor.serviceId FK)
              if (serviceIdsToLink.length > 0) {
                await vendor.related('services').attach(
                  serviceIdsToLink.reduce((acc, id) => ({ ...acc, [id]: { active: true } }), {}),
                  trx
                )
              }

              // If we reach here, all steps for this row succeeded
              await trx.commit()
              report.successfulRows++
            } catch (error) {
              await trx.rollback()
              report.failedRows++
              const errorMessages = error.messages || { general: [error.message] } // Ensure errors are in an array
              // Add specific handling for unique constraint violations if needed
              if (error.code === '23505') {
                // PostgreSQL unique violation code
                errorMessages.unique_constraint = [
                  `Database unique constraint failed: ${error.detail || error.message}`,
                ]
              }
              console.error(
                `[Job ${this.job.id}] Error processing row ${currentRowNum}:`,
                errorMessages,
                'Data:',
                rowData
              )
              report.errors.push({ row: currentRowNum, errors: errorMessages, data: rowData })
            }

            // Update job progress
            await this.job.updateProgress(Math.round((currentRowNum / results.length) * 100))
          } // End for loop

          console.log(
            `[Job ${this.job.id}] Finished processing all rows. Success: ${report.successfulRows}, Failed: ${report.failedRows}`
          )
          report.timestamp = new Date().toISOString()
          await this.cleanupS3File(diskName, s3ObjectKey) // Cleanup S3 file
          this.job.returnvalue = report // Store report in job's returnvalue property
          resolve() // Resolve without returning the report
        })
        .on('error', async (error) => {
          console.error(`[Job ${this.job.id}] Critical CSV Stream Error:`, error)
          report.errors.push({
            row: 0,
            errors: { file: `CSV parsing error: ${error.message}` },
            data: {},
          })
          report.failedRows = results.length - report.successfulRows // Estimate failures
          report.timestamp = new Date().toISOString()
          await this.cleanupS3File(diskName, s3ObjectKey)
          // Reject the promise to signal job failure to BullMQ
          reject(new Error(`CSV parsing error: ${error.message}`))
        })
    }) // End Promise wrapper
  }

  /**
   * Helper to safely delete the S3 file.
   */
  private async cleanupS3File(diskName: 's3', s3ObjectKey: string): Promise<void> {
    try {
      console.log(`[Job ${this.job.id}] Attempting to delete S3 object: ${s3ObjectKey}`)
      const exists = await Drive.use(diskName).exists(s3ObjectKey)
      if (exists) {
        await Drive.use(diskName).delete(s3ObjectKey)
        console.log(`[Job ${this.job.id}] S3 object ${s3ObjectKey} deleted successfully.`)
      } else {
        console.warn(`[Job ${this.job.id}] S3 object ${s3ObjectKey} not found for deletion.`)
      }
    } catch (deleteError) {
      console.error(`[Job ${this.job.id}] Failed to delete S3 object ${s3ObjectKey}:`, deleteError)
    }
  }

  /**
   * Called when the job fails after all retry attempts.
   */
  public async failed() {
    const payload = this.job.data as ProcessVendorCsvSetupPayload | undefined
    console.error(`[Job ${this.job.id}] ProcessVendorCsvSetup FAILED permanently.`)
    console.error(`[Job ${this.job.id}] Failure Reason:`, this.job.failedReason)

    if (payload?.s3ObjectKey && payload?.diskName) {
      console.log(`[Job ${this.job.id}] Attempting S3 cleanup after permanent failure...`)
      await this.cleanupS3File(payload.diskName, payload.s3ObjectKey)
    } else {
      console.error(
        `[Job ${this.job.id}] Cannot cleanup S3 file due to missing/invalid payload on permanent failure.`
      )
    }

    // Add notification logic here if needed (e.g., notify the admin)
    if (payload?.userId) {
      // Example: await User.find(payload.userId)?.notify(new VendorUploadFailedPermanently(this.job.id, this.job.failedReason));
      console.log(
        `[Job ${this.job.id}] TODO: Notify user ${payload.userId} about permanent job failure.`
      )
    }
  }
}
