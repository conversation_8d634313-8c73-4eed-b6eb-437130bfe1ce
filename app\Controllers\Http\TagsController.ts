import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Tag from '../../Models/Tag'
import { bind } from '@adonisjs/route-model-binding'

export default class TagsController {
  /**
   * @index
   * @summary Show all tags
   * @version 1.0.0
   * @description Tag management for the application
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const tagQuery = Tag.filter(filters)

    return await tagQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a tag
   * @description Create a tag with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   */
  public async store({ request }: HttpContextContract) {
    const { name, details } = request.all()
    const tag = new Tag()

    tag.fill({ name, details })

    // const image = request.file('image')

    // if (image) {
    //   tag.image = Attachment.fromFile(image)
    // }

    return await tag.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a single Tag
   * @description Show a Tag with their details (name and details)
   * @responseBody 200 - <Tag>
   * @response 404 - Tag not found
   */
  public async show({ response }: HttpContextContract, tag: Tag) {
    return response.json(tag)
  }

  @bind()

  /**
   * @update
   * @summary Update a Tag
   * @description Update a Tag with their details (name and details)
   * @requestBody <Tag>
   * @responseBody 200 - <Tag>
   * @response 404 - Tag not found
   */
  public async update({ request, response }: HttpContextContract, tag: Tag) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await tag.merge(input).save()

    return response.json(tag)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a Tage
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, tag: Tag) {
    return await tag.delete()
  }
}
