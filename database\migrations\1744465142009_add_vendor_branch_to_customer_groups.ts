import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'customer_groups'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE').nullable()
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('vendor_id')
      table.dropColumn('branch_id')
    })
  }
} 