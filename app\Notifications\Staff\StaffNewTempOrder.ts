import { NotificationContract } from '@ioc:Verful/Notification'
// Updated: Use unified Order model instead of TempOrder
import Order from 'App/Models/Order'
import User from 'App/Models/User'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

export default class StaffNewTempOrder implements NotificationContract {
  constructor(
    // Updated: Accept unified Order model (will be temp order with status='Pending')
    private order: Order,
    public message: string = 'New order request from {firstName}',
    public title: string = 'New Order Request'
  ) {
    // Validate that this is actually a temp order
    if (order.status !== 'Pending') {
      console.warn(
        'StaffNewTempOrder notification received non-temp order:',
        order.id,
        order.status
      )
    }

    this.message = message || `New order request from ${this.order.customer.firstName}`
  }

  public via(_notifiable) {
    return ['database' as const, 'fcm' as const]
  }

  public toDatabase() {
    const personalizedMessage = this.message.replace('{firstName}', this.order.customer.firstName)

    return NotificationHelper.createNotificationData(
      this.title,
      personalizedMessage,
      NotificationHelper.createTempOrderActions(this.order.id, 'staff'),
      {
        category: NotificationCategory.ORDER,
        notificationType: NotificationType.TEMP_ORDER_CREATED,
        priority: NotificationPriority.HIGH, // High priority for staff to review
        entityId: this.order.id,
        entityType: 'temp_order',
        orderStatus: this.order.status,
        customerId: this.order.customer?.id,
        customerName: `${this.order.customer?.firstName} ${this.order.customer?.lastName}`,
        vendorId: this.order.vendor?.id,
        vendorName: this.order.vendor?.name,
        requestDate: this.order.createdAt?.toISO(),
        userRole: 'staff',
      },
      'https://cdn.verful.com/icons/staff-temp-order-icon.png'
    )
  }

  public toFcm(_notifiable: User): NotificationMessagePayload {
    const body = this.message.replace('{firstName}', this.order.customer.firstName)

    return {
      title: this.title,
      body,
      // url: `aiastaff://orders/${this.order.id}`,
      // icon: 'https://cdn.verful.com/icons/verful-512x512.png',
    }
  }
}
