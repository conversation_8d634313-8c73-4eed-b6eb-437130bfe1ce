import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'upsells'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE')
      table.string('upsell_id').references('id').inTable('products').onDelete('CASCADE')
      table.decimal('price').defaultTo(0)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      table.unique(['product_id', 'upsell_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
