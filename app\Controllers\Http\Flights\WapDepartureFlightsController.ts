import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Mail from '@ioc:Adonis/Addons/Mail'
import WapDepartureFlight from 'App/Models/Flights/WapDepartureFlight'

/**
 * @summary WAP Departure Flights
 * @description Controller for managing departure flights from WAP.
 */
export default class WapDepartureFlightsController {
  /**
   * @index
   *
   * @summary Fetch WAP Departure Flights
   * @description Retrieves a list of departure flights from WAP, optionally filtered by airline and origin airport.
   *
   * @paramQuery page - The page number for pagination.
   * @paramQuery per - The number of records per page.
   *
   * @responseBody 200 - A paginated list of <WapDepartureFlight> objects.
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const { page, per, ...filters } = request.qs()

      const flightQuery = WapDepartureFlight.filter(filters)

      const flights = await flightQuery.paginate(page, per)
      return response.json(flights)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Arrival Flights Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }

  /**
   * @show
   *
   * @summary Show Departure Flight
   * @description Retrieves details of a specific departure flight from WAP.
   *
   * @pathParam id - The ID of the flight to retrieve.
   *
   * @responseBody 200 - Details of the requested <WapDepartureFlight> object.
   * @response 404 - If the flight with the given ID is not found.
   */
  public async show({ response, params }: HttpContextContract) {
    try {
      const flight = await WapDepartureFlight.findOrFail(params.id)

      return response.json(flight)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('KIA Arrival Flights Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }
}
