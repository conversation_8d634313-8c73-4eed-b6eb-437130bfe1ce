import { schema, rules, CustomMessages } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class CreateUserValidator {
  constructor(protected ctx: HttpContextContract) {}

  /*
   * Define schema to validate the "shape", "type", "formatting" and "integrity" of data.
   *
   * For example:
   * 1. The username must be of data type string. But then also, it should
   *    not contain special characters or numbers.
   *    ```
   *     schema.string([ rules.alpha() ])    ```
   *
   * 2. The email must be of data type string, formatted as a valid
   *    email. But also, not used by any other user.
   *    ```
   *     schema.string([
   *       rules.email(),
   *       rules.unique({ table: 'users', column: 'email' }),
   *     ])    ```
   */
  public schema = schema.create({
    title: schema.string.optional(),
    firstName: schema.string({ trim: true }),
    lastName: schema.string({ trim: true }),
    email: schema.string({ trim: true }, [
      rules.email(),
      // rules.unique({ table: 'users', column: 'email' }),
    ]),
    phone: schema.string.optional({ trim: true }, [
      // rules.mobile({ strict: true }),
      // rules.unique({ table: 'users', column: 'phone' }),
    ]),
    idpass: schema.string.optional(),
    dob: schema.string.optional(),
    role: schema.string.optional(),
    location: schema.object.optional().members({
      name: schema.string.optional(),
      address: schema.string(),
      regions: schema.object().members({
        administrative_area_level_3: schema.string.optional(),
        administrative_area_level_1: schema.string.optional(),
        country: schema.string(),
      }),
      coordinates: schema.object().members({
        lat: schema.number(),
        lng: schema.number(),
      }),
      place_id: schema.string(),
    }),
  })

  /**
   * Custom messages for validation failures. You can make use of dot notation `(.)`
   * for targeting nested fields and array expressions `(*)` for targeting all
   * children of an array. For example:
   *
   * {
   *   'profile.username.required': 'Username is required',
   *   'scores.*.number': 'Define scores as valid numbers'
   * }
   *
   */
  public messages: CustomMessages = {}
}
