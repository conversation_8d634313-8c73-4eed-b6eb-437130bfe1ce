import { useEffect, useRef, useCallback, useState } from 'react'
import { io, Socket } from 'socket.io-client'
import { WebSocketSubscriptionClient, WebSocketSubscriptionHelpers } from '@/services/WebSocketClientHelper'

interface UseWebSocketReturn {
  socket: Socket | null
  connected: boolean
  subscribe: (type: string, targetId: string, subType?: string) => Promise<string | null>
  unsubscribe: (subscriptionId: string) => Promise<boolean>
  subscriptions: string[]
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  lastError: string | null
}

interface WebSocketConfig {
  url?: string
  autoConnect?: boolean
  reconnection?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
}

export const useWebSocket = (config: WebSocketConfig = {}): UseWebSocketReturn => {
  const {
    url = process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'http://localhost:3333',
    autoConnect = true,
    reconnection = true,
    reconnectionAttempts = 5,
    reconnectionDelay = 1000
  } = config

  const [connected, setConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [subscriptions, setSubscriptions] = useState<string[]>([])
  const [lastError, setLastError] = useState<string | null>(null)

  const socketRef = useRef<Socket | null>(null)
  const subscriptionClientRef = useRef<WebSocketSubscriptionClient | null>(null)

  // Get auth token for WebSocket authentication
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('auth_token') || ''
  }, [])

  // Initialize WebSocket connection
  const initializeSocket = useCallback(() => {
    if (socketRef.current?.connected) {
      return socketRef.current
    }

    setConnectionStatus('connecting')
    setLastError(null)

    const token = getAuthToken()
    
    const socket = io(url, {
      auth: {
        token
      },
      reconnection,
      reconnectionAttempts,
      reconnectionDelay,
      transports: ['websocket', 'polling']
    })

    // Connection event handlers
    socket.on('connect', () => {
      console.log('WebSocket connected:', socket.id)
      setConnected(true)
      setConnectionStatus('connected')
      setLastError(null)
    })

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason)
      setConnected(false)
      setConnectionStatus('disconnected')
      
      // Clear subscriptions on disconnect
      setSubscriptions([])
    })

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      setConnected(false)
      setConnectionStatus('error')
      setLastError(error.message || 'Connection failed')
    })

    socket.on('reconnect', (attemptNumber) => {
      console.log('WebSocket reconnected after', attemptNumber, 'attempts')
      setConnected(true)
      setConnectionStatus('connected')
      setLastError(null)
    })

    socket.on('reconnect_error', (error) => {
      console.error('WebSocket reconnection error:', error)
      setLastError(error.message || 'Reconnection failed')
    })

    socket.on('reconnect_failed', () => {
      console.error('WebSocket reconnection failed')
      setConnectionStatus('error')
      setLastError('Failed to reconnect after maximum attempts')
    })

    // Authentication events
    socket.on('auth_error', (error) => {
      console.error('WebSocket authentication error:', error)
      setLastError('Authentication failed')
      socket.disconnect()
    })

    socket.on('auth_success', (data) => {
      console.log('WebSocket authenticated successfully:', data)
    })

    // Real-time event handlers
    socket.on('notification', (data) => {
      console.log('Received notification:', data)
      // Dispatch custom event for notification handling
      window.dispatchEvent(new CustomEvent('websocket-notification', { detail: data }))
    })

    socket.on('order_status_update', (data) => {
      console.log('Order status update:', data)
      window.dispatchEvent(new CustomEvent('websocket-order-status', { detail: data }))
    })

    socket.on('item_status_update', (data) => {
      console.log('Item status update:', data)
      window.dispatchEvent(new CustomEvent('websocket-item-status', { detail: data }))
    })

    socket.on('modifier_status_update', (data) => {
      console.log('Modifier status update:', data)
      window.dispatchEvent(new CustomEvent('websocket-modifier-status', { detail: data }))
    })

    socket.on('department_workload_update', (data) => {
      console.log('Department workload update:', data)
      window.dispatchEvent(new CustomEvent('websocket-department-workload', { detail: data }))
    })

    socket.on('overdue_alert', (data) => {
      console.log('Overdue alert:', data)
      window.dispatchEvent(new CustomEvent('websocket-overdue-alert', { detail: data }))
    })

    socket.on('staff_assignment', (data) => {
      console.log('Staff assignment:', data)
      window.dispatchEvent(new CustomEvent('websocket-staff-assignment', { detail: data }))
    })

    socket.on('order_completed', (data) => {
      console.log('Order completed:', data)
      window.dispatchEvent(new CustomEvent('websocket-order-completed', { detail: data }))
    })

    socketRef.current = socket
    
    // Initialize subscription client
    subscriptionClientRef.current = new WebSocketSubscriptionClient(socket)

    return socket
  }, [url, reconnection, reconnectionAttempts, reconnectionDelay, getAuthToken])

  // Subscribe to a channel
  const subscribe = useCallback(async (
    type: string,
    targetId: string,
    subType?: string
  ): Promise<string | null> => {
    if (!subscriptionClientRef.current) {
      console.error('WebSocket subscription client not initialized')
      return null
    }

    try {
      const result = await subscriptionClientRef.current.subscribe({
        type: type as any,
        target_id: targetId,
        sub_type: subType
      })

      if (result.success && result.subscription_id) {
        setSubscriptions(prev => [...prev, result.subscription_id!])
        console.log(`Subscribed to ${type}:${targetId}${subType ? `:${subType}` : ''}`)
        return result.subscription_id
      } else {
        console.error('Subscription failed:', result.error)
        setLastError(result.error || 'Subscription failed')
        return null
      }
    } catch (error) {
      console.error('Error subscribing:', error)
      setLastError(error instanceof Error ? error.message : 'Subscription error')
      return null
    }
  }, [])

  // Unsubscribe from a channel
  const unsubscribe = useCallback(async (subscriptionId: string): Promise<boolean> => {
    if (!subscriptionClientRef.current) {
      console.error('WebSocket subscription client not initialized')
      return false
    }

    try {
      const result = await subscriptionClientRef.current.unsubscribe(subscriptionId)
      
      if (result.success) {
        setSubscriptions(prev => prev.filter(id => id !== subscriptionId))
        console.log(`Unsubscribed from ${subscriptionId}`)
        return true
      } else {
        console.error('Unsubscription failed:', result.error)
        setLastError(result.error || 'Unsubscription failed')
        return false
      }
    } catch (error) {
      console.error('Error unsubscribing:', error)
      setLastError(error instanceof Error ? error.message : 'Unsubscription error')
      return false
    }
  }, [])

  // Initialize socket on mount
  useEffect(() => {
    if (autoConnect) {
      initializeSocket()
    }

    return () => {
      if (socketRef.current) {
        console.log('Cleaning up WebSocket connection')
        socketRef.current.disconnect()
        socketRef.current = null
        subscriptionClientRef.current = null
      }
    }
  }, [autoConnect, initializeSocket])

  // Cleanup subscriptions on unmount
  useEffect(() => {
    return () => {
      subscriptions.forEach(subscriptionId => {
        unsubscribe(subscriptionId)
      })
    }
  }, []) // Empty dependency array for cleanup only

  return {
    socket: socketRef.current,
    connected,
    subscribe,
    unsubscribe,
    subscriptions,
    connectionStatus,
    lastError
  }
}

// Helper hook for specific subscription patterns
export const useOrderSubscription = (orderId: string) => {
  const { subscribe, unsubscribe } = useWebSocket()
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null)

  useEffect(() => {
    if (orderId) {
      WebSocketSubscriptionHelpers.subscribeToOrder(
        { subscribe } as any,
        orderId
      ).then(result => {
        if (result.success && result.subscription_id) {
          setSubscriptionId(result.subscription_id)
        }
      })
    }

    return () => {
      if (subscriptionId) {
        unsubscribe(subscriptionId)
      }
    }
  }, [orderId, subscribe, unsubscribe, subscriptionId])

  return { subscriptionId }
}

export const useDepartmentSubscription = (departmentId: string) => {
  const { subscribe, unsubscribe } = useWebSocket()
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null)

  useEffect(() => {
    if (departmentId) {
      WebSocketSubscriptionHelpers.subscribeToDepartment(
        { subscribe } as any,
        departmentId
      ).then(result => {
        if (result.success && result.subscription_id) {
          setSubscriptionId(result.subscription_id)
        }
      })
    }

    return () => {
      if (subscriptionId) {
        unsubscribe(subscriptionId)
      }
    }
  }, [departmentId, subscribe, unsubscribe, subscriptionId])

  return { subscriptionId }
}

export const useVendorSubscription = (vendorId: string, subType?: string) => {
  const { subscribe, unsubscribe } = useWebSocket()
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null)

  useEffect(() => {
    if (vendorId) {
      WebSocketSubscriptionHelpers.subscribeToVendor(
        { subscribe } as any,
        vendorId,
        subType
      ).then(result => {
        if (result.success && result.subscription_id) {
          setSubscriptionId(result.subscription_id)
        }
      })
    }

    return () => {
      if (subscriptionId) {
        unsubscribe(subscriptionId)
      }
    }
  }, [vendorId, subType, subscribe, unsubscribe, subscriptionId])

  return { subscriptionId }
}

export default useWebSocket
