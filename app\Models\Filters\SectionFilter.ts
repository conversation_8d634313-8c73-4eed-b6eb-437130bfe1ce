import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Section from '../Section'

export default class SectionFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Section, Section>

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }

  public with(relations: string) {
    relations.split(',').map((relation: 'branch') => {
      this.$query.preload(relation)
    })
  }
}
