module.exports = {
  apps: [
    {
      name: 'aia<PERSON>',
      script: 'node ./dist/server.js',
      autorestart: true,
      watch: false, // Disable watch mode to prevent conflicts
      max_memory_restart: '1G',
      kill_timeout: 5000, // Give 5 seconds for graceful shutdown
      listen_timeout: 10000, // Wait 10 seconds for app to be ready
      wait_ready: true, // Wait for app to emit 'ready' event
      restart_delay: 2000, // Wait 2 seconds before restart
      max_restarts: 10, // Limit restarts to prevent restart loops
      min_uptime: '10s', // App must run for 10s to be considered stable
    },
    {
      name: 'scheduler',
      script: 'node ace scheduler:run',
      autorestart: true,
      watch: false,
      kill_timeout: 5000,
      restart_delay: 2000,
      max_restarts: 10,
      min_uptime: '10s',
    },
    {
      name: 'queue',
      script: 'node ace queue:listen',
      autorestart: true,
      watch: false,
      kill_timeout: 5000,
      restart_delay: 2000,
      max_restarts: 10,
      min_uptime: '10s',
    },
  ],
}
