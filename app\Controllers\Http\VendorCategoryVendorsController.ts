import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'
import { DateTime } from 'luxon'
import VendorCategory from 'App/Models/VendorCategory'

export default class VendorCategoryVendorsController {
  @bind()
  /**
   * @name VendorCategoryVendor management
   * @index
   * @summary List all VendorCategoryVendor
   * @description List all VendorCategoryVendor, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */
  public async index({ request, response }: HttpContextContract, category: VendorCategory) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const vendorQuery = category.related('vendors').query().filter(filters)

    const vendors = await vendorQuery.orderBy(order, sort).paginate(page, per)

    return response.json(vendors)
  }

  @bind()

  /**
   * @store
   * @summary Create a VendorCategoryVendor
   * @description Create a VendorCategoryVendor with their details (name and details)
   * @requestBody {"firstName": "", "lastName": "", "email": "", "phone":"", "serviceId": "", "vendor": ""}
   * @responseBody 200 - <VendorCategoryVendor>
   */
  public async store({ request, response }: HttpContextContract, category: VendorCategory) {
    const { firstName, lastName, email, phone, serviceId, vendor } = request.all()

    const user = await User.create({
      firstName,
      lastName,
      email,
      phone,
      password: phone,
    })

    const vendorQ = await user.related('vendors').create({
      name: vendor.name,
      email: vendor.email,
      phone: vendor.phone,
      reg: vendor.reg || DateTime.now().toISODate(),
      kra: vendor.kra || DateTime.now().toISODate(),
      serviceId,
    })

    const logo = request.file('vendor.logo')

    if (logo) {
      await vendorQ.merge({ logo: Attachment.fromFile(logo) }).save()
    }

    await vendorQ.related('categories').attach([category.id])

    return response.json(vendorQ)
  }
}
