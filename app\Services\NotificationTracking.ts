import Database from '@ioc:Adonis/Lucid/Database'
import { NotificationContract } from '@ioc:Verful/Notification'
import User from 'App/Models/User'
import Logger from '@ioc:Adonis/Core/Logger'
import NotificationUsage from 'App/Models/NotificationUsage'
import NotificationBillingTier from 'App/Models/NotificationBillingTier'
import { DateTime } from 'luxon'

export default class NotificationTracking {
  /**
   * Track a notification event and record usage for billing
   */
  public static async track(
    notification: NotificationContract,
    notifiable: User,
    status: 'sent' | 'failed' | 'delivered' | 'read',
    channel: string,
    metadata: Record<string, any> = {}
  ) {
    try {
      // Track basic notification event
      await Database.table('notification_tracking').insert({
        notification_type: notification.constructor.name,
        notifiable_id: notifiable.id,
        channel,
        status,
        metadata: JSON.stringify(metadata),
        created_at: new Date(),
        updated_at: new Date(),
      })

      // If notification is sent successfully, record usage for billing
      if (status === 'sent' && notifiable.vendorId) {
        await this.recordUsage(notification, notifiable, channel, metadata)
      }

      Logger.info('Notification tracked', {
        notification_type: notification.constructor.name,
        notifiable_id: notifiable.id,
        channel,
        status,
        metadata,
      })
    } catch (error) {
      Logger.error('Failed to track notification', {
        error,
        notification_type: notification.constructor.name,
        notifiable_id: notifiable.id,
        channel,
        status,
        metadata,
      })
    }
  }

  /**
   * Record notification usage for billing
   */
  private static async recordUsage(
    notification: NotificationContract,
    notifiable: User,
    channel: string,
    metadata: Record<string, any>
  ) {
    try {
      // Get vendor's current billing tier
      const tier = await NotificationBillingTier.query()
        .where('is_active', true)
        .first()

      if (!tier) {
        Logger.warn('No active billing tier found for vendor', {
          vendor_id: notifiable.vendorId,
        })
        return
      }

      // Calculate cost based on channel
      const cost = tier.calculateCost(channel as 'sms' | 'email' | 'push')

      // Create usage record
      await NotificationUsage.create({
        vendorId: notifiable.vendorId,
        tierId: tier.id,
        notificationId: metadata.notificationId,
        notificationType: notification.constructor.name,
        channel,
        recipient: notifiable.id,
        cost,
        currency: tier.currency,
        status: 'pending',
        billingPeriod: DateTime.now().toFormat('yyyy-MM'),
        meta: {
          ...metadata,
          notificationData: notification.toDatabase ? notification.toDatabase(notifiable) : null,
        },
      })

      Logger.info('Notification usage recorded', {
        vendor_id: notifiable.vendorId,
        tier_id: tier.id,
        channel,
        cost,
      })
    } catch (error) {
      Logger.error('Failed to record notification usage', {
        error,
        vendor_id: notifiable.vendorId,
        notification_type: notification.constructor.name,
        channel,
      })
    }
  }

  /**
   * Get notification statistics
   */
  public static async getStats(filters: {
    startDate?: Date
    endDate?: Date
    notificationClassName?: string
    channel?: string
    status?: string
  } = {}) {
    const query = Database.from('notification_tracking')

    if (filters.startDate) {
      query.where('created_at', '>=', filters.startDate)
    }

    if (filters.endDate) {
      query.where('created_at', '<=', filters.endDate)
    }

    if (filters.notificationClassName) {
      query.where('notification_type', filters.notificationClassName)
    }

    if (filters.channel) {
      query.where('channel', filters.channel)
    }

    if (filters.status) {
      query.where('status', filters.status)
    }

    const stats = await query
      .select('notification_type', 'channel', 'status')
      .count('* as count')
      .groupBy('notification_type', 'channel', 'status')

    return stats
  }

  /**
   * Get notification delivery rates
   */
  public static async getDeliveryRates(filters: {
    startDate?: Date
    endDate?: Date
    notificationClassName?: string
    channel?: string
  } = {}) {
    const stats = await this.getStats(filters)
    
    const rates: Record<string, any> = {}
    
    stats.forEach((stat) => {
      const key = `${stat.notification_type}_${stat.channel}`
      if (!rates[key]) {
        rates[key] = {
          total: 0,
          delivered: 0,
          failed: 0,
          rate: 0,
        }
      }
      
      rates[key].total += stat.count
      if (stat.status === 'delivered') {
        rates[key].delivered += stat.count
      } else if (stat.status === 'failed') {
        rates[key].failed += stat.count
      }
      
      rates[key].rate = (rates[key].delivered / rates[key].total) * 100
    })
    
    return rates
  }
} 