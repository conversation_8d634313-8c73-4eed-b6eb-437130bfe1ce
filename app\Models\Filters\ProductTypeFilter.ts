import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import ProductType from '../ProductType'

export default class ProductTypeFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof ProductType, ProductType>

  public s(name: string) {
    this.$query.where((builder) => {
      builder.whereILike('name', `%${name}%`)
    })
  }

  // public with(relations: string) {
  //   relations.split(',').map((relation: 'categories') => {
  //     this.$query.preload(relation, (rq) => {
  //       if (relation === 'categories') {
  //         rq.preload('products', (pq) => {
  //           pq.preload('vendor')
  //         })
  //       }
  //     })
  //   })
  // }

  public service(typeId: string) {
    this.$query.where((builder) => {
      builder.where('service_id', typeId)
    })
  }

  public vendor(vendorId: string) {
    this.$query.whereHas('categories', (cq) => {
      cq.whereHas('products', (pq) => {
        pq.where('vendor_id', vendorId)
      })
    })
  }
}
