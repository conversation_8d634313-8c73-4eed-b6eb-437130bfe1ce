import { bind } from '@adonisjs/route-model-binding'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Setting from 'App/Models/Setting'

export default class SettingsController {
  /**
   * @index
   * @summary Show all Settings
   * @version 1.0.0
   * @description  Settings management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request, response }: HttpContextContract) {
    const {
      per = 10,
      page = 1,
      order = 'createdAt',
      sort = 'asc',
      name = null,
      branch = null,
    } = request.qs()

    const settingQuery = Setting.query()

    if (name) {
      settingQuery.where('name', name)
    }

    if (branch) {
      settingQuery.where('branchId', branch)
    }

    const settings = await settingQuery.orderBy(order, sort).paginate(page, per)

    return response.json(settings)
  }

  /**
   * @store
   * @summary Create a Setting
   * @description Create a Setting with their details (name and options)
   * @requestBody {"name": "", "options": "", "branchId": ""}
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, options, branchId } = request.all()
    const setting = await Setting.updateOrCreate(
      {
        name,
        branchId,
      },
      { options }
    )

    return response.json(setting)
  }

  @bind()
  /**
   * @show
   * @summary Show a single Setting
   * @description Show a setting with their details (name and options)
   * @paramPath id required number - Setting ID
   * @responseBody 200 - <Setting>
   * @response 404 - Setting not found
   */
  public async show({ response }: HttpContextContract, setting: Setting) {
    return response.json(setting)
  }

  @bind()
  /**
   * @update
   * @summary Update a setting
   * @description Update a setting with their details (name and options)
   * @paramPath id required number - Setting ID
   * @requestBody <Setting>
   * @responseBody 200 - <Setting>
   * @response 404 - Setting not found
   */
  public async update({ request, response }: HttpContextContract, setting: Setting) {
    setting.merge(request.all())
    await setting.save()

    return response.json(setting)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Setting
   * @reponseBody 204 - No content
   */
  public async destroy({ response }: HttpContextContract, setting: Setting) {
    await setting.delete()

    return response.json({ message: 'Setting deleted' })
  }
}
