import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Product from 'App/Models/Product'
import ProductFulfillmentSetting from 'App/Models/ProductFulfillmentSetting'
import Vendor from 'App/Models/Vendor'
import Database from '@ioc:Adonis/Lucid/Database'

export default class CartFulfillmentController {
  /**
   * Get available fulfillment options for cart items
   */
  public async getFulfillmentOptions({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      items: schema.array().members(
        schema.object().members({
          productId: schema.string({}, [rules.exists({ table: 'products', column: 'id' })]),
          quantity: schema.number([rules.unsigned()]),
        })
      ),
      vendorId: schema.string.optional({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      location: schema.object.optional().members({
        lat: schema.number(),
        lng: schema.number(),
        address: schema.string.optional(),
      }),
    })

    const payload = await request.validate({ schema: validationSchema })
    const { items, vendorId, location } = payload

    try {
      // Get all product IDs from cart
      const productIds = items.map((item) => item.productId)

      // Get products with their fulfillment settings
      const products = await Product.query()
        .whereIn('id', productIds)
        .preload('fulfillmentSettings')
        .exec()

      // Get vendor delivery settings if vendorId provided
      let vendorSettings = null
      if (vendorId) {
        const vendor = await Vendor.find(vendorId)
        vendorSettings = {
          deliveryPreferences: vendor?.deliveryPreferences || {},
          availabilitySettings: vendor?.availabilitySettings || {},
        }
      }

      // Calculate available fulfillment options
      const fulfillmentOptions = this.calculateFulfillmentOptions(products, vendorSettings, location)

      // Get delivery options if delivery is available
      let deliveryOptions = []
      if (fulfillmentOptions.delivery.available && location) {
        deliveryOptions = await this.getDeliveryOptions(vendorId, location, items)
      }

      return response.ok({
        fulfillmentOptions,
        deliveryOptions,
        vendorSettings,
        location,
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to get fulfillment options',
        error: error.message,
      })
    }
  }

  /**
   * Validate a specific fulfillment method for cart items
   */
  public async validateFulfillment({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      items: schema.array().members(
        schema.object().members({
          productId: schema.string({}, [rules.exists({ table: 'products', column: 'id' })]),
          quantity: schema.number([rules.unsigned()]),
        })
      ),
      fulfillmentType: schema.enum(['delivery', 'pickup', 'dinein', 'download'] as const),
      vendorId: schema.string({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      deliveryAddress: schema.object.optional().members({
        lat: schema.number(),
        lng: schema.number(),
        address: schema.string(),
      }),
      scheduledTime: schema.date.optional(),
    })

    const payload = await request.validate({ schema: validationSchema })
    const { items, fulfillmentType, vendorId, deliveryAddress, scheduledTime } = payload

    try {
      // Get products with fulfillment settings
      const productIds = items.map((item) => item.productId)
      const products = await Product.query()
        .whereIn('id', productIds)
        .preload('fulfillmentSettings')
        .exec()

      // Validate fulfillment type against all products
      const validation = this.validateFulfillmentType(products, fulfillmentType, scheduledTime)

      if (!validation.isValid) {
        return response.unprocessableEntity({
          message: 'Fulfillment validation failed',
          errors: validation.errors,
          invalidProducts: validation.invalidProducts,
        })
      }

      // Additional validation for delivery
      if (fulfillmentType === 'delivery' && deliveryAddress) {
        const deliveryValidation = await this.validateDeliveryAddress(vendorId, deliveryAddress)
        if (!deliveryValidation.isValid) {
          return response.unprocessableEntity({
            message: 'Delivery validation failed',
            errors: deliveryValidation.errors,
          })
        }
      }

      return response.ok({
        isValid: true,
        fulfillmentType,
        message: 'Fulfillment method is valid for all cart items',
        estimatedTime: validation.estimatedTime,
        additionalCharges: validation.additionalCharges,
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to validate fulfillment',
        error: error.message,
      })
    }
  }

  /**
   * Get delivery options for specific location and items
   */
  public async getDeliveryOptionsForCart({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      items: schema.array().members(
        schema.object().members({
          productId: schema.string(),
          quantity: schema.number(),
        })
      ),
      vendorId: schema.string({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      location: schema.object().members({
        lat: schema.number(),
        lng: schema.number(),
        address: schema.string(),
      }),
    })

    const payload = await request.validate({ schema: validationSchema })
    const { items, vendorId, location } = payload

    try {
      const deliveryOptions = await this.getDeliveryOptions(vendorId, location, items)

      return response.ok({
        deliveryOptions,
        location,
        totalItems: items.length,
      })
    } catch (error) {
      return response.badRequest({
        message: 'Failed to get delivery options',
        error: error.message,
      })
    }
  }

  /**
   * Calculate available fulfillment options based on products
   */
  private calculateFulfillmentOptions(products: Product[], vendorSettings: any, location: any) {
    const options = {
      delivery: {
        available: true,
        reasons: [],
        requirements: [],
      },
      pickup: {
        available: true,
        reasons: [],
        requirements: [],
      },
      dinein: {
        available: true,
        reasons: [],
        requirements: [],
      },
      download: {
        available: true,
        reasons: [],
        requirements: [],
      },
      schedule: {
        available: true,
        reasons: [],
        requirements: [],
      },
      preorder: {
        available: true,
        reasons: [],
        requirements: [],
      },
    }

    // Check each product's fulfillment settings
    for (const product of products) {
      const settings = product.fulfillmentSettings

      if (!settings) {
        // Use default settings based on product type
        const defaults = this.getDefaultFulfillmentSettings(product)
        this.applyProductConstraints(options, defaults, product.name)
      } else {
        this.applyProductConstraints(options, settings, product.name)
      }
    }

    // Apply vendor-level constraints
    if (vendorSettings) {
      this.applyVendorConstraints(options, vendorSettings, location)
    }

    return options
  }

  /**
   * Apply product-level fulfillment constraints
   */
  private applyProductConstraints(options: any, settings: any, productName: string) {
    if (!settings.isDeliverable) {
      options.delivery.available = false
      options.delivery.reasons.push(`${productName} is not available for delivery`)
    }

    if (!settings.isPickup) {
      options.pickup.available = false
      options.pickup.reasons.push(`${productName} is not available for pickup`)
    }

    if (!settings.physicalConsumptionIsOnsite) {
      options.dinein.available = false
      options.dinein.reasons.push(`${productName} is not available for dine-in`)
    }

    if (!settings.isDownloadable) {
      options.download.available = false
      options.download.reasons.push(`${productName} is not downloadable`)
    }

    if (!settings.scheduleAllowed) {
      options.schedule.available = false
      options.schedule.reasons.push(`${productName} cannot be scheduled`)
    }

    if (!settings.preorderAllowed) {
      options.preorder.available = false
      options.preorder.reasons.push(`${productName} cannot be preordered`)
    }
  }

  /**
   * Apply vendor-level fulfillment constraints
   */
  private applyVendorConstraints(options: any, vendorSettings: any, location: any) {
    const { deliveryPreferences } = vendorSettings

    if (deliveryPreferences && !deliveryPreferences.enableDelivery) {
      options.delivery.available = false
      options.delivery.reasons.push('Vendor does not offer delivery service')
    }

    if (location && deliveryPreferences?.deliveryRadius) {
      // Add location-based delivery validation
      options.delivery.requirements.push(`Delivery within ${deliveryPreferences.deliveryRadius}km radius`)
    }

    if (deliveryPreferences?.minimumOrderAmount) {
      options.delivery.requirements.push(`Minimum order amount: ${deliveryPreferences.minimumOrderAmount}`)
    }
  }

  /**
   * Get default fulfillment settings based on product type
   */
  private getDefaultFulfillmentSettings(product: Product) {
    const defaults = {
      isDeliverable: true,
      isPickup: true,
      physicalConsumptionIsOnsite: true,
      isDownloadable: false,
      scheduleAllowed: true,
      preorderAllowed: true,
    }

    // Adjust defaults based on product type
    if (product.type === 'Digital') {
      defaults.isDownloadable = true
      defaults.physicalConsumptionIsOnsite = false
    } else if (product.type === 'Service') {
      defaults.isDeliverable = false
      defaults.physicalConsumptionIsOnsite = false
    }

    return defaults
  }

  /**
   * Validate fulfillment type against products
   */
  private validateFulfillmentType(products: Product[], fulfillmentType: string, scheduledTime?: Date) {
    const validation = {
      isValid: true,
      errors: [],
      invalidProducts: [],
      estimatedTime: null,
      additionalCharges: [],
    }

    for (const product of products) {
      const settings = product.fulfillmentSettings || this.getDefaultFulfillmentSettings(product)

      switch (fulfillmentType) {
        case 'delivery':
          if (!settings.isDeliverable) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not available for delivery`)
            validation.invalidProducts.push(product.id)
          }
          break
        case 'pickup':
          if (!settings.isPickup) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not available for pickup`)
            validation.invalidProducts.push(product.id)
          }
          break
        case 'dinein':
          if (!settings.physicalConsumptionIsOnsite) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not available for dine-in`)
            validation.invalidProducts.push(product.id)
          }
          break
        case 'download':
          if (!settings.isDownloadable) {
            validation.isValid = false
            validation.errors.push(`${product.name} is not downloadable`)
            validation.invalidProducts.push(product.id)
          }
          break
      }

      // Validate scheduling if scheduled time provided
      if (scheduledTime && !settings.scheduleAllowed) {
        validation.isValid = false
        validation.errors.push(`${product.name} cannot be scheduled`)
        validation.invalidProducts.push(product.id)
      }
    }

    return validation
  }

  /**
   * Validate delivery address against vendor settings
   */
  private async validateDeliveryAddress(vendorId: string, deliveryAddress: any) {
    const vendor = await Vendor.find(vendorId)
    const validation = {
      isValid: true,
      errors: [],
    }

    if (!vendor) {
      validation.isValid = false
      validation.errors.push('Vendor not found')
      return validation
    }

    const deliveryPreferences = vendor.deliveryPreferences || {}

    // Validate delivery radius (simplified - would need actual distance calculation)
    if (deliveryPreferences.deliveryRadius) {
      // Add actual distance calculation logic here
      validation.errors.push(`Delivery address must be within ${deliveryPreferences.deliveryRadius}km`)
    }

    return validation
  }

  /**
   * Get available delivery options for location
   */
  private async getDeliveryOptions(vendorId: string | undefined, location: any, items: any[]) {
    // This would integrate with actual delivery service providers
    // For now, return mock delivery options
    const baseOptions = [
      {
        id: 'standard_delivery',
        name: 'Standard Delivery',
        description: 'Regular motorcycle delivery',
        estimatedTime: 45,
        fee: 150,
        vehicleType: 'motorcycle',
        isAvailable: true,
      },
      {
        id: 'express_delivery',
        name: 'Express Delivery',
        description: 'Fast car delivery',
        estimatedTime: 25,
        fee: 250,
        vehicleType: 'car',
        isAvailable: true,
      },
    ]

    // Filter based on vendor settings and location
    if (vendorId) {
      const vendor = await Vendor.find(vendorId)
      const deliveryPreferences = vendor?.deliveryPreferences || {}

      return baseOptions.filter((option) => {
        // Add vendor-specific filtering logic
        return option.isAvailable
      })
    }

    return baseOptions
  }
}
