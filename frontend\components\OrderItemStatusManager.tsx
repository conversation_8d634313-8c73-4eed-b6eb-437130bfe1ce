import React, { useState, useEffect, useCallback } from 'react'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  Timer,
  Play,
  Pause,
  Square,
  MessageSquare,
  Settings,
  RotateCcw,
  FastForward
} from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'
import { use<PERSON><PERSON>rItemStatus } from '@/hooks/useOrderItemStatus'
import { useWebSocket } from '@/hooks/useWebSocket'

interface OrderItemStatusManagerProps {
  orderItemId: number
  orderId: string
  initialData?: OrderItemData
  onStatusChange?: (itemId: number, newStatus: string, metadata?: Record<string, any>) => void
  onNotesUpdate?: (itemId: number, notes: string) => void
  compact?: boolean
  showModifiers?: boolean
}

interface OrderItemData {
  id: number
  order_id: string
  product_id: string
  product_name: string
  quantity: number
  status: 'pending' | 'preparing' | 'ready' | 'served'
  department_id?: string
  department_name?: string
  assigned_staff_id?: string
  assigned_staff_name?: string
  estimated_preparation_time?: number
  preparation_started_at?: string
  preparation_completed_at?: string
  preparation_notes?: string
  priority_level: number
  special_instructions?: string
  is_overdue: boolean
  overdue_minutes?: number
  modifiers: OrderItemModifier[]
  status_history: StatusHistoryEntry[]
}

interface OrderItemModifier {
  id: number
  name: string
  status: 'pending' | 'preparing' | 'completed' | 'skipped' | 'failed'
  complexity_level: number
  requires_special_skill: boolean
  prepared_by_staff_id?: string
  prepared_by_staff_name?: string
  preparation_started_at?: string
  preparation_completed_at?: string
  preparation_notes?: string
}

interface StatusHistoryEntry {
  from_status: string
  to_status: string
  updated_by: string
  updated_at: string
  notes?: string
}

interface StaffMember {
  id: string
  name: string
  skill_level: number
  current_workload: number
  max_workload: number
}

export const OrderItemStatusManager: React.FC<OrderItemStatusManagerProps> = ({
  orderItemId,
  orderId,
  initialData,
  onStatusChange,
  onNotesUpdate,
  compact = false,
  showModifiers = true
}) => {
  const [activeTab, setActiveTab] = useState('status')
  const [notes, setNotes] = useState('')
  const [showNotesDialog, setShowNotesDialog] = useState(false)
  const [selectedStaff, setSelectedStaff] = useState<string>('')
  const [preparationTimer, setPreparationTimer] = useState<number>(0)
  const [timerRunning, setTimerRunning] = useState(false)

  // Custom hooks
  const {
    orderItem,
    availableStaff,
    loading,
    error,
    updateStatus,
    assignStaff,
    updateNotes,
    startPreparation,
    pausePreparation,
    completePreparation,
    refreshData
  } = useOrderItemStatus(orderItemId, initialData)

  const { subscribe, unsubscribe } = useWebSocket()

  // Subscribe to real-time updates
  useEffect(() => {
    const subscriptionId = subscribe('order', orderId)
    return () => {
      if (subscriptionId) {
        unsubscribe(subscriptionId)
      }
    }
  }, [orderId, subscribe, unsubscribe])

  // Initialize notes from order item data
  useEffect(() => {
    if (orderItem?.preparation_notes) {
      setNotes(orderItem.preparation_notes)
    }
  }, [orderItem?.preparation_notes])

  // Timer management
  useEffect(() => {
    let interval: NodeJS.Timeout

    if (timerRunning && orderItem?.status === 'preparing') {
      interval = setInterval(() => {
        setPreparationTimer(prev => prev + 1)
      }, 1000)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [timerRunning, orderItem?.status])

  // Calculate preparation time
  const getPreparationTime = useCallback(() => {
    if (!orderItem?.preparation_started_at) return 0

    const startTime = new Date(orderItem.preparation_started_at)
    const endTime = orderItem.preparation_completed_at
      ? new Date(orderItem.preparation_completed_at)
      : new Date()

    return Math.floor((endTime.getTime() - startTime.getTime()) / 1000)
  }, [orderItem])

  // Handle status change
  const handleStatusChange = async (newStatus: string) => {
    try {
      await updateStatus(newStatus, {
        updated_via: 'status_manager',
        notes: notes.trim() || undefined
      })

      onStatusChange?.(orderItemId, newStatus, { notes })

      // Auto-start timer for preparing status
      if (newStatus === 'preparing') {
        setTimerRunning(true)
      } else {
        setTimerRunning(false)
      }
    } catch (error) {
      console.error('Failed to update status:', error)
    }
  }

  // Handle staff assignment
  const handleStaffAssignment = async (staffId: string) => {
    try {
      await assignStaff(staffId)
      setSelectedStaff('')
    } catch (error) {
      console.error('Failed to assign staff:', error)
    }
  }

  // Handle notes update
  const handleNotesUpdate = async () => {
    try {
      await updateNotes(notes)
      onNotesUpdate?.(orderItemId, notes)
      setShowNotesDialog(false)
    } catch (error) {
      console.error('Failed to update notes:', error)
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary'
      case 'preparing': return 'warning'
      case 'ready': return 'success'
      case 'served': return 'default'
      default: return 'default'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: number, isOverdue: boolean) => {
    if (isOverdue) return 'destructive'
    if (priority === 1) return 'destructive'
    if (priority === 2) return 'warning'
    return 'default'
  }

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (loading) {
    return (
      <Card className={compact ? 'p-2' : ''}>
        <CardContent className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    )
  }

  if (error || !orderItem) {
    return (
      <Card className={compact ? 'p-2' : ''}>
        <CardContent className="p-4">
          <Alert variant="destructive">
            <AlertDescription>
              {error || 'Order item not found'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  if (compact) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex-1">
              <h4 className="font-medium text-sm">{orderItem.product_name}</h4>
              <p className="text-xs text-muted-foreground">Qty: {orderItem.quantity}</p>
            </div>
            <div className="flex gap-1">
              <Badge variant={getStatusColor(orderItem.status)}>
                {orderItem.status}
              </Badge>
              {orderItem.is_overdue && (
                <Badge variant="destructive">
                  {orderItem.overdue_minutes}m overdue
                </Badge>
              )}
            </div>
          </div>

          <div className="flex gap-1">
            {['pending', 'preparing', 'ready', 'served'].map(status => (
              <Button
                key={status}
                size="sm"
                variant={orderItem.status === status ? 'default' : 'outline'}
                onClick={() => handleStatusChange(status)}
                disabled={orderItem.status === status}
                className="flex-1 text-xs"
              >
                {status}
              </Button>
            ))}
          </div>

          {orderItem.assigned_staff_name && (
            <div className="flex items-center gap-1 mt-2 text-xs">
              <User className="w-3 h-3" />
              <span>{orderItem.assigned_staff_name}</span>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">{orderItem.product_name}</h3>
            <p className="text-sm text-muted-foreground">
              Order #{orderItem.order_id} • Quantity: {orderItem.quantity}
            </p>
          </div>
          <div className="flex gap-2">
            <Badge variant={getPriorityColor(orderItem.priority_level, orderItem.is_overdue)}>
              {orderItem.is_overdue
                ? `${orderItem.overdue_minutes}m overdue`
                : `Priority ${orderItem.priority_level}`
              }
            </Badge>
            <Badge variant={getStatusColor(orderItem.status)}>
              {orderItem.status}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="timer">Timer</TabsTrigger>
            <TabsTrigger value="staff">Staff</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="status" className="space-y-4">
            {/* Status Controls */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {['pending', 'preparing', 'ready', 'served'].map(status => (
                <Button
                  key={status}
                  variant={orderItem.status === status ? 'default' : 'outline'}
                  onClick={() => handleStatusChange(status)}
                  disabled={orderItem.status === status}
                  className="flex items-center gap-2"
                >
                  {status === 'pending' && <Clock className="w-4 h-4" />}
                  {status === 'preparing' && <Timer className="w-4 h-4" />}
                  {status === 'ready' && <CheckCircle className="w-4 h-4" />}
                  {status === 'served' && <CheckCircle className="w-4 h-4" />}
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Button>
              ))}
            </div>

            {/* Special Instructions */}
            {orderItem.special_instructions && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Special Instructions:</strong> {orderItem.special_instructions}
                </AlertDescription>
              </Alert>
            )}

            {/* Department & Staff Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {orderItem.department_name && (
                <div className="flex items-center gap-2">
                  <Settings className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">Department: {orderItem.department_name}</span>
                </div>
              )}
              {orderItem.assigned_staff_name && (
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm">Assigned: {orderItem.assigned_staff_name}</span>
                </div>
              )}
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Preparation Notes</label>
                <Dialog open={showNotesDialog} onOpenChange={setShowNotesDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Edit Notes
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Preparation Notes</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <Textarea
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        placeholder="Add preparation notes, special requirements, or status updates..."
                        rows={4}
                      />
                      <div className="flex gap-2">
                        <Button onClick={handleNotesUpdate}>Save Notes</Button>
                        <Button variant="outline" onClick={() => setShowNotesDialog(false)}>
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
              {orderItem.preparation_notes && (
                <div className="p-3 bg-muted rounded-lg">
                  <p className="text-sm">{orderItem.preparation_notes}</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="timer" className="space-y-4">
            {/* Preparation Timer */}
            <div className="text-center space-y-4">
              <div className="text-3xl font-mono font-bold">
                {formatTime(getPreparationTime())}
              </div>

              {orderItem.estimated_preparation_time && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress</span>
                    <span>
                      {Math.round((getPreparationTime() / (orderItem.estimated_preparation_time * 60)) * 100)}%
                    </span>
                  </div>
                  <Progress
                    value={(getPreparationTime() / (orderItem.estimated_preparation_time * 60)) * 100}
                    className="h-2"
                  />
                  <p className="text-sm text-muted-foreground">
                    Estimated: {orderItem.estimated_preparation_time} minutes
                  </p>
                </div>
              )}

              <div className="flex justify-center gap-2">
                {orderItem.status === 'preparing' ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => setTimerRunning(!timerRunning)}
                    >
                      {timerRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      {timerRunning ? 'Pause' : 'Resume'}
                    </Button>
                    <Button onClick={() => handleStatusChange('ready')}>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Mark Ready
                    </Button>
                  </>
                ) : orderItem.status === 'pending' ? (
                  <Button onClick={() => handleStatusChange('preparing')}>
                    <Play className="w-4 h-4 mr-2" />
                    Start Preparation
                  </Button>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    Timer not available for {orderItem.status} items
                  </div>
                )}
              </div>

              {/* Time Stamps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {orderItem.preparation_started_at && (
                  <div>
                    <p className="font-medium">Started</p>
                    <p className="text-muted-foreground">
                      {format(new Date(orderItem.preparation_started_at), 'HH:mm:ss')}
                    </p>
                  </div>
                )}
                {orderItem.preparation_completed_at && (
                  <div>
                    <p className="font-medium">Completed</p>
                    <p className="text-muted-foreground">
                      {format(new Date(orderItem.preparation_completed_at), 'HH:mm:ss')}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="staff" className="space-y-4">
            {/* Staff Assignment */}
            <div className="space-y-3">
              <h4 className="font-medium">Staff Assignment</h4>

              {orderItem.assigned_staff_name ? (
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{orderItem.assigned_staff_name}</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleStaffAssignment('')}
                  >
                    Unassign
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <Select value={selectedStaff} onValueChange={setSelectedStaff}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select staff member" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableStaff.map(staff => (
                        <SelectItem key={staff.id} value={staff.id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{staff.name}</span>
                            <Badge variant={staff.current_workload >= staff.max_workload ? 'destructive' : 'default'}>
                              {staff.current_workload}/{staff.max_workload}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {selectedStaff && (
                    <Button
                      onClick={() => handleStaffAssignment(selectedStaff)}
                      className="w-full"
                    >
                      Assign Staff
                    </Button>
                  )}
                </div>
              )}
            </div>

            {/* Available Staff */}
            <div className="space-y-3">
              <h4 className="font-medium">Available Staff</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {availableStaff.map(staff => (
                  <div
                    key={staff.id}
                    className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-muted"
                    onClick={() => handleStaffAssignment(staff.id)}
                  >
                    <div>
                      <p className="font-medium text-sm">{staff.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Skill Level {staff.skill_level}
                      </p>
                    </div>
                    <Badge variant={staff.current_workload >= staff.max_workload ? 'destructive' : 'default'}>
                      {staff.current_workload}/{staff.max_workload}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            {/* Status History */}
            <div className="space-y-3">
              <h4 className="font-medium">Status History</h4>
              <div className="space-y-2">
                {orderItem.status_history.map((entry, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="text-sm">
                        <span className="font-medium">{entry.from_status}</span>
                        {' → '}
                        <span className="font-medium">{entry.to_status}</span>
                      </p>
                      <p className="text-xs text-muted-foreground">
                        by {entry.updated_by} • {formatDistanceToNow(new Date(entry.updated_at))} ago
                      </p>
                      {entry.notes && (
                        <p className="text-xs text-muted-foreground mt-1">{entry.notes}</p>
                      )}
                    </div>
                    <Badge variant="outline">
                      {format(new Date(entry.updated_at), 'HH:mm')}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Modifiers Section */}
        {showModifiers && orderItem.modifiers.length > 0 && (
          <div className="mt-6 space-y-3">
            <h4 className="font-medium">Modifiers</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {orderItem.modifiers.map(modifier => (
                <div key={modifier.id} className="flex items-center justify-between p-2 border rounded">
                  <div>
                    <p className="text-sm font-medium">{modifier.name}</p>
                    {modifier.requires_special_skill && (
                      <p className="text-xs text-orange-600">Requires special skill</p>
                    )}
                  </div>
                  <Badge variant={getStatusColor(modifier.status)}>
                    {modifier.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default OrderItemStatusManager

// Export types for use in other components
export type {
  OrderItemStatusManagerProps,
  OrderItemData,
  OrderItemModifier,
  StatusHistoryEntry,
  StaffMember
}
