import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Task from 'App/Models/Task'

export default class extends BaseSeeder {
  public async run() {
    // DISABLED: Tasks are now created by 9ProductHierarchySeeder.ts
    // await Task.createMany([
    //   {
    //     name: 'Buy & Order',
    //   },
    //   {
    //     name: 'Book & Reserve',
    //   },
    //   {
    //     name: 'Subscribe & Notify',
    //   },
    //   {
    //     name: 'Pay & Transact',
    //   },
    //   {
    //     name: 'Survey & Research',
    //   },
    //   {
    //     name: 'Query & Validate',
    //   },
    //   {
    //     name: 'Hire & Rent',
    //   },
    //   {
    //     name: 'Stream',
    //   },
    //   {
    //     name: 'Apply & Register',
    //   },
    //   {
    //     name: 'Report & Fill',
    //   },
    // ])
  }
}
