import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Branch from '../../Models/Branch'
import { bind } from '@adonisjs/route-model-binding'
import Lot from 'App/Models/Lot'

/**
 * @name Branch management
 * @version 1.0.0
 * @description Branch management for the application
 */
export default class BranchLotsController {
  /**
   * @index
   * @summary List all branches
   * @description List all branches, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page Number
   * @paramQuery order - Order by field
   * @paramQuery sort order - (asc, desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const branchQuery = Lot.query()
      .whereHas('section', (sq) => sq.where('branchId', branch.id))
      .filter(filters)

    return await branchQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a branch
   * @description Create a branch with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Branch>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, branch: Branch) {
    const { name, details, sectionId } = request.all()
    Lot.create({ sectionId, name, details })

    const image = request.file('image')
    if (image) {
      branch.merge({ image: Attachment.fromFile(image) })
    }

    await branch.save()

    return response.json(branch)
  }
}
