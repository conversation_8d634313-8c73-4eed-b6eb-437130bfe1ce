import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  beforeCreate,
  beforeSave,
  belongsTo,
  column,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { ulid } from 'ulidx'
import Database from '@ioc:Adonis/Lucid/Database'
import Vendor from './Vendor'
import Branch from './Branch'

type CoverageType = 'polygon' | 'circle' | 'administrative'
type AdministrativeArea = {
  country: string
  administrative_area_level_1?: string
  administrative_area_level_2?: string
  administrative_area_level_3?: string
  administrative_area_level_4?: string
  administrative_area_level_5?: string
}

export default class ServiceArea extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public name: string

  @column()
  public description: string | null

  @column()
  public type: CoverageType

  @column()
  public radius: number | null

  @column()
  public geom: any

  @column()
  public administrativeAreas: AdministrativeArea[] | null

  @column()
  public priority: number

  @column()
  public active: boolean

  @column()
  public meta: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(serviceArea: ServiceArea) {
    serviceArea.id = ulid().toLowerCase()
  }

  @beforeSave()
  public static async setGeom(serviceArea: ServiceArea) {
    if (serviceArea.type === 'polygon' && serviceArea.geom) {
      // The geom should already be in the correct format from the controller
      // Just ensure it's using the correct SRID
      serviceArea.geom = Database.st().geomFromText(serviceArea.geom, 4326)
    }
  }

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  /**
   * Check if a point is within this service area
   */
  public async containsPoint(lat: number, lng: number): Promise<boolean> {
    const point = Database.st().geomFromText(`POINT(${lng} ${lat})`, 4326)
    
    if (this.type === 'circle' && this.radius && this.branch?.location?.coordinates) {
      // For circular areas, check if the point is within the radius
      const branchPoint = Database.st().geomFromText(
        `POINT(${this.branch.location.coordinates.lng} ${this.branch.location.coordinates.lat})`,
        4326
      )
      const distance = Database.st().distanceSphere(point, branchPoint)
      return Number(distance) <= this.radius
    } else if (this.type === 'polygon' && this.geom) {
      // For polygon areas, check if the point is within the polygon
      const result = await Database.rawQuery(
        'SELECT ST_Contains(?, ?) as contains',
        [this.geom, point]
      )
      return result.rows[0].contains
    } else if (this.type === 'administrative' && this.administrativeAreas) {
      // For administrative areas, we need to check if the point's administrative areas
      // match any of the service area's administrative areas
      const pointAreas = await this.getPointAdministrativeAreas(lat, lng)
      return this.administrativeAreas.some(area => this.matchesAdministrativeArea(area, pointAreas))
    }
    
    return false
  }

  /**
   * Get administrative areas for a point using reverse geocoding
   */
  private async getPointAdministrativeAreas(_lat: number, _lng: number): Promise<AdministrativeArea> {
    // TODO: Implement reverse geocoding to get administrative areas
    // This would typically use a service like Google Maps Geocoding API
    // For now, return a mock implementation
    return {
      country: 'KE',
      administrative_area_level_1: 'Nairobi',
      administrative_area_level_2: 'Nairobi',
      administrative_area_level_3: 'Westlands',
    }
  }

  /**
   * Check if two administrative areas match
   */
  private matchesAdministrativeArea(area1: AdministrativeArea, area2: AdministrativeArea): boolean {
    // Match from most specific to least specific
    if (area1.administrative_area_level_5 && area2.administrative_area_level_5) {
      return area1.administrative_area_level_5 === area2.administrative_area_level_5
    }
    if (area1.administrative_area_level_4 && area2.administrative_area_level_4) {
      return area1.administrative_area_level_4 === area2.administrative_area_level_4
    }
    if (area1.administrative_area_level_3 && area2.administrative_area_level_3) {
      return area1.administrative_area_level_3 === area2.administrative_area_level_3
    }
    if (area1.administrative_area_level_2 && area2.administrative_area_level_2) {
      return area1.administrative_area_level_2 === area2.administrative_area_level_2
    }
    if (area1.administrative_area_level_1 && area2.administrative_area_level_1) {
      return area1.administrative_area_level_1 === area2.administrative_area_level_1
    }
    return area1.country === area2.country
  }
} 