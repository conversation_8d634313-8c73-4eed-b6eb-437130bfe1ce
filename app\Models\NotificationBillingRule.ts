import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class NotificationBillingRule extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public type: string

  @column()
  public name: string

  @column()
  public channel: string

  @column()
  public basePrice: number

  @column()
  public volumeThreshold: number | null

  @column()
  public volumeDiscount: number | null

  @column()
  public isActive: boolean

  @column()
  public meta: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  public static async getRule(
    type: string,
    name: string,
    channel: string
  ): Promise<NotificationBillingRule | null> {
    return await this.query()
      .where('type', type)
      .where('name', name)
      .where('channel', channel)
      .where('is_active', true)
      .first()
  }

  public calculateCost(volume: number): number {
    let cost = this.basePrice

    if (this.volumeThreshold && volume >= this.volumeThreshold && this.volumeDiscount) {
      const discount = (cost * this.volumeDiscount) / 100
      cost -= discount
    }

    return cost
  }
} 