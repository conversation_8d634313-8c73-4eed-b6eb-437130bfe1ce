/**
 * Notification Priority Levels
 * 
 * Defines priority levels for notifications to help with
 * frontend styling, ordering, and user attention management.
 */
export class NotificationPriority {
  static readonly HIGH = 'HIGH'
  static readonly MEDIUM = 'MEDIUM'
  static readonly LOW = 'LOW'

  /**
   * Get all available priorities
   */
  static getAll(): string[] {
    return [this.HIGH, this.MEDIUM, this.LOW]
  }

  /**
   * Check if priority is valid
   */
  static isValid(priority: string): boolean {
    return this.getAll().includes(priority)
  }

  /**
   * Get priority description
   */
  static getDescription(priority: string): string {
    switch (priority) {
      case this.HIGH:
        return 'Urgent - requires immediate attention'
      case this.MEDIUM:
        return 'Important - should be addressed soon'
      case this.LOW:
        return 'Informational - can be addressed when convenient'
      default:
        return 'Normal priority'
    }
  }

  /**
   * Get priority weight for sorting (higher number = higher priority)
   */
  static getWeight(priority: string): number {
    switch (priority) {
      case this.HIGH:
        return 3
      case this.MEDIUM:
        return 2
      case this.LOW:
        return 1
      default:
        return 2
    }
  }

  /**
   * Compare two priorities (returns -1, 0, or 1 for sorting)
   */
  static compare(priority1: string, priority2: string): number {
    const weight1 = this.getWeight(priority1)
    const weight2 = this.getWeight(priority2)
    
    if (weight1 > weight2) return -1
    if (weight1 < weight2) return 1
    return 0
  }
}
