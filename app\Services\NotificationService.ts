import NotificationTemplate from 'App/Models/NotificationTemplate'
import NotificationPreference from 'App/Models/NotificationPreference'
import NotificationBillingRule from 'App/Models/NotificationBillingRule'
import NotificationUsage from 'App/Models/NotificationUsage'
import { inject } from '@adonisjs/core/build/standalone'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { DateTime } from 'luxon'

@inject()
export default class NotificationService {
  constructor(protected ctx: HttpContextContract) {}

  public async sendNotification(
    userId: string,
    vendorId: string | null,
    type: string,
    name: string,
    data: Record<string, any>
  ): Promise<void> {
    // Get notification template
    const template = await NotificationTemplate.getTemplate(type, name)
    if (!template) {
      throw new Error(`Notification template not found: ${type}/${name}`)
    }

    // Get user preferences
    const preferences = await NotificationPreference.getUserPreferences(userId, vendorId, type)
    if (!preferences) {
      throw new Error(`Notification preferences not found for user: ${userId}`)
    }

    // Format message
    const { subject, body } = template.formatMessage(data)

    // Send through enabled channels
    for (const channel of template.channels) {
      if (preferences.isChannelEnabled(channel)) {
        try {
          await this.sendThroughChannel(channel, {
            userId,
            subject,
            body,
            data,
            type,
            name,
          })

          // Record successful notification usage
          await this.recordNotificationUsage(userId, vendorId, type, name, channel, 'success')
        } catch (error) {
          // Record failed notification usage
          await this.recordNotificationUsage(userId, vendorId, type, name, channel, 'failed', error.message)
          throw error
        }
      }
    }
  }

  private async sendThroughChannel(
    channel: string,
    notification: {
      userId: string
      subject: string
      body: string
      data: Record<string, any>
      type: string
      name: string
    }
  ): Promise<void> {
    switch (channel) {
      case 'email':
        await this.sendEmail(notification)
        break
      case 'sms':
        await this.sendSMS(notification)
        break
      case 'push':
        await this.sendPushNotification(notification)
        break
      default:
        throw new Error(`Unsupported notification channel: ${channel}`)
    }
  }

  private async sendEmail(notification: {
    userId: string
    subject: string
    body: string
  }): Promise<void> {
    // TODO: Implement email sending using your email service
    console.log('Sending email:', notification)
  }

  private async sendSMS(notification: {
    userId: string
    body: string
  }): Promise<void> {
    // TODO: Implement SMS sending using your SMS service
    console.log('Sending SMS:', notification)
  }

  private async sendPushNotification(notification: {
    userId: string
    subject: string
    body: string
    data: Record<string, any>
  }): Promise<void> {
    // TODO: Implement push notification sending using your push notification service
    console.log('Sending push notification:', notification)
  }

  private async recordNotificationUsage(
    userId: string,
    vendorId: string | null,
    type: string,
    name: string,
    channel: string,
    status: 'success' | 'failed',
    error?: string
  ): Promise<void> {
    // Get billing rule
    const rule = await NotificationBillingRule.getRule(type, name, channel)
    if (!rule) {
      throw new Error(`Billing rule not found for: ${type}/${name}/${channel}`)
    }

    // Get usage count for volume discount
    const startDate = DateTime.now().startOf('month')
    const endDate = DateTime.now().endOf('month')
    const usage = await NotificationUsage.query()
      .where('user_id', userId)
      .where('type', type)
      .where('name', name)
      .where('channel', channel)
      .whereBetween('created_at', [startDate.toSQL(), endDate.toSQL()])
      .count('* as count')
      .first()

    const volume = usage ? parseInt(usage.$extras.count) : 0
    const cost = rule.calculateCost(volume)

    // Create usage record
    await NotificationUsage.create({
      recipient: userId, // Use 'recipient' instead of 'userId'
      vendorId: vendorId || undefined, // Convert null to undefined
      notificationType: type,
      channel,
      cost,
      currency: 'KES', // Default currency
      status: status === 'success' ? 'billed' : 'failed', // Map to valid status values
      billingPeriod: DateTime.now().toFormat('yyyy-MM'), // Add required billing period
      meta: error ? { error } : null,
    })
  }

  public async getNotificationCost(
    userId: string,
    vendorId: string | null,
    startDate: DateTime,
    endDate: DateTime
  ): Promise<number> {
    return await NotificationUsage.getTotalCost(userId, vendorId, startDate, endDate)
  }
} 