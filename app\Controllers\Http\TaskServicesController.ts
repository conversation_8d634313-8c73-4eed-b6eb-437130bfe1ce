import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Task from '../../Models/Task'
import Service from '../../Models/Service'
import { validator } from '@ioc:Adonis/Core/Validator'
import UpdateServiceValidator from 'App/Validators/UpdateServiceValidator'

export default class TaskServicesController {
  @bind()
  /**
   * @index
   * @summary Show all services
   * @version 1.0.0
   * @description Service management for the application
   * @paramUse(filterable)
   * @paramPath taskId required number - Task ID
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract, task: Task) {
    const { per = 20, page = 1, order = 'order', sort = 'asc', ...filters } = request.qs()
    const serviceQuery = task.related('services').query().filter(filters)

    return await serviceQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()
  /**
   * @store
   * @summary Create a service
   * @description Create a service with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @paramPath taskId required number - Task ID
   */
  public async store({ request, response }: HttpContextContract, task: Task) {
    const { name, details, featured = false, order = 0 } = request.all()

    // Check global featured limit when creating new service
    if (featured) {
      const featuredCount = await Service.query()
        .where('featured', true)
        .count('* as total')
        .first()

      if (featuredCount && featuredCount.$extras.total >= 16) {
        return response.badRequest({
          error: 'Maximum of 16 featured services allowed across all tasks'
        })
      }
    }

    const service = await task.related('services').create({
      name,
      details,
      taskId: task.id,
      featured,
      order
    })

    const image = request.file('image')
    if (image) {
      await service.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(service)
  }

  @bind()
  /**
   * @show
   * @summary Show a single Service
   * @description Show a Service with their details (name and details)
   * @responseBody 200 - <Service>
   * @response 404 - Service not found
   */
  public async show({ response }: HttpContextContract, task: Task, service: Service) {
    return response.json({ ...service, task })
  }

  @bind()
  /**
   * @update
   * @summary Update a Service
   * @description Update a Service with their details (name and details)
   * @requestBody <Service>
   * @responseBody 200 - <Service>
   * @response 404 - Service not found
   */
  public async update({ request, response }: HttpContextContract, task: Task, service: Service) {
    const { image: uploadedImage, ...input } = request.all()

    // Validate basic fields
    await validator.validate({
      schema: new UpdateServiceValidator(request).schema,
      data: input
    })

    // Check global featured limit when updating
    if (input.featured === true && !service.featured) {
      const featuredCount = await Service.query()
        .where('featured', true)
        .whereNot('id', service.id)
        .count('* as total')
        .first()

      if (featuredCount && featuredCount.$extras.total >= 16) {
        return response.badRequest({
          error: 'Maximum of 16 featured services allowed across all tasks'
        })
      }
    }

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await service.merge(input).save()

    return response.json({ ...service, task })
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Service
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, service: Service) {
    return await service.delete()
  }
}
