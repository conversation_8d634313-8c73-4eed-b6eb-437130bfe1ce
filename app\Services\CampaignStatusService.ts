import { DateTime } from 'luxon'
import Campaign from 'App/Models/Campaign'
import Logger from '@ioc:Adonis/Core/Logger'

export default class CampaignStatusService {
  /**
   * Valid status transitions
   */
  private static readonly VALID_TRANSITIONS: Record<Campaign['status'], Campaign['status'][]> = {
    'Draft': ['Pending'],
    'Pending': ['Approved', 'Expired'],
    'Approved': ['Expired'],
    'Expired': [],
  }

  /**
   * Change campaign status with validation
   */
  public static async changeStatus(
    campaign: Campaign,
    newStatus: Campaign['status'],
    userId?: string,
    // rejectionReason?: string
  ): Promise<boolean> {
    try {
      // Validate transition
      if (!this.isValidTransition(campaign.status, newStatus)) {
        throw new Error(`Invalid status transition from ${campaign.status} to ${newStatus}`)
      }

      // Update campaign status
      campaign.status = newStatus

      // Handle approval-specific updates
      if (newStatus === 'Approved') {
        campaign.approvedById = userId || null
        campaign.approvedAt = DateTime.now()
        campaign.rejectionReason = null
      }

      // Handle expiration
      if (newStatus === 'Expired') {
        // Validate campaign dates
        if (!campaign.endDate) {
          throw new Error('Campaign must have an end date to be expired')
        }
        if (campaign.endDate > DateTime.now()) {
          throw new Error('Campaign cannot be expired before end date')
        }
      }

      await campaign.save()

      Logger.info('Campaign status changed', {
        campaign_id: campaign.id,
        old_status: campaign.status,
        new_status: newStatus,
        user_id: userId,
      })

      return true
    } catch (error) {
      Logger.error('Failed to change campaign status', {
        error_message: error.message,
        error_stack: error.stack,
        campaign_id: campaign.id,
        new_status: newStatus,
      })
      throw error
    }
  }

  /**
   * Check if status transition is valid
   */
  private static isValidTransition(currentStatus: Campaign['status'], newStatus: Campaign['status']): boolean {
    const allowedTransitions = this.VALID_TRANSITIONS[currentStatus] || []
    return allowedTransitions.includes(newStatus)
  }

  /**
   * Check and update expired campaigns
   */
  public static async checkExpiredCampaigns(): Promise<void> {
    try {
      const expiredCampaigns = await Campaign.query()
        .whereIn('status', ['Pending', 'Approved'])
        .where('endDate', '<', DateTime.now().toSQL())

      for (const campaign of expiredCampaigns) {
        await this.changeStatus(campaign, 'Expired')
      }

      Logger.info('Expired campaigns checked', {
        count: expiredCampaigns.length,
      })
    } catch (error) {
      Logger.error('Failed to check expired campaigns', { error })
      throw error
    }
  }
} 