import { NotificationContract } from '@ioc:Verful/Notification'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import User from 'App/Models/User'

export default class SendPasswordResetLink implements NotificationContract {
  public via(_notifiable) {
    return ['mail' as const]
  }

  public toMail(notifiable: User) {
    return new CustomerMailer(notifiable, 'mails/password/forgot', {
      greeting: `Hello ${notifiable.firstName}!`,
      subject: 'Reset Your Password',
      intro: `We received a request to reset your password. If you didn't make the request, just ignore this email. Otherwise, you can reset your password using a one-time password (OTP) below.`,
      otp: notifiable.otp,
    })
  }
}
