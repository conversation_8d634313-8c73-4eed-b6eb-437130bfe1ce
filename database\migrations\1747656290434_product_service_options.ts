import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'product_service_options'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE').index()
      table.string('service_option_id').references('id').inTable('service_options').onDelete('CASCADE').index()
      
      // Pivot-specific fields
      table.decimal('price_adjustment_override', 10, 2).nullable()
      table.boolean('is_default').notNullable().defaultTo(false).index()
      table.integer('sort_order').notNullable().defaultTo(0).index()
      
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index(['product_id', 'sort_order'])
      table.index(['product_id', 'is_default'])
      table.index(['service_option_id'])
      
      // Unique constraint to prevent duplicate options per product
      table.unique(['product_id', 'service_option_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
