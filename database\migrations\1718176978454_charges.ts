import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'charges'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE').nullable()
      table.string('task_id').references('id').inTable('tasks').onDelete('CASCADE').nullable()
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE').nullable()
      table.enum('type', ['fixed', 'percentage']).defaultTo('fixed')
      table.float('amount').defaultTo(0)
      table.string('currency').nullable()
      table.jsonb('meta').nullable()
      table.enum('status', ['Pending', 'Success', 'Failed']).defaultTo('Pending')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
