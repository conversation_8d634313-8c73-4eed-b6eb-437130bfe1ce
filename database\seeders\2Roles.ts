import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Permission from '@ioc:Verful/Permissions/Permission'
import Role from '@ioc:Verful/Permissions/Role'

export default class extends BaseSeeder {
  public async run() {
    // Create roles
    const admin = await Role.create({ name: 'admin' })
    const vendor = await Role.create({ name: 'vendor' })
    const employee = await Role.create({ name: 'employee' })
    const accounts = await Role.create({ name: 'accounts' })
    const customer = await Role.create({ name: 'customer' })
    const student = await Role.create({ name: 'student' })

    // Create permissions
    await Permission.create({ name: 'create-users' })
    await Permission.create({ name: 'read-users' })
    await Permission.create({ name: 'update-users' })
    await Permission.create({ name: 'delete-users' })

    await Permission.create({ name: 'create-deals' })
    await Permission.create({ name: 'read-deals' })
    await Permission.create({ name: 'update-deals' })
    await Permission.create({ name: 'delete-deals' })

    await Permission.create({ name: 'create-payments' })
    await Permission.create({ name: 'read-payments' })
    await Permission.create({ name: 'update-payments' })
    await Permission.create({ name: 'delete-payments' })

    await Permission.create({ name: 'create-claims' })
    await Permission.create({ name: 'read-claims' })
    await Permission.create({ name: 'update-claims' })
    await Permission.create({ name: 'delete-claims' })

    // Assign permissions to roles
    await admin.givePermissionTo('create-users')
    await admin.givePermissionTo('read-users')
    await admin.givePermissionTo('update-users')
    await admin.givePermissionTo('delete-users')

    await admin.givePermissionTo('create-deals')
    await admin.givePermissionTo('read-deals')
    await admin.givePermissionTo('update-deals')
    await admin.givePermissionTo('delete-deals')

    await admin.givePermissionTo('create-payments')
    await admin.givePermissionTo('read-payments')
    await admin.givePermissionTo('update-payments')
    await admin.givePermissionTo('delete-payments')

    await admin.givePermissionTo('create-claims')
    await admin.givePermissionTo('read-claims')
    await admin.givePermissionTo('update-claims')
    await admin.givePermissionTo('delete-claims')

    await vendor.givePermissionTo('create-users')
    await vendor.givePermissionTo('read-users')
    await vendor.givePermissionTo('update-users')
    await vendor.givePermissionTo('delete-users')

    await vendor.givePermissionTo('create-deals')
    await vendor.givePermissionTo('read-deals')
    await vendor.givePermissionTo('update-deals')
    await vendor.givePermissionTo('delete-deals')

    await vendor.givePermissionTo('create-payments')
    await vendor.givePermissionTo('read-payments')
    await vendor.givePermissionTo('update-payments')
    await vendor.givePermissionTo('delete-payments')

    await vendor.givePermissionTo('create-claims')
    await vendor.givePermissionTo('read-claims')
    await vendor.givePermissionTo('update-claims')
    await vendor.givePermissionTo('delete-claims')

    await employee.givePermissionTo('create-deals')
    await employee.givePermissionTo('read-deals')
    await employee.givePermissionTo('update-deals')
    await employee.givePermissionTo('delete-deals')

    await accounts.givePermissionTo('create-payments')
    await accounts.givePermissionTo('read-payments')
    await accounts.givePermissionTo('update-payments')
    await accounts.givePermissionTo('delete-payments')

    await customer.givePermissionTo('create-claims')
    await customer.givePermissionTo('read-claims')
    await customer.givePermissionTo('update-claims')

    await student.givePermissionTo('create-claims')
    await student.givePermissionTo('read-claims')
    await student.givePermissionTo('update-claims')
  }
}
