import test from 'japa'
import { DateTime } from 'luxon'
import BookingAvailabilityService from 'App/Services/BookingAvailabilityService'
import Product from 'App/Models/Product'
import Branch from 'App/Models/Branch'
import Vendor from 'App/Models/Vendor'
import User from 'App/Models/User'
import Duration from 'App/Models/Duration'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption from 'App/Models/ServiceConfigurationOption'
import { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'

test.group('Booking Availability Service', (group) => {
  let vendor: Vendor
  let branch: Branch
  let customer: User
  let product: Product
  let duration: Duration
  let serviceConfiguration: ServiceConfiguration

  group.beforeEach(async () => {
    // Create test vendor
    vendor = await Vendor.create({
      name: 'Test Spa',
      email: '<EMAIL>',
      phone: '1234567890',
      active: true
    })

    // Create test branch
    branch = await Branch.create({
      vendorId: vendor.id,
      name: 'Main Branch',
      details: 'Main spa location',
      location: {
        name: 'Test Location',
        coordinates: { lat: 0, lng: 0 }
      },
      phone: '1234567890'
    })

    // Create test customer
    customer = await User.create({
      name: 'Test Customer',
      email: '<EMAIL>',
      phone: '1234567890',
      password: 'password123'
    })

    // Create test duration
    duration = await Duration.create({
      name: 'Standard Massage (60 minutes)',
      description: '60-minute relaxing massage',
      minutes: 60,
      bufferMinutes: 15,
      category: 'medium',
      maxConcurrent: 3,
      allowsBackToBack: false,
      requiredBreakAfter: 30,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 8,
        timeSlots: [],
        blackoutDays: ['sunday']
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: ['massage_table']
      },
      active: true
    })

    // Create service configuration
    serviceConfiguration = await ServiceConfiguration.create({
      name: 'Massage Service Configuration',
      description: 'Configuration for massage services',
      active: true
    })

    // Create service configuration option
    await ServiceConfigurationOption.create({
      serviceConfigurationId: serviceConfiguration.id,
      name: 'Standard Duration',
      type: ServiceOptionType.DURATION,
      description: '60-minute session',
      priceAdjustment: 0,
      durationId: duration.id,
      isDefault: true,
      sortOrder: 1,
      constraints: {},
      active: true
    })

    // Create test product (service)
    product = await Product.create({
      name: 'Relaxing Massage',
      details: 'A relaxing full-body massage',
      price: 100,
      vendorId: vendor.id,
      branchId: branch.id,
      serviceConfigurationId: serviceConfiguration.id,
      active: true,
      type: 'Service'
    })
  })

  test('should get available slots for a service', async (assert) => {
    const preferredDate = DateTime.now().plus({ days: 1 }).startOf('day')

    const availability = await BookingAvailabilityService.getAvailableSlots({
      productId: product.id,
      branchId: branch.id,
      selectedOptions: [],
      preferredDate,
      customerId: customer.id
    })

    assert.exists(availability)
    assert.equal(availability.date, preferredDate.toFormat('yyyy-MM-dd'))
    assert.isArray(availability.availableSlots)
    assert.isAbove(availability.totalSlots, 0)
    assert.exists(availability.constraints)
    assert.equal(availability.constraints.minAdvanceHours, 2)
    assert.include(availability.constraints.blackoutDays, 'sunday')
  })

  test('should respect blackout days', async (assert) => {
    // Try to get availability for a Sunday (blackout day)
    let sunday = DateTime.now().plus({ days: 1 })
    while (sunday.weekday !== 7) { // 7 = Sunday
      sunday = sunday.plus({ days: 1 })
    }

    try {
      await BookingAvailabilityService.getAvailableSlots({
        productId: product.id,
        branchId: branch.id,
        selectedOptions: [],
        preferredDate: sunday,
        customerId: customer.id
      })
      assert.fail('Should have thrown an error for blackout day')
    } catch (error) {
      assert.include(error.message, 'not available on sundays')
    }
  })

  test('should respect minimum advance booking time', async (assert) => {
    // Try to book for tomorrow (less than 2 hours advance)
    const tooSoon = DateTime.now().plus({ hours: 1 })

    try {
      await BookingAvailabilityService.getAvailableSlots({
        productId: product.id,
        branchId: branch.id,
        selectedOptions: [],
        preferredDate: tooSoon,
        customerId: customer.id
      })
      assert.fail('Should have thrown an error for insufficient advance time')
    } catch (error) {
      assert.include(error.message, 'at least 2 hours in advance')
    }
  })

  test('should respect maximum advance booking time', async (assert) => {
    // Try to book too far in advance (more than 30 days)
    const tooFar = DateTime.now().plus({ days: 35 })

    try {
      await BookingAvailabilityService.getAvailableSlots({
        productId: product.id,
        branchId: branch.id,
        selectedOptions: [],
        preferredDate: tooFar,
        customerId: customer.id
      })
      assert.fail('Should have thrown an error for booking too far in advance')
    } catch (error) {
      assert.include(error.message, 'more than 30 days in advance')
    }
  })

  test('should generate time slots within branch hours', async (assert) => {
    const preferredDate = DateTime.now().plus({ days: 1 }).startOf('day')

    const availability = await BookingAvailabilityService.getAvailableSlots({
      productId: product.id,
      branchId: branch.id,
      selectedOptions: [],
      preferredDate,
      customerId: customer.id
    })

    // Check that all slots are within reasonable business hours
    availability.availableSlots.forEach(slot => {
      const hour = slot.startTime.hour
      assert.isAtLeast(hour, 9, 'Slot should not be before 9 AM')
      assert.isAtMost(hour, 16, 'Slot should not be after 4 PM')
    })
  })

  test('should calculate correct slot duration including buffer', async (assert) => {
    const preferredDate = DateTime.now().plus({ days: 1 }).startOf('day')

    const availability = await BookingAvailabilityService.getAvailableSlots({
      productId: product.id,
      branchId: branch.id,
      selectedOptions: [],
      preferredDate,
      customerId: customer.id
    })

    // Check that slots have correct duration (60 minutes service + 15 minutes buffer = 75 minutes total)
    availability.availableSlots.forEach(slot => {
      const slotDuration = slot.endTime.diff(slot.startTime, 'minutes').minutes
      assert.equal(slotDuration, 60, 'Service slot should be 60 minutes (excluding buffer)')
    })
  })

  test('should reserve a time slot temporarily', async (assert) => {
    const startTime = DateTime.now().plus({ days: 1, hours: 10 })
    const endTime = startTime.plus({ hours: 1 })

    const reservation = await BookingAvailabilityService.reserveSlot(
      product.id,
      branch.id,
      startTime,
      endTime,
      customer.id,
      []
    )

    assert.isTrue(reservation.reserved)
    assert.exists(reservation.reservationId)
    assert.exists(reservation.expiresAt)
    
    // Reservation should expire in 15 minutes
    const expirationTime = DateTime.fromJSDate(reservation.expiresAt!)
    const expectedExpiration = DateTime.now().plus({ minutes: 15 })
    const timeDiff = Math.abs(expirationTime.diff(expectedExpiration, 'minutes').minutes)
    assert.isBelow(timeDiff, 1, 'Reservation should expire in approximately 15 minutes')
  })

  group.afterEach(async () => {
    // Clean up test data
    await product.delete()
    await serviceConfiguration.delete()
    await duration.delete()
    await branch.delete()
    await vendor.delete()
    await customer.delete()
  })
})
