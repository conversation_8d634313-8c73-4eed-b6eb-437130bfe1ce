/*
|--------------------------------------------------------------------------
| Validating Environment Variables
|--------------------------------------------------------------------------
|
| In this file we define the rules for validating environment variables.
| By performing validation we ensure that your application is running in
| a stable environment with correct configuration values.
|
| This file is read automatically by the framework during the boot lifecycle
| and hence do not rename or move this file to a different location.
|
*/

import Env from '@ioc:Adonis/Core/Env'

export default Env.rules({
  HOST: Env.schema.string({ format: 'host' }),
  PORT: Env.schema.number(),
  APP_KEY: Env.schema.string(),
  APP_NAME: Env.schema.string(),

  DRIVE_DISK: Env.schema.enum(['local', 's3'] as const),

  NODE_ENV: Env.schema.enum(['development', 'production', 'test'] as const),

  PG_HOST: Env.schema.string({ format: 'host' }),
  PG_PORT: Env.schema.number(),
  PG_USER: Env.schema.string(),
  PG_PASSWORD: Env.schema.string.optional(),
  PG_DB_NAME: Env.schema.string(),

  MYSQL_HOST: Env.schema.string({ format: 'host' }),
  MYSQL_PORT: Env.schema.number(),
  MYSQL_USER: Env.schema.string(),
  MYSQL_PASSWORD: Env.schema.string.optional(),
  MYSQL_DATABASE: Env.schema.string(),

  GOOGLE_CLIENT_ID: Env.schema.string.optional(),
  GOOGLE_CLIENT_SECRET: Env.schema.string.optional(),
  FACEBOOK_CLIENT_ID: Env.schema.string.optional(),
  FACEBOOK_CLIENT_SECRET: Env.schema.string.optional(),
  APPLE_APP_ID: Env.schema.string.optional(),
  APPLE_TEAM_ID: Env.schema.string.optional(),
  APPLE_CLIENT_ID: Env.schema.string.optional(),
  APPLE_CLIENT_SECRET: Env.schema.string.optional(),

  SMTP_HOST: Env.schema.string({ format: 'host' }),
  SMTP_PORT: Env.schema.number(),
  SMTP_USERNAME: Env.schema.string(),
  SMTP_PASSWORD: Env.schema.string(),
  MAIL_DOMAIN: Env.schema.string.optional(),

  REDIS_CONNECTION: Env.schema.enum(['local'] as const),
  REDIS_HOST: Env.schema.string(), //{ format: 'host' }
  REDIS_PORT: Env.schema.number(),
  REDIS_PASSWORD: Env.schema.string.optional(),

  QUEUE_REDIS_HOST: Env.schema.string(), //{ format: 'host' }
  QUEUE_REDIS_PORT: Env.schema.number(),
  QUEUE_REDIS_PASSWORD: Env.schema.string.optional(),

  S3_KEY: Env.schema.string(),
  S3_SECRET: Env.schema.string(),
  S3_BUCKET: Env.schema.string(),
  S3_REGION: Env.schema.string(),
  S3_ENDPOINT: Env.schema.string.optional(),

  SMS_PARTNER_ID: Env.schema.number(),
  SMS_API_KEY: Env.schema.string(),
  SMS_SHORT_CODE: Env.schema.string(),

  // M-Pesa Configuration
  MPESA_CONSUMER_KEY: Env.schema.string(),
  MPESA_CONSUMER_SECRET: Env.schema.string(),
  MPESA_ENVIRONMENT: Env.schema.enum(['sandbox', 'live', 'production'] as const),
  MPESA_STORE: Env.schema.number(),
  MPESA_SHORTCODE: Env.schema.number(),
  MPESA_PASSKEY: Env.schema.string(),
  MPESA_INITIATOR_NAME: Env.schema.string.optional(),
  MPESA_INITIATOR_PASSWORD: Env.schema.string.optional(),
  MPESA_CALLBACK_DOMAIN: Env.schema.string.optional(),
  MPESA_VALIDATION_URL: Env.schema.string.optional(),
  MPESA_CONFIRMATION_URL: Env.schema.string.optional(),
  // Additional variables for official library
  MPESA_LIPA_NA_MPESA_SHORTCODE: Env.schema.number.optional(),
  MPESA_SECURITY_CREDENTIAL: Env.schema.string.optional(),
  MPESA_CERT_PATH: Env.schema.string.optional(),
})
