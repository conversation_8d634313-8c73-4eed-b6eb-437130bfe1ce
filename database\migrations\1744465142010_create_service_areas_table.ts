import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'service_areas'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE')
      table.string('name').notNullable()
      table.text('description').nullable()
      table.enum('type', ['polygon', 'circle']).notNullable()
      table.float('radius').nullable() // For circular areas
      table.geometry('geom').nullable() // For polygon areas
      table.integer('priority').defaultTo(0)
      table.boolean('active').defaultTo(true)
      table.json('meta').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })

    // Add indexes
    this.schema.alterTable(this.tableName, (table) => {
      table.index('vendor_id')
      table.index('branch_id')
      table.index('active')
      table.index('priority')
      // Add spatial index using GIST
      table.index('geom', undefined, 'gist')
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 