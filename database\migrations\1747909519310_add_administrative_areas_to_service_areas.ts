import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'service_areas'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add administrative area fields
      table.json('administrative_areas').nullable()
      table.enum('coverage_type', ['polygon', 'circle', 'administrative']).notNullable().defaultTo('polygon')
      table.index('coverage_type')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('administrative_areas')
      table.dropColumn('coverage_type')
    })
  }
}
