import { schema, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'


export default class CreateOrderValidator {
  constructor(protected ctx: HttpContextContract) {}

  /*
   * Define schema to validate the "shape", "type", "formatting" and "integrity" of data.
   *
   * For example:
   * 1. The username must be of data type string. But then also, it should
   *    not contain special characters or numbers.
   *    ```
   *     schema.string([ rules.alpha() ])    ```
   *
   * 2. The email must be of data type string, formatted as a valid
   *    email. But also, not used by any other user.
   *    ```
   *     schema.string([
   *       rules.email(),
   *       rules.unique({ table: 'users', column: 'email' }),
   *     ])    ```
   */
  public schema = schema.create({
    serviceId: schema.string([
      rules.exists({ table: 'services', column: 'id' })
    ]),
    action: schema.string([
      rules.exists({ 
        table: 'service_actions', 
        column: 'action',
        where: {
          service_id: this.ctx.request.input('serviceId'),
          is_active: true
        }
      })
    ]),
    type: schema.enum(['Preorder', 'Instant']),
    delivery: schema.enum(['Takeaway', 'Dinein', 'Delivery', 'Selfpick']),
    status: schema.enum.optional([
      'Pending',
      'Placed',
      'Processing',
      'Ready',
      'Delivering',
      'Delivered',
      'Completed',
      'Cancelled'
    ]),
    meta: schema.object.optional().members({}),
    items: schema.array.optional().members(
      schema.object().members({
        productId: schema.string([
          rules.exists({ table: 'products', column: 'id' })
        ]),
        quantity: schema.number(),
        selected_modifiers: schema.array.optional().members(
          schema.object().members({
            modifier_option_id: schema.string([
              rules.exists({ table: 'modifier_options', column: 'id' })
            ]),
            quantity: schema.number.optional([
              rules.unsigned(),
              rules.range(1, 100) // Basic range validation instead of complex custom logic
            ])
          })
        )
      })
    )
  })

  /**
   * Custom messages for validation failures. You can make use of dot notation `(.)`
   * for targeting nested fields and array expressions `(*)` for targeting all
   * children of an array. For example:
   *
   * {
   *   'profile.username.required': 'Username is required',
   *   'scores.*.number': 'Define scores as valid numbers'
   * }
   *
   */
  public messages = {
    'serviceId.required': 'Service is required',
    'serviceId.exists': 'Selected service does not exist',
    'action.required': 'Action is required',
    'action.exists': 'Selected action is not allowed for this service',
    'type.required': 'Order type is required',
    'type.enum': 'Invalid order type',
    'delivery.required': 'Delivery method is required',
    'delivery.enum': 'Invalid delivery method',
    'status.enum': 'Invalid status',
    'items.*.productId.exists': 'Selected product does not exist',
    'items.*.quantity.required': 'Quantity is required',
    'items.*.selected_modifiers.*.modifier_option_id.exists': 'Selected modifier option does not exist',
    'items.*.selected_modifiers.*.quantity.range': 'Modifier quantity must be between 1 and 100'
  }
}
