# Dependencies
node_modules
bun.lockb
yarn.lock
package-lock.json

# Build output
build
dist
dist-bkup
coverage

# IDE and editor files
.vscode
.idea
*.swp
*.swo
.DS_Store

# Environment variables
.env
.env.*
!.env.example

# Logs
logs
/logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
tmp
temp
*.tmp
*.temp

# System Files
Thumbs.db
ehthumbs.db
Desktop.ini

# Project specific
google-services-*.json
sales.json
ace-manifest.json
/docs

# Bun specific
bun.lock