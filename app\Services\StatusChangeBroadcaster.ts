import WebSocketManager from './WebSocketManager'
import OrderItem from '../Models/OrderItem'
import OrderItemModifier from '../Models/OrderItemModifier'
import Order from '../Models/Order'
import { DateTime } from 'luxon'
import Event from '@ioc:Adonis/Core/Event'

export interface StatusChangeEvent {
  type: 'order' | 'item' | 'modifier'
  entityId: string | number
  status: string
  previousStatus: string
  updatedBy?: string
  updatedAt: DateTime
  metadata?: Record<string, any>
}

export interface BroadcastContext {
  orderId: string
  vendorId: string
  branchId?: string
  departmentId?: string
  customerId?: string
  assignedStaffId?: string
  preparedByStaffId?: string
}

/**
 * Service for broadcasting status change events in real-time
 */
export default class StatusChangeBroadcaster {

  /**
   * Broadcast order status change
   */
  public static async broadcastOrderStatusChange(
    order: Order,
    previousStatus: string,
    updatedBy?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Load necessary relationships
      await order.load('customer')
      await order.load('vendor')
      await order.load('branch')
      await order.load('items', (itemQuery) => {
        itemQuery.preload('department')
      })

      const context: BroadcastContext = {
        orderId: order.id,
        vendorId: order.vendorId,
        branchId: order.branchId,
        customerId: order.userId
      }

      const statusChangeEvent: StatusChangeEvent = {
        type: 'order',
        entityId: order.id,
        status: order.status,
        previousStatus,
        updatedBy,
        updatedAt: DateTime.now(),
        metadata
      }

      // Broadcast to WebSocket clients
      WebSocketManager.broadcastOrderStatusUpdate({
        orderId: order.id,
        vendorId: order.vendorId,
        branchId: order.branchId,
        customerId: order.userId,
        status: order.status,
        previousStatus,
        updatedBy,
        timestamp: DateTime.now()
      })

      // Emit internal event for other listeners
      Event.emit('order:status:changed', {
        order,
        previousStatus,
        updatedBy,
        metadata,
        context
      })

      // Log the status change
      this.logStatusChange('order', order.id, order.status, previousStatus, updatedBy, context)

    } catch (error) {
      console.error('Error broadcasting order status change:', error)
    }
  }

  /**
   * Broadcast order item status change
   */
  public static async broadcastItemStatusChange(
    orderItem: OrderItem,
    previousStatus: string,
    updatedBy?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Load necessary relationships
      await orderItem.load('order', (orderQuery) => {
        orderQuery.preload('customer')
      })
      await orderItem.load('department')
      await orderItem.load('assignedStaff')
      await orderItem.load('product')

      const context: BroadcastContext = {
        orderId: orderItem.orderId,
        vendorId: orderItem.order.vendorId,
        branchId: orderItem.order.branchId,
        departmentId: orderItem.departmentId,
        customerId: orderItem.order.userId,
        assignedStaffId: orderItem.assignedStaffId
      }

      const statusChangeEvent: StatusChangeEvent = {
        type: 'item',
        entityId: orderItem.id,
        status: orderItem.status,
        previousStatus,
        updatedBy,
        updatedAt: DateTime.now(),
        metadata: {
          ...metadata,
          product_name: orderItem.product?.name,
          department_name: orderItem.department?.name,
          assigned_staff_name: orderItem.assignedStaff?.name,
          estimated_preparation_time: orderItem.estimatedPreparationTime,
          is_overdue: orderItem.isOverdue
        }
      }

      // Broadcast to WebSocket clients
      WebSocketManager.broadcastItemStatusUpdate({
        orderId: orderItem.orderId,
        itemId: orderItem.id,
        vendorId: orderItem.order.vendorId,
        branchId: orderItem.order.branchId,
        departmentId: orderItem.departmentId,
        status: orderItem.status,
        previousStatus,
        assignedStaffId: orderItem.assignedStaffId,
        updatedBy,
        timestamp: DateTime.now()
      })

      // Emit internal event
      Event.emit('item:status:changed', {
        orderItem,
        previousStatus,
        updatedBy,
        metadata: statusChangeEvent.metadata,
        context
      })

      // Check if this status change affects order completion
      if (['ready', 'served'].includes(orderItem.status)) {
        Event.emit('item:completed', {
          orderItem,
          context
        })
      }

      // Check for overdue status
      if (orderItem.isOverdue && orderItem.status === 'preparing') {
        this.broadcastOverdueAlert(orderItem, context)
      }

      // Log the status change
      this.logStatusChange('item', orderItem.id, orderItem.status, previousStatus, updatedBy, context)

    } catch (error) {
      console.error('Error broadcasting item status change:', error)
    }
  }

  /**
   * Broadcast modifier status change
   */
  public static async broadcastModifierStatusChange(
    modifier: OrderItemModifier,
    previousStatus: string,
    updatedBy?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Load necessary relationships
      await modifier.load('orderItem', (itemQuery) => {
        itemQuery.preload('order', (orderQuery) => {
          orderQuery.preload('customer')
        })
        itemQuery.preload('department')
        itemQuery.preload('product')
      })
      await modifier.load('preparedByStaff')
      await modifier.load('modifierOption')

      const context: BroadcastContext = {
        orderId: modifier.orderItem.orderId,
        vendorId: modifier.orderItem.order.vendorId,
        branchId: modifier.orderItem.order.branchId,
        departmentId: modifier.orderItem.departmentId,
        customerId: modifier.orderItem.order.userId,
        preparedByStaffId: modifier.preparedByStaffId
      }

      const statusChangeEvent: StatusChangeEvent = {
        type: 'modifier',
        entityId: modifier.id,
        status: modifier.status,
        previousStatus,
        updatedBy,
        updatedAt: DateTime.now(),
        metadata: {
          ...metadata,
          modifier_name: modifier.modifierOption?.name,
          product_name: modifier.orderItem.product?.name,
          department_name: modifier.orderItem.department?.name,
          prepared_by_staff_name: modifier.preparedByStaff?.name,
          complexity_level: modifier.complexityLevel,
          requires_special_skill: modifier.requiresSpecialSkill
        }
      }

      // Broadcast to WebSocket clients
      WebSocketManager.broadcastModifierStatusUpdate({
        orderId: modifier.orderItem.orderId,
        itemId: modifier.orderItemId,
        modifierId: modifier.id,
        vendorId: modifier.orderItem.order.vendorId,
        departmentId: modifier.orderItem.departmentId,
        status: modifier.status,
        previousStatus,
        preparedByStaffId: modifier.preparedByStaffId,
        updatedBy,
        timestamp: DateTime.now()
      })

      // Emit internal event
      Event.emit('modifier:status:changed', {
        modifier,
        previousStatus,
        updatedBy,
        metadata: statusChangeEvent.metadata,
        context
      })

      // Check if this modifier completion affects item status
      if (['completed', 'skipped'].includes(modifier.status)) {
        Event.emit('modifier:completed', {
          modifier,
          context
        })
      }

      // Log the status change
      this.logStatusChange('modifier', modifier.id, modifier.status, previousStatus, updatedBy, context)

    } catch (error) {
      console.error('Error broadcasting modifier status change:', error)
    }
  }

  /**
   * Broadcast department workload update
   */
  public static async broadcastDepartmentWorkloadUpdate(
    departmentId: string,
    vendorId: string,
    branchId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Calculate current workload data
      const workloadData = await this.calculateDepartmentWorkload(departmentId)

      // Broadcast to WebSocket clients
      WebSocketManager.broadcastDepartmentWorkloadUpdate({
        departmentId,
        vendorId,
        branchId,
        workloadData,
        timestamp: DateTime.now()
      })

      // Emit internal event
      Event.emit('department:workload:updated', {
        departmentId,
        vendorId,
        branchId,
        workloadData,
        metadata
      })

    } catch (error) {
      console.error('Error broadcasting department workload update:', error)
    }
  }

  /**
   * Broadcast overdue alert
   */
  public static async broadcastOverdueAlert(
    orderItem: OrderItem,
    context: BroadcastContext
  ): Promise<void> {
    try {
      const overdueMinutes = orderItem.preparationStartedAt ? 
        DateTime.now().diff(orderItem.preparationStartedAt, 'minutes').minutes - (orderItem.estimatedPreparationTime || 0) : 0

      let priorityLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'
      if (overdueMinutes > 30) priorityLevel = 'critical'
      else if (overdueMinutes > 15) priorityLevel = 'high'
      else if (overdueMinutes > 5) priorityLevel = 'medium'

      // Broadcast to WebSocket clients
      WebSocketManager.broadcastOverdueAlert({
        orderId: context.orderId,
        itemId: orderItem.id,
        vendorId: context.vendorId,
        branchId: context.branchId,
        departmentId: context.departmentId,
        assignedStaffId: context.assignedStaffId,
        overdueMinutes,
        priorityLevel,
        timestamp: DateTime.now()
      })

      // Emit internal event
      Event.emit('item:overdue', {
        orderItem,
        overdueMinutes,
        priorityLevel,
        context
      })

    } catch (error) {
      console.error('Error broadcasting overdue alert:', error)
    }
  }

  /**
   * Broadcast staff assignment notification
   */
  public static async broadcastStaffAssignment(
    orderItem: OrderItem,
    assignedStaffId: string,
    assignedBy: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await orderItem.load('order')

      // Broadcast to WebSocket clients
      WebSocketManager.notifyStaffAssignment({
        staffId: assignedStaffId,
        orderId: orderItem.orderId,
        itemId: orderItem.id,
        departmentId: orderItem.departmentId,
        assignedBy,
        estimatedTime: orderItem.estimatedPreparationTime,
        priority: orderItem.priorityLevel,
        timestamp: DateTime.now()
      })

      // Emit internal event
      Event.emit('staff:assigned', {
        orderItem,
        assignedStaffId,
        assignedBy,
        metadata
      })

    } catch (error) {
      console.error('Error broadcasting staff assignment:', error)
    }
  }

  /**
   * Calculate department workload data
   */
  private static async calculateDepartmentWorkload(departmentId: string): Promise<{
    pending_items: number
    preparing_items: number
    ready_items: number
    total_items: number
    staff_count: number
    capacity_utilization: number
  }> {
    const items = await OrderItem.query()
      .where('department_id', departmentId)
      .whereIn('status', ['pending', 'preparing', 'ready'])
      .exec()

    const pending_items = items.filter(item => item.status === 'pending').length
    const preparing_items = items.filter(item => item.status === 'preparing').length
    const ready_items = items.filter(item => item.status === 'ready').length
    const total_items = items.length

    // This would need to be calculated based on actual department staff
    const staff_count = 5 // Placeholder
    const capacity_utilization = total_items > 0 ? Math.round((preparing_items / (staff_count * 3)) * 100) : 0

    return {
      pending_items,
      preparing_items,
      ready_items,
      total_items,
      staff_count,
      capacity_utilization: Math.min(100, capacity_utilization)
    }
  }

  /**
   * Log status change for analytics
   */
  private static logStatusChange(
    type: 'order' | 'item' | 'modifier',
    entityId: string | number,
    status: string,
    previousStatus: string,
    updatedBy?: string,
    context?: BroadcastContext
  ): void {
    const logData = {
      type,
      entity_id: entityId,
      status,
      previous_status: previousStatus,
      updated_by: updatedBy,
      timestamp: DateTime.now().toISO(),
      context
    }

    // This would typically go to your analytics/logging service
    console.log(`Status change [${type}]:`, JSON.stringify(logData, null, 2))
    
    // Example: Send to analytics service
    // AnalyticsService.track('status_change', logData)
  }

  /**
   * Broadcast bulk status changes
   */
  public static async broadcastBulkStatusChanges(
    changes: Array<{
      type: 'order' | 'item' | 'modifier'
      entity: Order | OrderItem | OrderItemModifier
      previousStatus: string
      updatedBy?: string
      metadata?: Record<string, any>
    }>
  ): Promise<void> {
    for (const change of changes) {
      try {
        switch (change.type) {
          case 'order':
            await this.broadcastOrderStatusChange(
              change.entity as Order,
              change.previousStatus,
              change.updatedBy,
              change.metadata
            )
            break
          case 'item':
            await this.broadcastItemStatusChange(
              change.entity as OrderItem,
              change.previousStatus,
              change.updatedBy,
              change.metadata
            )
            break
          case 'modifier':
            await this.broadcastModifierStatusChange(
              change.entity as OrderItemModifier,
              change.previousStatus,
              change.updatedBy,
              change.metadata
            )
            break
        }
      } catch (error) {
        console.error(`Error broadcasting bulk status change for ${change.type} ${change.entity.id}:`, error)
      }
    }
  }
}
