import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import VendorType from '../VendorType'

export default class VendorTypeFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof VendorType, VendorType>

  public s(name: string) {
    this.$query.where((builder) => {
      builder.whereILike('name', `%${name}%`)
    })
  }

  public type(type: string) {
    this.$query.where((builder) => {
      builder.whereILike('service_id', `%${type}%`)
    })
  }

  public with(relations: string) {
    relations.split(',').map((relation: 'vendorCategories') => {
      this.$query.preload(relation)
    })
  }

  public withTrashed() {
    this.$query.withTrashed()
  }

  public onlyTrashed() {
    this.$query.onlyTrashed()
  }
}
