import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import CustomerSubscription from './CustomerSubscription'

export default class CustomerUsage extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public customerId: string

  @column()
  public subscriptionId: number | null

  @column()
  public type: 'order' | 'feature' | 'notification'

  @column()
  public featureCode: string | null

  @column()
  public quantity: number

  @column()
  public amount: number

  @column()
  public currency: string

  @column()
  public billingPeriod: string

  @column()
  public status: 'pending' | 'billed'

  @column.dateTime()
  public billedAt: DateTime | null

  @column()
  public referenceId: string | null

  @column()
  public referenceType: string | null

  @column()
  public meta: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User)
  public customer: BelongsTo<typeof User>

  @belongsTo(() => CustomerSubscription)
  public subscription: BelongsTo<typeof CustomerSubscription>

  /**
   * Check if usage is billable
   */
  public get isBillable(): boolean {
    return this.status === 'pending' && this.amount > 0
  }

  /**
   * Mark usage as billed
   */
  public async markAsBilled(): Promise<void> {
    this.status = 'billed'
    this.billedAt = DateTime.now()
    await this.save()
  }
}