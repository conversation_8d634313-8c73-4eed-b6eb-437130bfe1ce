import { BaseCommand, args, flags } from '@adonisjs/core/build/standalone'
import OrderCompletionDetector from '../Services/OrderCompletionDetector'
import { DateTime } from 'luxon'

export default class CheckOrderCompletion extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'check:order:completion'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Check and update order completion status for pending orders'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest` 
     * afterwards.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call 
     * `node ace generate:manifest` afterwards.
     */
    stayAlive: false,
  }

  @args.string({ description: 'Specific order ID to check (optional)' })
  public orderId: string

  @flags.boolean({ 
    alias: 's', 
    description: 'Run scheduled completion check for all pending orders' 
  })
  public scheduled: boolean

  @flags.string({ 
    alias: 'v', 
    description: 'Filter by vendor ID' 
  })
  public vendorId: string

  @flags.number({ 
    alias: 'l', 
    description: 'Limit number of orders to check' 
  })
  public limit: number

  @flags.boolean({ 
    alias: 'stats', 
    description: 'Show completion statistics' 
  })
  public showStats: boolean

  public async run() {
    this.logger.info('Starting order completion check...')

    try {
      if (this.showStats) {
        await this.showCompletionStatistics()
        return
      }

      if (this.orderId) {
        await this.checkSpecificOrder(this.orderId)
      } else if (this.scheduled) {
        await this.runScheduledCheck()
      } else {
        await this.checkPendingOrders()
      }

    } catch (error) {
      this.logger.error('Error during order completion check:', error.message)
      this.exitCode = 1
    }
  }

  /**
   * Check a specific order
   */
  private async checkSpecificOrder(orderId: string): Promise<void> {
    this.logger.info(`Checking completion for order: ${orderId}`)

    try {
      const result = await OrderCompletionDetector.checkOrderCompletion(orderId)
      
      if (result.statusChanged) {
        this.logger.success(`✅ Order ${orderId} status updated: ${result.previousStatus} → ${result.newStatus}`)
        this.logger.info(`   Completion time: ${result.completionTime?.toISO()}`)
        this.logger.info(`   Items: ${result.readyItems}/${result.totalItems} ready`)
      } else {
        this.logger.info(`ℹ️  Order ${orderId} checked - no status change needed`)
        this.logger.info(`   Current status: ${result.newStatus}`)
        this.logger.info(`   Items: ${result.readyItems}/${result.totalItems} ready`)
      }

      // Show department breakdown
      if (Object.keys(result.departmentBreakdown).length > 0) {
        this.logger.info('   Department breakdown:')
        Object.entries(result.departmentBreakdown).forEach(([dept, stats]: [string, any]) => {
          this.logger.info(`     ${dept}: ${stats.ready}/${stats.total} ready`)
        })
      }

    } catch (error) {
      this.logger.error(`❌ Failed to check order ${orderId}: ${error.message}`)
    }
  }

  /**
   * Run scheduled completion check for all pending orders
   */
  private async runScheduledCheck(): Promise<void> {
    this.logger.info('Running scheduled completion check for all pending orders...')

    try {
      const result = await OrderCompletionDetector.scheduledCompletionCheck()
      
      this.logger.success(`✅ Scheduled check completed:`)
      this.logger.info(`   Orders checked: ${result.checked}`)
      this.logger.info(`   Orders completed: ${result.completed}`)
      
      if (result.errors > 0) {
        this.logger.warning(`   Errors: ${result.errors}`)
      }

      if (result.completed > 0) {
        this.logger.success(`🎉 ${result.completed} orders were marked as ready!`)
      }

    } catch (error) {
      this.logger.error(`❌ Scheduled check failed: ${error.message}`)
    }
  }

  /**
   * Check pending orders with optional filtering
   */
  private async checkPendingOrders(): Promise<void> {
    this.logger.info('Checking pending orders for completion...')

    try {
      const orders = await OrderCompletionDetector.getOrdersReadyForCompletion()
      
      let filteredOrders = orders
      
      if (this.vendorId) {
        filteredOrders = orders.filter(order => order.vendorId === this.vendorId)
        this.logger.info(`Filtered to vendor ${this.vendorId}: ${filteredOrders.length} orders`)
      }

      if (this.limit) {
        filteredOrders = filteredOrders.slice(0, this.limit)
        this.logger.info(`Limited to ${this.limit} orders`)
      }

      this.logger.info(`Found ${filteredOrders.length} orders ready for completion check`)

      let completed = 0
      let errors = 0

      for (const order of filteredOrders) {
        try {
          const result = await OrderCompletionDetector.checkOrderCompletion(order.id)
          
          if (result.statusChanged) {
            completed++
            this.logger.success(`✅ Order ${order.id}: ${result.previousStatus} → ${result.newStatus}`)
          } else {
            this.logger.info(`ℹ️  Order ${order.id}: No change needed`)
          }

        } catch (error) {
          errors++
          this.logger.error(`❌ Order ${order.id}: ${error.message}`)
        }
      }

      this.logger.success(`\n📊 Summary:`)
      this.logger.info(`   Orders checked: ${filteredOrders.length}`)
      this.logger.info(`   Orders completed: ${completed}`)
      
      if (errors > 0) {
        this.logger.warning(`   Errors: ${errors}`)
      }

    } catch (error) {
      this.logger.error(`❌ Failed to check pending orders: ${error.message}`)
    }
  }

  /**
   * Show completion statistics
   */
  private async showCompletionStatistics(): Promise<void> {
    this.logger.info('Generating completion statistics...')

    try {
      const endDate = DateTime.now()
      const startDate = endDate.minus({ days: 7 }) // Last 7 days

      const stats = await OrderCompletionDetector.getCompletionStatistics(
        this.vendorId, 
        startDate, 
        endDate
      )

      this.logger.success(`📊 Completion Statistics (Last 7 days):`)
      this.logger.info(`   Total orders: ${stats.totalOrders}`)
      this.logger.info(`   Completed orders: ${stats.completedOrders}`)
      
      const completionRate = stats.totalOrders > 0 ? 
        Math.round((stats.completedOrders / stats.totalOrders) * 100) : 0
      this.logger.info(`   Completion rate: ${completionRate}%`)
      this.logger.info(`   Average completion time: ${stats.averageCompletionTime} minutes`)

      if (Object.keys(stats.departmentPerformance).length > 0) {
        this.logger.info('\n🏢 Department Performance:')
        Object.entries(stats.departmentPerformance).forEach(([dept, perf]: [string, any]) => {
          const deptRate = perf.totalItems > 0 ? 
            Math.round((perf.completedItems / perf.totalItems) * 100) : 0
          this.logger.info(`   ${dept}: ${perf.completedItems}/${perf.totalItems} (${deptRate}%)`)
        })
      }

    } catch (error) {
      this.logger.error(`❌ Failed to generate statistics: ${error.message}`)
    }
  }
}
