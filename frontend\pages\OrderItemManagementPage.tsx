import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Card, CardHeader, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Search, 
  Filter, 
  RefreshCw, 
  Grid, 
  List, 
  Clock,
  AlertTriangle,
  CheckCircle,
  Timer
} from 'lucide-react'
import OrderItemStatusManager, { OrderItemData } from '@/components/OrderItemStatusManager'
import OrderItemStatusCard from '@/components/OrderItemStatusCard'
import { useWebSocket } from '@/hooks/useWebSocket'
import { useAuth } from '@/hooks/useAuth'

interface OrderItemFilters {
  status?: string
  department?: string
  assigned_staff?: string
  priority?: string
  overdue_only?: boolean
  search?: string
}

const OrderItemManagementPage: React.FC = () => {
  const router = useRouter()
  const { orderId, itemId } = router.query
  const { user, isAuthenticated } = useAuth()
  
  const [orderItems, setOrderItems] = useState<OrderItemData[]>([])
  const [filteredItems, setFilteredItems] = useState<OrderItemData[]>([])
  const [selectedItem, setSelectedItem] = useState<OrderItemData | null>(null)
  const [filters, setFilters] = useState<OrderItemFilters>({})
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'detail'>('grid')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // WebSocket connection
  const { connected, connectionStatus } = useWebSocket({
    autoConnect: true,
    reconnection: true
  })

  // API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333/api/v1'

  // Get auth token
  const getAuthToken = () => {
    return localStorage.getItem('auth_token') || ''
  }

  // Fetch order items
  const fetchOrderItems = async () => {
    try {
      setLoading(true)
      setError(null)

      const token = getAuthToken()
      let endpoint = '/order-items'
      
      // If orderId is provided, fetch items for specific order
      if (orderId) {
        endpoint = `/orders/${orderId}/items`
      }

      const response = await fetch(`${API_BASE_URL}${endpoint}?include=product,department,assignedStaff,modifiers,statusHistory`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch order items')
      }

      const data = await response.json()
      setOrderItems(data.data || [])

      // If itemId is provided, select that specific item
      if (itemId && data.data) {
        const item = data.data.find((item: OrderItemData) => item.id.toString() === itemId)
        if (item) {
          setSelectedItem(item)
          setViewMode('detail')
        }
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch order items')
    } finally {
      setLoading(false)
    }
  }

  // Apply filters
  useEffect(() => {
    let filtered = [...orderItems]

    if (filters.status) {
      filtered = filtered.filter(item => item.status === filters.status)
    }

    if (filters.department) {
      filtered = filtered.filter(item => item.department_id === filters.department)
    }

    if (filters.assigned_staff) {
      filtered = filtered.filter(item => item.assigned_staff_id === filters.assigned_staff)
    }

    if (filters.priority) {
      filtered = filtered.filter(item => item.priority_level.toString() === filters.priority)
    }

    if (filters.overdue_only) {
      filtered = filtered.filter(item => item.is_overdue)
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      filtered = filtered.filter(item => 
        item.product_name.toLowerCase().includes(searchLower) ||
        item.order_id.toLowerCase().includes(searchLower) ||
        item.special_instructions?.toLowerCase().includes(searchLower)
      )
    }

    setFilteredItems(filtered)
  }, [orderItems, filters])

  // Handle status change
  const handleStatusChange = (itemId: number, newStatus: string, metadata?: Record<string, any>) => {
    setOrderItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, status: newStatus as any }
        : item
    ))

    if (selectedItem?.id === itemId) {
      setSelectedItem(prev => prev ? { ...prev, status: newStatus as any } : null)
    }
  }

  // Handle notes update
  const handleNotesUpdate = (itemId: number, notes: string) => {
    setOrderItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, preparation_notes: notes }
        : item
    ))

    if (selectedItem?.id === itemId) {
      setSelectedItem(prev => prev ? { ...prev, preparation_notes: notes } : null)
    }
  }

  // Get status counts
  const getStatusCounts = () => {
    return {
      pending: orderItems.filter(item => item.status === 'pending').length,
      preparing: orderItems.filter(item => item.status === 'preparing').length,
      ready: orderItems.filter(item => item.status === 'ready').length,
      served: orderItems.filter(item => item.status === 'served').length,
      overdue: orderItems.filter(item => item.is_overdue).length
    }
  }

  // Initialize data
  useEffect(() => {
    if (isAuthenticated) {
      fetchOrderItems()
    }
  }, [isAuthenticated, orderId])

  // Handle real-time updates
  useEffect(() => {
    const handleItemUpdate = (event: CustomEvent) => {
      const data = event.detail
      if (data.type === 'item_status_update') {
        handleStatusChange(data.item_id, data.status, data.metadata)
      }
    }

    window.addEventListener('websocket-item-status', handleItemUpdate as EventListener)
    
    return () => {
      window.removeEventListener('websocket-item-status', handleItemUpdate as EventListener)
    }
  }, [])

  if (!isAuthenticated) {
    router.push('/login')
    return null
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  const statusCounts = getStatusCounts()

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Order Item Management</h1>
          <p className="text-muted-foreground">
            {orderId ? `Managing items for Order #${orderId}` : 'Manage all order items'}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant={connected ? 'success' : 'destructive'}>
            {connectionStatus}
          </Badge>
          <Button onClick={fetchOrderItems} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-gray-500" />
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">{statusCounts.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Timer className="w-4 h-4 text-yellow-500" />
              <div>
                <p className="text-sm text-muted-foreground">Preparing</p>
                <p className="text-2xl font-bold">{statusCounts.preparing}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Ready</p>
                <p className="text-2xl font-bold">{statusCounts.ready}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Served</p>
                <p className="text-2xl font-bold">{statusCounts.served}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-red-500" />
              <div>
                <p className="text-sm text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-500">{statusCounts.overdue}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-medium">Filters</h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="md:col-span-2">
              <Input
                placeholder="Search items..."
                value={filters.search || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full"
              />
            </div>

            <Select
              value={filters.status || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, status: value || undefined }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="preparing">Preparing</SelectItem>
                <SelectItem value="ready">Ready</SelectItem>
                <SelectItem value="served">Served</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.priority || ''}
              onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value || undefined }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="All priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All priorities</SelectItem>
                <SelectItem value="1">High (1)</SelectItem>
                <SelectItem value="2">Medium (2)</SelectItem>
                <SelectItem value="3">Low (3)</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant={filters.overdue_only ? 'default' : 'outline'}
              onClick={() => setFilters(prev => ({ ...prev, overdue_only: !prev.overdue_only }))}
            >
              <AlertTriangle className="w-4 h-4 mr-2" />
              Overdue Only
            </Button>

            <div className="flex gap-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      {viewMode === 'detail' && selectedItem ? (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setViewMode('grid')}
            >
              ← Back to List
            </Button>
            <h2 className="text-xl font-semibold">Item Details</h2>
          </div>
          <OrderItemStatusManager
            orderItemId={selectedItem.id}
            orderId={selectedItem.order_id}
            initialData={selectedItem}
            onStatusChange={handleStatusChange}
            onNotesUpdate={handleNotesUpdate}
            showModifiers={true}
          />
        </div>
      ) : (
        <div className="space-y-4">
          {filteredItems.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <div className="space-y-4">
                  <Clock className="w-16 h-16 mx-auto text-muted-foreground" />
                  <h3 className="text-xl font-medium">No Items Found</h3>
                  <p className="text-muted-foreground">
                    {orderItems.length === 0 
                      ? 'No order items available'
                      : 'No items match your current filters'
                    }
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                : 'space-y-4'
            }>
              {filteredItems.map(item => (
                <OrderItemStatusCard
                  key={item.id}
                  orderItem={item}
                  onStatusChange={handleStatusChange}
                  onNotesUpdate={handleNotesUpdate}
                  showQuickActions={true}
                  showModifiers={true}
                  className={viewMode === 'list' ? 'w-full' : ''}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default OrderItemManagementPage
