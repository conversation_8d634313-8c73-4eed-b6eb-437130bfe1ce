import axios from 'axios'

export default class Pesaflow {
  constructor(public id = 'pesaflow') {}

  public process = <T>(config: T) => {
    return config
  }

  public balance = <T>(config: T) => {
    return config
  }

  public account = <T>(config: T) => {
    return config
  }

  public flights = async () => {
    const { data } = await axios.get('https://pesaflow.go.ke', {
      headers: {
        Authorization: 'Bearer {token}',
      },
    })

    console.log(data)
  }
}
