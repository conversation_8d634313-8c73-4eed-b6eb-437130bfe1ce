import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import Booking from 'App/Models/Booking'
import User from 'App/Models/User'
import { DateTime } from 'luxon'

export default class BookingReminderNotification implements NotificationContract {
  constructor(
    private booking: Booking,
    private reminderType: '24_hour' | '2_hour' | '30_minute' = '24_hour'
  ) {}

  public via(_notifiable: User) {
    // For urgent reminders (2 hour, 30 minute), include SMS
    const channels = ['database' as const, 'fcm' as const]
    
    if (this.reminderType === '2_hour' || this.reminderType === '30_minute') {
      channels.push('sms' as const)
    } else {
      channels.push('mail' as const)
    }
    
    return channels
  }

  public toDatabase(notifiable: User) {
    const timeUntil = this.getTimeUntilAppointment()
    const title = `Appointment Reminder - ${timeUntil}`
    const message = `Don't forget! You have an appointment for ${this.booking.product?.name} ${timeUntil} at ${this.booking.appointmentStart.toFormat('h:mm a')}.`

    return NotificationHelper.createDatabaseNotification({
      category: NotificationCategory.BOOKING,
      type: NotificationType.BOOKING_REMINDER,
      priority: this.getReminderPriority(),
      title,
      message,
      actionType: NotificationActionType.VIEW_BOOKING,
      actionUrl: `/bookings/${this.booking.id}`,
      actionLabel: 'View Booking',
      data: {
        booking_id: this.booking.id,
        reminder_type: this.reminderType,
        confirmation_code: this.booking.confirmationCode,
        appointment_date: this.booking.appointmentStart.toFormat('yyyy-MM-dd'),
        appointment_time: this.booking.appointmentStart.toFormat('HH:mm'),
        service_name: this.booking.product?.name,
        vendor_name: this.booking.vendor?.name,
        branch_name: this.booking.branch?.name,
        time_until: timeUntil
      }
    })
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const timeUntil = this.getTimeUntilAppointment()
    const title = `Appointment Reminder`
    const body = `Don't forget! You have an appointment for ${this.booking.product?.name} ${timeUntil} at ${this.booking.appointmentStart.toFormat('h:mm a')}.`

    return {
      title,
      body: body.replace('{firstName}', notifiable.firstName || notifiable.name),
      url: `aiauser://bookings/${this.booking.id}`,
      icon: 'https://cdn.verful.com/icons/verful-512x512.png',
      data: {
        booking_id: this.booking.id,
        reminder_type: this.reminderType,
        type: 'booking_reminder'
      }
    }
  }

  public toMail(notifiable: User) {
    const timeUntil = this.getTimeUntilAppointment()
    const subject = `Appointment Reminder - ${timeUntil}`
    const appointmentDate = this.booking.appointmentStart.toFormat('EEEE, MMMM dd, yyyy')
    const appointmentTime = this.booking.appointmentStart.toFormat('h:mm a')

    const emailData = {
      greeting: `Hello ${notifiable.firstName || notifiable.name}!`,
      subject,
      intro: `This is a friendly reminder about your upcoming appointment.`,
      reminder_details: {
        time_until: timeUntil,
        service_name: this.booking.product?.name,
        appointment_date: appointmentDate,
        appointment_time: appointmentTime,
        duration: `${this.booking.durationMinutes} minutes`,
        confirmation_code: this.booking.confirmationCode,
        vendor_name: this.booking.vendor?.name,
        branch_name: this.booking.branch?.name,
        branch_location: this.booking.branch?.location?.name || 'Please contact us for location details'
      },
      selected_options: this.booking.selectedServiceOptions || [],
      staff_assignments: this.booking.staffAssignments || [],
      preparation_notes: this.getPreparationNotes(),
      action_url: `/bookings/${this.booking.id}`,
      action_label: 'View Booking Details',
      footer_text: 'If you need to reschedule or cancel, please contact us as soon as possible. We look forward to seeing you!'
    }

    return new CustomerMailer(notifiable, 'mails/booking/reminder', emailData)
  }

  public toSms(notifiable: User) {
    if (!notifiable.phone) {
      throw new Error('Cannot send SMS to user without phone number')
    }

    const timeUntil = this.getTimeUntilAppointment()
    const serviceName = this.booking.product?.name || 'service'
    const time = this.booking.appointmentStart.toFormat('h:mm a')
    const confirmationCode = this.booking.confirmationCode

    let text: string
    if (this.reminderType === '30_minute') {
      text = `URGENT: Hi ${notifiable.firstName || notifiable.name}, your appointment for ${serviceName} is in 30 minutes at ${time}. Confirmation: ${confirmationCode}`
    } else if (this.reminderType === '2_hour') {
      text = `Reminder: Hi ${notifiable.firstName || notifiable.name}, your appointment for ${serviceName} is in 2 hours at ${time}. Confirmation: ${confirmationCode}`
    } else {
      text = `Reminder: Hi ${notifiable.firstName || notifiable.name}, you have an appointment for ${serviceName} tomorrow at ${time}. Confirmation: ${confirmationCode}`
    }

    return {
      text,
      phone: notifiable.phone.includes('+')
        ? notifiable.phone.replace(/^\+/, '')
        : notifiable.phone,
    }
  }

  private getTimeUntilAppointment(): string {
    switch (this.reminderType) {
      case '24_hour':
        return 'Tomorrow'
      case '2_hour':
        return 'in 2 hours'
      case '30_minute':
        return 'in 30 minutes'
      default:
        return 'soon'
    }
  }

  private getReminderPriority(): NotificationPriority {
    switch (this.reminderType) {
      case '30_minute':
        return NotificationPriority.HIGH
      case '2_hour':
        return NotificationPriority.MEDIUM
      case '24_hour':
        return NotificationPriority.LOW
      default:
        return NotificationPriority.MEDIUM
    }
  }

  private getPreparationNotes(): string[] {
    const notes: string[] = []
    
    // Add general preparation notes
    notes.push('Please arrive 10 minutes early for check-in')
    
    // Add service-specific notes based on selected options
    const serviceOptions = this.booking.selectedServiceOptions || []
    
    serviceOptions.forEach(option => {
      if (option.type === 'duration' && option.name.includes('90') || option.name.includes('120')) {
        notes.push('This is an extended session - please plan accordingly')
      }
      if (option.type === 'equipment' && option.name.toLowerCase().includes('massage')) {
        notes.push('Please wear comfortable, loose-fitting clothing')
      }
    })

    // Add reminder-specific notes
    if (this.reminderType === '24_hour') {
      notes.push('Please confirm your attendance by replying to this email')
    } else if (this.reminderType === '2_hour') {
      notes.push('Please ensure you have the confirmation code ready')
    }

    return notes
  }
}
