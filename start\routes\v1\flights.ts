import Route from '@ioc:Adonis/Core/Route'

Route.group(() => {
  Route.group(() => {
    Route.shallowResource('arrivals', 'Flights/KiaArrivalFlightsController')
      .apiOnly()
      .except(['store', 'update', 'destroy'])
      .as('arrivals')
    Route.shallowResource('departures', 'Flights/KiaDepartureFlightsController')
      .apiOnly()
      .except(['store', 'update', 'destroy'])
      .as('departures')
  })
    .as('kia')
    .prefix('kia')

  Route.group(() => {
    Route.shallowResource('arrivals', 'Flights/MiaArrivalFlightsController')
      .apiOnly()
      .except(['store', 'update', 'destroy'])
      .as('arrivals')
    Route.shallowResource('departures', 'Flights/MiaDepartureFlightsController')
      .apiOnly()
      .except(['store', 'update', 'destroy'])
      .as('departures')
  })
    .as('mia')
    .prefix('mia')

  Route.group(() => {
    Route.shallowResource('arrivals', 'Flights/WapArrivalFlightsController')
      .apiOnly()
      .except(['store', 'update', 'destroy'])
      .as('arrivals')
    Route.shallowResource('departures', 'Flights/WapDepartureFlightsController')
      .apiOnly()
      .except(['store', 'update', 'destroy'])
      .as('departures')
  })
    .as('wap')
    .prefix('wap')
})
  .prefix('v1/flights')
  .middleware('auth')
