import { schema, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class CreateMessageActionValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    type_id: schema.number([
      rules.exists({ table: 'action_types', column: 'id' })
    ]),
    config: schema.object().members({}),
    status: schema.enum.optional(['pending', 'active', 'completed', 'cancelled'])
  })

  public messages = {
    'type_id.required': 'Action type is required',
    'type_id.exists': 'Selected action type does not exist',
    'config.required': 'Configuration is required',
    'status.enum': 'Status must be one of: pending, active, completed, cancelled'
  }
} 