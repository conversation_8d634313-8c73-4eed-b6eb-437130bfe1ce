import { NotificationContract } from '@ioc:Verful/Notification'
import User from 'App/Models/User'

export default class SendSmsOtp implements NotificationContract {
  public via(notifiable: User) {
    return notifiable.phone ? ['sms' as const] : []
  }

  //   public toDatabase(notifiable: User) {
  //     return {
  //       subject: 'Your OTP',
  //       message: `Hi ${notifiable.firstName}, use ${notifiable.otp} to verify your phone number`,
  //     }
  //   }

  public toSms(notifiable: User) {
    if (!notifiable.phone) {
      throw new Error('Cannot send SMS to user without phone number')
    }
    
    const text = `Hi ${notifiable.firstName}, use ${notifiable.otp} to verify your phone number`

    return {
      phone: notifiable.phone.includes('+') ? notifiable.phone.replace('+', '') : notifiable.phone,
      text,
    }
  }
}
