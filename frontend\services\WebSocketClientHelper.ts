/**
 * Client-side WebSocket subscription helper
 * This file provides TypeScript interfaces and helper functions for frontend integration
 */

export interface WebSocketSubscriptionRequest {
  type: 'order' | 'department' | 'vendor' | 'branch' | 'staff' | 'customer' | 'admin'
  target_id: string
  sub_type?: string
  filters?: Record<string, any>
  metadata?: Record<string, any>
}

export interface WebSocketSubscriptionResponse {
  success: boolean
  subscription_id?: string
  channel?: string
  error?: string
  validation_result?: {
    allowed: boolean
    reason?: string
    required_permissions?: string[]
    suggested_alternatives?: string[]
  }
}

export interface WebSocketSubscriptionInfo {
  id: string
  type: string
  target_id: string
  sub_type?: string
  subscribed_at: string
  last_activity: string
  filters?: Record<string, any>
  metadata?: Record<string, any>
}

export interface WebSocketEventData {
  type: string
  timestamp: string
  room?: string
  [key: string]: any
}

/**
 * WebSocket client helper class for managing subscriptions
 */
export class WebSocketSubscriptionClient {
  private socket: any // Socket.IO client instance
  private subscriptions: Map<string, WebSocketSubscriptionInfo> = new Map()
  private eventHandlers: Map<string, Function[]> = new Map()

  constructor(socket: any) {
    this.socket = socket
    this.setupEventListeners()
  }

  /**
   * Set up event listeners for subscription responses
   */
  private setupEventListeners(): void {
    this.socket.on('subscription_success', (data: any) => {
      console.log('Subscription successful:', data)
      this.handleSubscriptionSuccess(data)
    })

    this.socket.on('subscription_error', (data: any) => {
      console.error('Subscription error:', data)
      this.handleSubscriptionError(data)
    })

    this.socket.on('unsubscription_success', (data: any) => {
      console.log('Unsubscription successful:', data)
      this.handleUnsubscriptionSuccess(data)
    })

    this.socket.on('unsubscription_error', (data: any) => {
      console.error('Unsubscription error:', data)
      this.handleUnsubscriptionError(data)
    })

    this.socket.on('subscriptions_list', (data: any) => {
      console.log('Subscriptions list received:', data)
      this.handleSubscriptionsList(data)
    })

    // Set up event handlers for real-time updates
    this.setupRealTimeEventHandlers()
  }

  /**
   * Set up handlers for real-time events
   */
  private setupRealTimeEventHandlers(): void {
    // Order events
    this.socket.on('order_status_update', (data: WebSocketEventData) => {
      this.triggerEventHandlers('order_status_update', data)
    })

    this.socket.on('order_completed', (data: WebSocketEventData) => {
      this.triggerEventHandlers('order_completed', data)
    })

    // Item events
    this.socket.on('item_status_update', (data: WebSocketEventData) => {
      this.triggerEventHandlers('item_status_update', data)
    })

    // Modifier events
    this.socket.on('modifier_status_update', (data: WebSocketEventData) => {
      this.triggerEventHandlers('modifier_status_update', data)
    })

    // Department events
    this.socket.on('department_workload_update', (data: WebSocketEventData) => {
      this.triggerEventHandlers('department_workload_update', data)
    })

    // Alert events
    this.socket.on('overdue_alert', (data: WebSocketEventData) => {
      this.triggerEventHandlers('overdue_alert', data)
    })

    // Staff events
    this.socket.on('staff_assignment', (data: WebSocketEventData) => {
      this.triggerEventHandlers('staff_assignment', data)
    })
  }

  /**
   * Subscribe to a channel
   */
  public async subscribe(request: WebSocketSubscriptionRequest): Promise<WebSocketSubscriptionResponse> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve({
          success: false,
          error: 'Subscription timeout'
        })
      }, 10000) // 10 second timeout

      // Set up one-time listeners for this subscription
      const successHandler = (data: any) => {
        clearTimeout(timeout)
        this.socket.off('subscription_success', successHandler)
        this.socket.off('subscription_error', errorHandler)
        resolve({
          success: true,
          subscription_id: data.subscription_id,
          channel: data.channel
        })
      }

      const errorHandler = (data: any) => {
        clearTimeout(timeout)
        this.socket.off('subscription_success', successHandler)
        this.socket.off('subscription_error', errorHandler)
        resolve({
          success: false,
          error: data.error,
          validation_result: data.validation_result
        })
      }

      this.socket.once('subscription_success', successHandler)
      this.socket.once('subscription_error', errorHandler)

      // Send subscription request
      this.socket.emit('subscribe_advanced', request)
    })
  }

  /**
   * Unsubscribe from a channel
   */
  public async unsubscribe(subscriptionId: string): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve({
          success: false,
          error: 'Unsubscription timeout'
        })
      }, 5000) // 5 second timeout

      // Set up one-time listeners for this unsubscription
      const successHandler = (data: any) => {
        clearTimeout(timeout)
        this.socket.off('unsubscription_success', successHandler)
        this.socket.off('unsubscription_error', errorHandler)
        resolve({ success: true })
      }

      const errorHandler = (data: any) => {
        clearTimeout(timeout)
        this.socket.off('unsubscription_success', successHandler)
        this.socket.off('unsubscription_error', errorHandler)
        resolve({
          success: false,
          error: data.error
        })
      }

      this.socket.once('unsubscription_success', successHandler)
      this.socket.once('unsubscription_error', errorHandler)

      // Send unsubscription request
      this.socket.emit('unsubscribe_advanced', { subscription_id: subscriptionId })
    })
  }

  /**
   * Get current subscriptions
   */
  public getSubscriptions(): Promise<WebSocketSubscriptionInfo[]> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve([])
      }, 5000) // 5 second timeout

      const handler = (data: any) => {
        clearTimeout(timeout)
        this.socket.off('subscriptions_list', handler)
        resolve(data.subscriptions || [])
      }

      this.socket.once('subscriptions_list', handler)
      this.socket.emit('get_subscriptions')
    })
  }

  /**
   * Add event handler for specific event type
   */
  public on(eventType: string, handler: Function): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, [])
    }
    this.eventHandlers.get(eventType)!.push(handler)
  }

  /**
   * Remove event handler
   */
  public off(eventType: string, handler: Function): void {
    const handlers = this.eventHandlers.get(eventType)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * Trigger event handlers
   */
  private triggerEventHandlers(eventType: string, data: WebSocketEventData): void {
    const handlers = this.eventHandlers.get(eventType)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in event handler for ${eventType}:`, error)
        }
      })
    }
  }

  /**
   * Handle subscription success
   */
  private handleSubscriptionSuccess(data: any): void {
    // Store subscription info locally if needed
    console.log('Subscription successful:', data)
  }

  /**
   * Handle subscription error
   */
  private handleSubscriptionError(data: any): void {
    console.error('Subscription failed:', data)
  }

  /**
   * Handle unsubscription success
   */
  private handleUnsubscriptionSuccess(data: any): void {
    // Remove subscription from local storage if needed
    console.log('Unsubscription successful:', data)
  }

  /**
   * Handle unsubscription error
   */
  private handleUnsubscriptionError(data: any): void {
    console.error('Unsubscription failed:', data)
  }

  /**
   * Handle subscriptions list
   */
  private handleSubscriptionsList(data: any): void {
    // Update local subscriptions cache
    this.subscriptions.clear()
    if (data.subscriptions) {
      data.subscriptions.forEach((sub: WebSocketSubscriptionInfo) => {
        this.subscriptions.set(sub.id, sub)
      })
    }
  }

  /**
   * Get local subscriptions cache
   */
  public getLocalSubscriptions(): WebSocketSubscriptionInfo[] {
    return Array.from(this.subscriptions.values())
  }

  /**
   * Check if subscribed to a specific channel
   */
  public isSubscribedTo(type: string, targetId: string, subType?: string): boolean {
    return Array.from(this.subscriptions.values()).some(sub => 
      sub.type === type && 
      sub.target_id === targetId && 
      sub.sub_type === subType
    )
  }
}

/**
 * Helper functions for common subscription patterns
 */
export class WebSocketSubscriptionHelpers {
  
  /**
   * Subscribe to order updates
   */
  static subscribeToOrder(client: WebSocketSubscriptionClient, orderId: string): Promise<WebSocketSubscriptionResponse> {
    return client.subscribe({
      type: 'order',
      target_id: orderId
    })
  }

  /**
   * Subscribe to department updates
   */
  static subscribeToDepartment(client: WebSocketSubscriptionClient, departmentId: string): Promise<WebSocketSubscriptionResponse> {
    return client.subscribe({
      type: 'department',
      target_id: departmentId
    })
  }

  /**
   * Subscribe to vendor updates
   */
  static subscribeToVendor(client: WebSocketSubscriptionClient, vendorId: string, subType?: string): Promise<WebSocketSubscriptionResponse> {
    return client.subscribe({
      type: 'vendor',
      target_id: vendorId,
      sub_type: subType
    })
  }

  /**
   * Subscribe to staff updates
   */
  static subscribeToStaff(client: WebSocketSubscriptionClient, staffId: string): Promise<WebSocketSubscriptionResponse> {
    return client.subscribe({
      type: 'staff',
      target_id: staffId
    })
  }

  /**
   * Subscribe to customer order updates
   */
  static subscribeToCustomerOrders(client: WebSocketSubscriptionClient, customerId: string): Promise<WebSocketSubscriptionResponse> {
    return client.subscribe({
      type: 'customer',
      target_id: customerId,
      sub_type: 'orders'
    })
  }
}
