import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import FeatureFlagService from 'App/Services/FeatureFlagService'
import FeatureFlag from 'App/Models/FeatureFlag'

export default class FeatureFlagsController {
  /**
   * List all feature flags
   */
  public async index({ response }: HttpContextContract) {
    const flags = await FeatureFlag.query().orderBy('created_at', 'desc')
    return response.ok(flags)
  }

  /**
   * Get a specific feature flag
   */
  public async show({ params, response }: HttpContextContract) {
    const flag = await FeatureFlag.findOrFail(params.id)
    return response.ok(flag)
  }

  /**
   * Create a new feature flag
   */
  public async store({ request, response }: HttpContextContract) {
    const data = request.only([
      'name',
      'code',
      'description',
      'isEnabled',
      'conditions',
      'scope',
      'scopeId',
      'meta',
    ])

    const flag = await FeatureFlag.create(data)
    return response.created(flag)
  }

  /**
   * Update a feature flag
   */
  public async update({ params, request, response }: HttpContextContract) {
    const flag = await FeatureFlag.findOrFail(params.id)
    const data = request.only([
      'name',
      'code',
      'description',
      'isEnabled',
      'conditions',
      'scope',
      'scopeId',
      'meta',
    ])

    flag.merge(data)
    await flag.save()

    // Update cache
    FeatureFlagService.clearCache()

    return response.ok(flag)
  }

  /**
   * Enable a feature flag
   */
  public async enable({ params, request, response }: HttpContextContract) {
    const { scope, scopeId, conditions } = request.only(['scope', 'scopeId', 'conditions'])
    const flag = await FeatureFlagService.enableFlag(params.code, { scope, scopeId, conditions })
    return response.ok(flag)
  }

  /**
   * Disable a feature flag
   */
  public async disable({ params, response }: HttpContextContract) {
    const flag = await FeatureFlagService.disableFlag(params.code)
    return response.ok(flag)
  }

  /**
   * Delete a feature flag
   */
  public async destroy({ params, response }: HttpContextContract) {
    const flag = await FeatureFlag.findOrFail(params.id)
    await flag.delete()

    // Update cache
    FeatureFlagService.clearCache()

    return response.noContent()
  }
}