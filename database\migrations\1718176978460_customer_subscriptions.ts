import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'customer_subscriptions'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      
      // Relationships
      table.string('customer_id').references('id').inTable('users').onDelete('CASCADE').notNullable()
      table.integer('plan_id').references('id').inTable('subscription_plans').onDelete('CASCADE').notNullable()
      
      // Subscription details
      table.enum('status', ['active', 'cancelled', 'expired']).notNullable().defaultTo('active')
      table.timestamp('start_date', { useTz: true }).notNullable()
      table.timestamp('end_date', { useTz: true }).notNullable()
      table.enum('billing_cycle', ['monthly', 'yearly']).notNullable()
      table.decimal('amount', 10, 2).notNullable()
      table.string('currency').notNullable().defaultTo('KES')
      table.boolean('auto_renew').notNullable().defaultTo(true)
      
      // Billing info
      table.timestamp('last_billed_at', { useTz: true }).nullable()
      table.timestamp('next_billing_at', { useTz: true }).nullable()
      table.string('payment_method_id').nullable()
      
      // Metadata
      table.jsonb('meta').nullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Indexes
      table.index(['customer_id'])
      table.index(['plan_id'])
      table.index(['status'])
      table.index(['end_date'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 