import { DateTime } from 'luxon'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import {
  column,
  BaseModel,
  belongsTo,
  BelongsTo,
  beforeCreate,
  hasMany,
  HasMany,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import Branch from './Branch'
import SectionFilter from './Filters/SectionFilter'
import { ulid } from 'ulidx'
import Lot from './Lot'

export default class Section extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => SectionFilter
  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public branchId: string

  @column()
  public details: string

  @column()
  public active: boolean

  @attachment({ folder: 'sections', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @beforeCreate()
  public static async generateId(section: Section) {
    section.id = ulid().toLowerCase()
  }

  @hasMany(() => Lot)
  public lots: HasMany<typeof Lot>
}
