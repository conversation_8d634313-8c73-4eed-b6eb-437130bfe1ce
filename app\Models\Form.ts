import { compose } from '@ioc:<PERSON>onis/Core/Helpers'
import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import FormFilter from './Filters/FormFilter'
import { ulid } from 'ulidx'
import { FormSection } from 'App/Interfaces/Forms'

export default class Form extends compose(BaseModel, Filterable) {
  public static $filter = () => FormFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public sections: {
    [sectionId: string]: FormSection
  }

  @column()
  public action: 'Save' | 'Send' | 'Save and Send'

  @column()
  public link: string

  @attachment()
  public image: AttachmentContract

  @column()
  public productId: string

  @column()
  public auth: Record<string, unknown>

  @column()
  public headers: Record<string, unknown>

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(form: Form) {
    form.id = ulid().toLowerCase()
  }
}
