import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import SubscriptionPlan from './SubscriptionPlan'
import CustomerUsage from './CustomerUsage'

export default class CustomerSubscription extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public customerId: string

  @column()
  public planId: number

  @column()
  public status: 'active' | 'cancelled' | 'expired'

  @column.dateTime()
  public startDate: DateTime

  @column.dateTime()
  public endDate: DateTime

  @column()
  public billingCycle: 'monthly' | 'yearly'

  @column()
  public amount: number

  @column()
  public currency: string

  @column()
  public autoRenew: boolean

  @column.dateTime()
  public lastBilledAt: DateTime | null

  @column.dateTime()
  public nextBillingAt: DateTime | null

  @column()
  public paymentMethodId: string | null

  @column()
  public meta: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User)
  public customer: BelongsTo<typeof User>

  @belongsTo(() => SubscriptionPlan)
  public plan: BelongsTo<typeof SubscriptionPlan>

  @hasMany(() => CustomerUsage)
  public usages: HasMany<typeof CustomerUsage>

  /**
   * Check if subscription is active
   */
  public get isActive(): boolean {
    return this.status === 'active' && this.endDate > DateTime.now()
  }

  /**
   * Check if subscription needs renewal
   */
  public get needsRenewal(): boolean {
    if (!this.autoRenew) return false
    return this.nextBillingAt ? this.nextBillingAt <= DateTime.now() : false
  }

  /**
   * Calculate next billing date
   */
  public calculateNextBillingDate(): DateTime {
    const now = DateTime.now()
    if (this.billingCycle === 'monthly') {
      return now.plus({ months: 1 })
    }
    return now.plus({ years: 1 })
  }
}