import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class PackagingOptionValidator {
  constructor(protected ctx: HttpContextContract) {}

  /*
   * Define schema to validate the "shape", "type", "formatting" and "integrity" of data.
   *
   * For example:
   * 1. The username must be of data type string. But then also, it should
   *    not contain special characters or numbers.
   *    ```
   *     schema.string({}, [ rules.alpha() ])
   *    ```
   *
   * 2. The email must be of data type string, formatted as a valid
   *    email. But also, not used by any other user.
   *    ```
   *     schema.string({}, [
   *       rules.email(),
   *       rules.unique({ table: 'users', column: 'email' }),
   *     ])
   *    ```
   */
  public schema = schema.create({
    name: schema.string({ trim: true }, [
      rules.required(),
      rules.minLength(2),
      rules.maxLength(100),
    ]),
    description: schema.string.optional({ trim: true }, [
      rules.maxLength(500),
    ]),
    price: schema.number([
      rules.required(),
      rules.range(0, 999999.99),
    ]),
    active: schema.boolean.optional(),
  })

  /**
   * Custom messages for validation failures. You can make use of dot notation `(.)`
   * for targeting nested fields and array expressions `(*)` for targeting all
   * children of an array. For example:
   *
   * {
   *   'profile.username.required': 'Username is required',
   *   'scores.*.number': 'Define scores as valid numbers'
   * }
   *
   */
  public messages: CustomMessages = {
    'name.required': 'Packaging option name is required',
    'name.minLength': 'Packaging option name must be at least 2 characters long',
    'name.maxLength': 'Packaging option name cannot exceed 100 characters',
    'description.maxLength': 'Description cannot exceed 500 characters',
    'price.required': 'Price is required',
    'price.range': 'Price must be between 0 and 999,999.99',
    'price.number': 'Price must be a valid number',
    'active.boolean': 'Active status must be true or false',
  }
}

export class PackagingOptionUpdateValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    name: schema.string.optional({ trim: true }, [
      rules.minLength(2),
      rules.maxLength(100),
    ]),
    description: schema.string.optional({ trim: true }, [
      rules.maxLength(500),
    ]),
    price: schema.number.optional([
      rules.range(0, 999999.99),
    ]),
    active: schema.boolean.optional(),
  })

  public messages: CustomMessages = {
    'name.minLength': 'Packaging option name must be at least 2 characters long',
    'name.maxLength': 'Packaging option name cannot exceed 100 characters',
    'description.maxLength': 'Description cannot exceed 500 characters',
    'price.range': 'Price must be between 0 and 999,999.99',
    'price.number': 'Price must be a valid number',
    'active.boolean': 'Active status must be true or false',
  }
}
