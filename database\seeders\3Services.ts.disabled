import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Task from 'App/Models/Task'

export default class extends BaseSeeder {
  public async run() {
    // DISABLED: Services are now created by 9ProductHierarchySeeder.ts
    // const buy = await Task.findByOrFail('slug', 'buy-order')
    // await buy.related('services').createMany([
    //   {
    //     name: 'Food & Drinks',
    //   },
    //   {
    //     name: 'Clothing & Apparel',
    //   },
    //   {
    //     name: 'Entertainment',
    //   },
    //   {
    //     name: 'Toys & Games',
    //   },
    //   {
    //     name: 'Electronics',
    //   },
    //   {
    //     name: 'Home Goods',
    //   },
    //   {
    //     name: 'Beauty & Personal Care',
    //   },
    //   {
    //     name: 'Automotive',
    //   },
    //   {
    //     name: 'Sports & Fitness',
    //   },
    //   {
    //     name: 'Health & Wellness',
    //   },
    //   {
    //     name: 'Books & Magazines',
    //   },
    //   {
    //     name: 'Office Supplies',
    //   },
    //   {
    //     name: 'Pets & Animals',
    //   },
    //   {
    //     name: 'Travel & Leisure',
    //   },
    //   {
    //     name: 'Gifts & Flowers',
    //   },
    //   {
    //     name: 'Groceries',
    //   },
    // ])

    // const register = await Task.findByOrFail('slug', 'apply-register')

    // await register.related('services').createMany([
    //   {
    //     name: 'Scholarships',
    //   },
    //   {
    //     name: 'Loans',
    //   },
    //   {
    //     name: 'Jobs',
    //   },
    //   {
    //     name: 'Warantees',
    //   },
    //   {
    //     name: 'Licenses & Permits',
    //   },
    //   {
    //     name: 'Visa & Work Permits',
    //   },
    //   {
    //     name: 'Events',
    //   },
    //   {
    //     name: 'Courses & Programs',
    //   },
    //   {
    //     name: 'School Admissions',
    //   },
    // ])
  }
}
