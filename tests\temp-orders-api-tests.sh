#!/bin/bash

# Temp Orders API Testing Script
# Tests the unified temp order system with comprehensive scenarios

# Configuration
BASE_URL="http://localhost:3080/v1"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="54722332233"
TEST_PHONE="254722332233"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "PASS")
            echo -e "${GREEN}✅ PASS${NC}: $message"
            ((PASSED_TESTS++))
            ;;
        "FAIL")
            echo -e "${RED}❌ FAIL${NC}: $message"
            ((FAILED_TESTS++))
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  INFO${NC}: $message"
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  WARN${NC}: $message"
            ;;
    esac
    ((TOTAL_TESTS++))
}

# Function to make API request and check response
api_test() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local test_name=$5
    local auth_header=$6

    echo -e "\n${BLUE}Testing:${NC} $test_name"
    echo -e "${BLUE}Request:${NC} $method $endpoint"
    
    if [ -n "$data" ]; then
        echo -e "${BLUE}Data:${NC} $data"
    fi

    # Make the request
    if [ -n "$auth_header" ]; then
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Authorization: Bearer $auth_header" \
                -H "Content-Type: application/json" \
                -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Authorization: Bearer $auth_header" \
                -H "Content-Type: application/json")
        fi
    else
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json" \
                -d "$data")
        else
            response=$(curl -s -w "\n%{http_code}" -X $method "$BASE_URL$endpoint" \
                -H "Content-Type: application/json")
        fi
    fi

    # Extract status code and body
    status_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)

    echo -e "${BLUE}Response Status:${NC} $status_code"
    echo -e "${BLUE}Response Body:${NC} $body" | head -c 200
    if [ ${#body} -gt 200 ]; then
        echo "..."
    fi

    # Check status code
    if [ "$status_code" = "$expected_status" ]; then
        print_status "PASS" "$test_name - Status Code: $status_code"
        echo "$body"
        return 0
    else
        print_status "FAIL" "$test_name - Expected: $expected_status, Got: $status_code"
        echo "$body"
        return 1
    fi
}

# Function to extract value from JSON response
extract_json_value() {
    local json=$1
    local key=$2
    echo "$json" | grep -o "\"$key\":\"[^\"]*\"" | cut -d'"' -f4
}

# Start testing
echo -e "${BLUE}🚀 Starting Temp Orders API Testing${NC}"
echo -e "${BLUE}Base URL:${NC} $BASE_URL"
echo -e "${BLUE}Test User:${NC} $TEST_EMAIL"
echo ""

# Step 1: Authentication
echo -e "\n${YELLOW}=== STEP 1: AUTHENTICATION ===${NC}"

auth_response=$(api_test "POST" "/auth/login" \
    "{\"email\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}" \
    "200" "User Authentication")

if [ $? -eq 0 ]; then
    TOKEN=$(extract_json_value "$auth_response" "token")
    if [ -z "$TOKEN" ]; then
        # Try alternative token extraction
        TOKEN=$(echo "$auth_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    fi
    
    if [ -n "$TOKEN" ]; then
        print_status "PASS" "Token extracted successfully"
        echo -e "${BLUE}Token:${NC} ${TOKEN:0:20}..."
    else
        print_status "FAIL" "Failed to extract authentication token"
        echo "Response: $auth_response"
        exit 1
    fi
else
    print_status "FAIL" "Authentication failed"
    exit 1
fi

# Step 2: Create Temp Order
echo -e "\n${YELLOW}=== STEP 2: CREATE TEMP ORDER ===${NC}"

temp_order_data='{
    "vendorId": "01j5r8zqhm5kx8qp6j9y8x7w5v",
    "branchId": "01j5r8zqhm5kx8qp6j9y8x7w5w",
    "sectionId": "01j5r8zqhm5kx8qp6j9y8x7w5x",
    "action": "Purchase",
    "type": "Instant",
    "delivery": "Dinein",
    "items": {
        "01j5r8zqhm5kx8qp6j9y8x7w5y": 2,
        "01j5r8zqhm5kx8qp6j9y8x7w5z": 1
    },
    "meta": {
        "charges": {
            "service": 50,
            "tax": 25
        },
        "customerName": "Test Customer",
        "customerPhone": "+254722332233"
    }
}'

create_response=$(api_test "POST" "/temp-orders" "$temp_order_data" "200" "Create Temp Order" "$TOKEN")

if [ $? -eq 0 ]; then
    TEMP_ORDER_ID=$(extract_json_value "$create_response" "id")
    if [ -z "$TEMP_ORDER_ID" ]; then
        TEMP_ORDER_ID=$(echo "$create_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    fi
    
    if [ -n "$TEMP_ORDER_ID" ]; then
        print_status "PASS" "Temp order created with ID: $TEMP_ORDER_ID"
    else
        print_status "FAIL" "Failed to extract temp order ID"
        echo "Response: $create_response"
    fi
fi

# Step 3: List Temp Orders
echo -e "\n${YELLOW}=== STEP 3: LIST TEMP ORDERS ===${NC}"

list_response=$(api_test "GET" "/temp-orders" "" "200" "List Temp Orders" "$TOKEN")

if [ $? -eq 0 ]; then
    # Check if response contains data array
    if echo "$list_response" | grep -q '"data":\['; then
        print_status "PASS" "Temp orders list contains data array"
    else
        print_status "FAIL" "Temp orders list missing data array"
    fi
fi

# Step 4: Get Single Temp Order
if [ -n "$TEMP_ORDER_ID" ]; then
    echo -e "\n${YELLOW}=== STEP 4: GET SINGLE TEMP ORDER ===${NC}"
    
    get_response=$(api_test "GET" "/temp-orders/$TEMP_ORDER_ID" "" "200" "Get Single Temp Order" "$TOKEN")
    
    if [ $? -eq 0 ]; then
        # Check if response contains required fields
        if echo "$get_response" | grep -q '"id"' && echo "$get_response" | grep -q '"items"' && echo "$get_response" | grep -q '"total"'; then
            print_status "PASS" "Temp order response contains required fields (id, items, total)"
        else
            print_status "FAIL" "Temp order response missing required fields"
        fi
    fi
fi

# Step 5: Update Temp Order
if [ -n "$TEMP_ORDER_ID" ]; then
    echo -e "\n${YELLOW}=== STEP 5: UPDATE TEMP ORDER ===${NC}"
    
    update_data='{
        "items": {
            "01j5r8zqhm5kx8qp6j9y8x7w5y": 3,
            "01j5r8zqhm5kx8qp6j9y8x7w5z": 2
        },
        "meta": {
            "charges": {
                "service": 60,
                "tax": 30
            },
            "customerName": "Updated Customer",
            "notes": "Updated order"
        }
    }'
    
    update_response=$(api_test "PUT" "/temp-orders/$TEMP_ORDER_ID" "$update_data" "200" "Update Temp Order" "$TOKEN")
    
    if [ $? -eq 0 ]; then
        # Check if update was successful
        if echo "$update_response" | grep -q '"customerName":"Updated Customer"' || echo "$update_response" | grep -q '"notes":"Updated order"'; then
            print_status "PASS" "Temp order updated successfully"
        else
            print_status "WARN" "Temp order update response unclear"
        fi
    fi
fi

# Step 6: Place Order (Convert Temp Order)
if [ -n "$TEMP_ORDER_ID" ]; then
    echo -e "\n${YELLOW}=== STEP 6: PLACE ORDER ===${NC}"
    
    place_data='{
        "ref": "test_payment_ref_123"
    }'
    
    place_response=$(api_test "POST" "/temp-orders/$TEMP_ORDER_ID/place-order" "$place_data" "200" "Place Order" "$TOKEN")
    
    if [ $? -eq 0 ]; then
        # Check if order was placed (status should be 'Placed')
        if echo "$place_response" | grep -q '"status":"Placed"'; then
            print_status "PASS" "Order placed successfully - Status: Placed"
            
            # Extract placed order ID
            PLACED_ORDER_ID=$(extract_json_value "$place_response" "id")
            if [ -z "$PLACED_ORDER_ID" ]; then
                PLACED_ORDER_ID=$(echo "$place_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
            fi
            
            # Check if invoice was created
            if echo "$place_response" | grep -q '"invoices"'; then
                print_status "PASS" "Invoice created for placed order"
            else
                print_status "WARN" "Invoice not found in placed order response"
            fi
            
        else
            print_status "FAIL" "Order placement failed - Status not 'Placed'"
        fi
    fi
fi

# Step 7: Test Error Cases
echo -e "\n${YELLOW}=== STEP 7: ERROR HANDLING TESTS ===${NC}"

# Test unauthorized access
api_test "GET" "/temp-orders" "" "401" "Unauthorized Access (No Token)" ""

# Test invalid temp order creation
invalid_data='{"vendorId": "", "items": {}}'
api_test "POST" "/temp-orders" "$invalid_data" "400" "Invalid Temp Order Creation" "$TOKEN"

# Test non-existent temp order
api_test "GET" "/temp-orders/invalid-id" "" "404" "Non-existent Temp Order" "$TOKEN"

# Test placing non-existent order
api_test "POST" "/temp-orders/invalid-id/place-order" '{}' "404" "Place Non-existent Order" "$TOKEN"

# Step 8: Cleanup (Delete temp order if still exists)
if [ -n "$TEMP_ORDER_ID" ] && [ -z "$PLACED_ORDER_ID" ]; then
    echo -e "\n${YELLOW}=== STEP 8: CLEANUP ===${NC}"
    
    api_test "DELETE" "/temp-orders/$TEMP_ORDER_ID" "" "204" "Delete Temp Order" "$TOKEN"
fi

# Test Summary
echo -e "\n${YELLOW}=== TEST SUMMARY ===${NC}"
echo -e "${BLUE}Total Tests:${NC} $TOTAL_TESTS"
echo -e "${GREEN}Passed:${NC} $PASSED_TESTS"
echo -e "${RED}Failed:${NC} $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed! Unified temp order system is working correctly.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Please review the failures above.${NC}"
    exit 1
fi
