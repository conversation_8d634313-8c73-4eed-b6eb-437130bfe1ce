import { bind } from '@adonisjs/route-model-binding'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { CallbackMetadataItem } from 'App/Interfaces/CallbackMetadataItem'
import Payment from 'App/Models/Payment'

export default class PaymentIpnController {
  /**
   * @name Payment IPN
   * @group Payment
   * @summary Handle payment IPN
   * @description Handle payment IPN
   * @requestBody {"content":{"application/json":{"schema":{"properties":{"paymentStatus":{"type":"string"},"transactionId":{"type":"string"}},"required":["paymentStatus","transactionId"]}}}}
   */
  @bind()
  public async handle({ request, response }: HttpContextContract, payment: Payment) {
    try {
      const body = request.body()
      console.log('=== PAYMENT IPN REQUEST ===')
      console.log('Payment ID:', payment.id)
      console.log('Payment Method:', payment.method)
      console.log('Request Body:', JSON.stringify(body, null, 2))
      console.log('Timestamp:', new Date().toISOString())

      const { paymentStatus, transactionId } = body

      switch (payment.method.toLowerCase()) {
        case 'paypal':
          if (transactionId) {
            payment.receipt = transactionId
            payment.status = paymentStatus === 'Success' ? 'Success' : 'Failed'
            await payment.save()
            console.log(`PayPal payment ${payment.id} updated: ${payment.status}`)
          }
          break

        case 'stripe':
          if (transactionId) {
            payment.receipt = transactionId
            payment.status = paymentStatus === 'Success' ? 'Success' : 'Failed'
            await payment.save()
            console.log(`Stripe payment ${payment.id} updated: ${payment.status}`)
          }
          break

        case 'mpesa':
          // Handle M-Pesa STK Push callback
          if (body.Body && body.Body.stkCallback) {
            const stkCallback = body.Body.stkCallback
            console.log('=== MPESA STK CALLBACK ===')
            console.log('Result Code:', stkCallback.ResultCode)
            console.log('Result Description:', stkCallback.ResultDesc)

            if (stkCallback.ResultCode === 0) {
              // Success - extract metadata
              const callbackMetadataItems = stkCallback.CallbackMetadata?.Item || []
              const parsed = callbackMetadataItems.reduce(
                (acc: Record<string, string>, item: CallbackMetadataItem) => ({
                  ...acc,
                  [item.Name]: item.Value,
                }),
                {}
              )

              payment.receipt = parsed['MpesaReceiptNumber'] || ''
              payment.status = 'Success'
              await payment.save()

              // Update related invoice and order
              await payment.load('invoice', (iq) => iq.preload('order'))
              if (payment.invoice) {
                await payment.invoice.merge({ status: 'Paid' }).save()
                if (payment.invoice.order) {
                  await payment.invoice.order.merge({ status: 'Completed' }).save()
                }
              }

              console.log(`M-Pesa STK payment ${payment.id} completed successfully`)
            } else {
              // Failed
              payment.status = 'Failed'
              await payment.save()
              console.log(`M-Pesa STK payment ${payment.id} failed: ${stkCallback.ResultDesc}`)
            }
          } else if (paymentStatus === 'Success' && transactionId) {
            // Fallback for other M-Pesa callback formats
            payment.status = 'Success'
            payment.receipt = transactionId
            await payment.save()

            await payment.load('invoice', (iq) => iq.preload('order'))
            if (payment.invoice) {
              await payment.invoice.merge({ status: 'Paid' }).save()
              if (payment.invoice.order) {
                await payment.invoice.order.merge({ status: 'Completed' }).save()
              }
            }
            console.log(`M-Pesa payment ${payment.id} updated via fallback method`)
          }
          break

        default:
          console.warn(`Unknown payment method: ${payment.method}`)
          break
      }

      console.log('=== PAYMENT IPN SUCCESS ===')
      return response.status(200).json({ status: 'success' })

    } catch (error) {
      console.error('=== PAYMENT IPN ERROR ===')
      console.error('Payment ID:', payment.id)
      console.error('Error:', error.message)
      console.error('Stack:', error.stack)
      return response.status(500).json({ status: 'error', message: 'Internal server error' })
    }
  }


}
