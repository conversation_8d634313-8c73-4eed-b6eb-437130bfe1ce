import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Env from '@ioc:Adonis/Core/Env'
import axios from 'axios'

export default class PlacesProxyController {
  /**
   * Proxy requests to Google Places API
   */
  public async handle({ request, response }: HttpContextContract) {
    try {
      // Get the path after /api/places-proxy/
      const path = request.param('*')
      
      // Construct the Google Places API URL
      const apiUrl = `https://maps.googleapis.com/maps/api/place/${path}`
      
      // Get all query parameters
      const queryParams = request.qs()
      
      // Add the API key to the query parameters
      queryParams.key = Env.get('GOOGLE_PLACES_API_KEY')
      
      // Make the request to Google Places API
      const apiResponse = await axios.get(apiUrl, {
        params: queryParams,
      })
      
      // Set CORS headers
      response.header('Access-Control-Allow-Origin', '*')
      response.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
      response.header('Access-Control-Allow-Headers', 'Content-Type')
      
      // Return the API response
      return response.json(apiResponse.data)
    } catch (error) {
      // Handle errors
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        return response.status(error.response.status).json(error.response.data)
      } else if (error.request) {
        // The request was made but no response was received
        return response.status(500).json({ error: 'No response from Google Places API' })
      } else {
        // Something happened in setting up the request that triggered an Error
        return response.status(500).json({ error: error.message })
      }
    }
  }

  /**
   * Handle OPTIONS requests for CORS preflight
   */
  public async options({ response }: HttpContextContract) {
    response.header('Access-Control-Allow-Origin', '*')
    response.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
    response.header('Access-Control-Allow-Headers', 'Content-Type')
    return response.status(204)
  }
} 