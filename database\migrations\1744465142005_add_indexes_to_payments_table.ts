import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'payments'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add individual indexes
      table.index('user_id')
      table.index('vendor_id')
      table.index('invoice_id')
      table.index('status')
      table.index('created_at')
      table.index('ref')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove indexes
      table.dropIndex('user_id')
      table.dropIndex('vendor_id')
      table.dropIndex('invoice_id')
      table.dropIndex('status')
      table.dropIndex('created_at')
      table.dropIndex('ref')
    })
  }
} 