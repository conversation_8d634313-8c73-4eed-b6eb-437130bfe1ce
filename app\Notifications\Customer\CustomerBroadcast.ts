import { NotificationChannelsList } from '@ioc:Verful/Notification'
import { NotificationContract } from '@ioc:Verful/Notification'
import User from 'App/Models/User'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

export default class CustomerBroadcast implements NotificationContract {
  constructor(
    public title: string,
    public message: string,
    public channels: keyof NotificationChannelsList | Array<keyof NotificationChannelsList> = [
      'fcm',
    ],
    public actions?: any
  ) {}

  public via(_notifiable: User) {
    return this.channels
  }

  public toSms(notifiable: User) {
    const text = this.message
      .replace('{firstName}', notifiable.firstName)
      .replace('{lastName}', notifiable.lastName)

    if (!notifiable.phone) {
      throw new Error('User phone number is required for SMS notification')
    }

    return {
      text,
      phone: notifiable.phone,
    }
  }

  public toDatabase(notifiable: User) {
    const body = this.message
      .replace('{firstName}', notifiable.firstName)
      .replace('{lastName}', notifiable.lastName)

    // Create actions based on the broadcast type
    const actions = this.actions || NotificationHelper.createPromotionActions('general', {})

    return NotificationHelper.createNotificationData(
      this.title,
      body,
      actions,
      {
        category: NotificationCategory.PROMOTION,
        notificationType: NotificationType.BROADCAST_ANNOUNCEMENT,
        priority: NotificationPriority.LOW,
        campaignId: `broadcast_${new Date().getTime()}`
      },
      'https://cdn.verful.com/icons/verful-512x512.png'
    )
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const body = this.message
      .replace('{firstName}', notifiable.firstName)
      .replace('{lastName}', notifiable.lastName)

    return {
      title: this.title,
      body,
      url: '/notifications',
      // icon: 'https://cdn.verful.com/icons/verful-512x512.png',
      actions: this.actions,
    }
  }

  public toMail(notifiable: User) {
    const body = this.message
      .replace('{firstName}', notifiable.firstName)
      .replace('{lastName}', notifiable.lastName)

    return new CustomerMailer(notifiable, 'mails/broadcast', {
      greeting: `Hello ${notifiable.firstName} ${notifiable.lastName}!`,
      subject: this.title,
      intro: 'You have received a new broadcast message',
      body: body,
    })
  }
}