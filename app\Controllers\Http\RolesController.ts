import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Role from '../../Models/Role'
import { bind } from '@adonisjs/route-model-binding'

export default class RolesController {
    /**
     * @index
     * @summary Show all roles
     * @version 1.0.0
     * @description Role management for the application
     */

    public async index({ request }: HttpContextContract) {
        const { per = 50, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
        const tagQuery = Role.filter(filters)

        return await tagQuery.orderBy(order, sort).paginate(page, per)
    }

    /**
     * @store
     * @summary Create a tag
     * @description Create a tag with their details (name and details)
     * @requestBody {"name": "", "details": ""}
     */

    public async store({ request }: HttpContextContract) {
        const { name } = request.all()
        const role = new Role()

        role.fill({ name })

        return await role.save()
    }

    @bind()

    /**
     * @show
     * @summary Show a single Role
     * @description Show a Role with their details (name)
     * @responseBody 200 - <Role>
     * @response 404 - Role not found
     */

    public async show({ response }: HttpContextContract, role: Role) {
        try {
            return response.json(role)
        } catch (error) {
            return response.status(404).json({ message: "Role not found" }) 
        }
    }

    @bind()

    /**
     * @update
     * @summary Update a Role
     * @description Update a Role with their details (name)
     * @requestBody <Role>
     * @responseBody 200 - <Role>
     * @response 404 - Role not found
     */
    public async update({ request, response }: HttpContextContract, role:Role) {
        const { name } = request.all()

        try {

            role.merge({ name })
            await role.save()

            return response.json(role)
        } catch (error) {
            return response.status(404).json({ message: "Role not found" })
        }
    }

    @bind()

    /**
     * @destroy
     * @summary delete a Role
     * @reponseBody 204 - No content
     */

    public async destroy(_: HttpContextContract, role: Role) {
        return await role.delete()
    }
}
