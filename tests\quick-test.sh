#!/bin/bash

# Quick Test Script for Unified Temp Orders System
# Performs basic smoke tests to verify system functionality

# Configuration
BASE_URL="http://localhost:3080/v1"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="54722332233"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Quick Test - Unified Temp Orders System${NC}"
echo -e "${BLUE}Testing basic functionality...${NC}\n"

# Step 1: Test Authentication
echo -e "${BLUE}1. Testing Authentication...${NC}"
auth_response=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$TEST_EMAIL\",\"password\":\"$TEST_PASSWORD\"}")

if echo "$auth_response" | grep -q '"token"'; then
    echo -e "${GREEN}✅ Authentication: PASS${NC}"
    TOKEN=$(echo "$auth_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
else
    echo -e "${RED}❌ Authentication: FAIL${NC}"
    echo "Response: $auth_response"
    exit 1
fi

# Step 2: Test Temp Order Creation
echo -e "${BLUE}2. Testing Temp Order Creation...${NC}"
create_response=$(curl -s -X POST "$BASE_URL/temp-orders" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "vendorId": "01j5r8zqhm5kx8qp6j9y8x7w5v",
    "branchId": "01j5r8zqhm5kx8qp6j9y8x7w5w",
    "action": "Purchase",
    "type": "Instant",
    "delivery": "Dinein",
    "items": {
      "01j5r8zqhm5kx8qp6j9y8x7w5y": 1
    },
    "meta": {
      "charges": {"service": 10}
    }
  }')

if echo "$create_response" | grep -q '"id"' && echo "$create_response" | grep -q '"status":"Pending"'; then
    echo -e "${GREEN}✅ Temp Order Creation: PASS${NC}"
    TEMP_ORDER_ID=$(echo "$create_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
else
    echo -e "${RED}❌ Temp Order Creation: FAIL${NC}"
    echo "Response: $create_response"
    exit 1
fi

# Step 3: Test Temp Order Retrieval
echo -e "${BLUE}3. Testing Temp Order Retrieval...${NC}"
get_response=$(curl -s -X GET "$BASE_URL/temp-orders/$TEMP_ORDER_ID" \
  -H "Authorization: Bearer $TOKEN")

if echo "$get_response" | grep -q '"id"' && echo "$get_response" | grep -q '"items"'; then
    echo -e "${GREEN}✅ Temp Order Retrieval: PASS${NC}"
else
    echo -e "${RED}❌ Temp Order Retrieval: FAIL${NC}"
    echo "Response: $get_response"
    exit 1
fi

# Step 4: Test Temp Order Listing
echo -e "${BLUE}4. Testing Temp Order Listing...${NC}"
list_response=$(curl -s -X GET "$BASE_URL/temp-orders" \
  -H "Authorization: Bearer $TOKEN")

if echo "$list_response" | grep -q '"data"'; then
    echo -e "${GREEN}✅ Temp Order Listing: PASS${NC}"
else
    echo -e "${RED}❌ Temp Order Listing: FAIL${NC}"
    echo "Response: $list_response"
    exit 1
fi

# Step 5: Test Order Placement
echo -e "${BLUE}5. Testing Order Placement...${NC}"
place_response=$(curl -s -X POST "$BASE_URL/temp-orders/$TEMP_ORDER_ID/place-order" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"ref": "test_ref"}')

if echo "$place_response" | grep -q '"status":"Placed"'; then
    echo -e "${GREEN}✅ Order Placement: PASS${NC}"
else
    echo -e "${RED}❌ Order Placement: FAIL${NC}"
    echo "Response: $place_response"
    # Don't exit here as we still want to test error handling
fi

# Step 6: Test Error Handling
echo -e "${BLUE}6. Testing Error Handling...${NC}"
error_response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL/temp-orders/invalid-id" \
  -H "Authorization: Bearer $TOKEN")

status_code=$(echo "$error_response" | tail -c 4)
if [ "$status_code" = "404" ]; then
    echo -e "${GREEN}✅ Error Handling: PASS${NC}"
else
    echo -e "${RED}❌ Error Handling: FAIL (Expected 404, got $status_code)${NC}"
fi

echo -e "\n${BLUE}🎉 Quick Test Complete!${NC}"
echo -e "${GREEN}All basic functionality is working correctly.${NC}"
echo -e "${BLUE}For comprehensive testing, run: ./tests/temp-orders-api-tests.sh${NC}"
