import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate, belongsTo, manyToMany, BelongsTo, ManyToMany, computed } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import { ServiceOptionType } from './ServiceConfigurationOption'
import Duration from './Duration'
import Product from './Product'

export default class ServiceOption extends BaseModel {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string | null

  @column()
  public name: string

  @column()
  public type: ServiceOptionType

  @column()
  public description: string | null

  @column()
  public defaultPriceAdjustment: number

  @column()
  public durationId: string | null

  @column()
  public constraints: Record<string, any>

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(serviceOption: ServiceOption) {
    serviceOption.id = ulid().toLowerCase()
  }

  @belongsTo(() => Duration)
  public duration: BelongsTo<typeof Duration>

  @manyToMany(() => Product, {
    pivotTable: 'product_service_options',
    localKey: 'id',
    pivotForeignKey: 'service_option_id',
    relatedKey: 'id',
    pivotRelatedForeignKey: 'product_id',
    pivotColumns: ['price_adjustment_override', 'is_default', 'sort_order']
  })
  public products: ManyToMany<typeof Product>

  @computed()
  public get isDurationOption(): boolean {
    return this.type === ServiceOptionType.DURATION
  }

  @computed()
  public get hasCalendarIntegration(): boolean {
    return this.isDurationOption && !!this.durationId
  }

  @computed()
  public get displayPrice(): string {
    if (this.defaultPriceAdjustment === 0) {
      return 'Included'
    } else if (this.defaultPriceAdjustment > 0) {
      return `+$${this.defaultPriceAdjustment.toFixed(2)}`
    } else {
      return `-$${Math.abs(this.defaultPriceAdjustment).toFixed(2)}`
    }
  }

  /**
   * Get the calendar block time if this is a duration option
   */
  public async getCalendarBlockMinutes(): Promise<number | null> {
    if (!this.isDurationOption || !this.durationId) {
      return null
    }

    if (!this.duration) {
      await this.load('duration')
    }

    return this.duration?.calendarBlockMinutes || null
  }

  /**
   * Check if this option can be combined with another option
   */
  public canCombineWith(otherOption: ServiceOption): boolean {
    // Same type options generally can't be combined (except add-ons)
    if (this.type === otherOption.type && this.type !== ServiceOptionType.ADD_ON) {
      return false
    }

    // Check constraints
    if (this.constraints.excludes && Array.isArray(this.constraints.excludes)) {
      if (this.constraints.excludes.includes(otherOption.id) || 
          this.constraints.excludes.includes(otherOption.type)) {
        return false
      }
    }

    if (otherOption.constraints.excludes && Array.isArray(otherOption.constraints.excludes)) {
      if (otherOption.constraints.excludes.includes(this.id) || 
          otherOption.constraints.excludes.includes(this.type)) {
        return false
      }
    }

    return true
  }

  /**
   * Check if this option requires other options
   */
  public getRequiredOptions(): string[] {
    return this.constraints.requires || []
  }

  /**
   * Check if this option has dependencies that are satisfied
   */
  public async checkDependencies(selectedOptionIds: string[]): Promise<{ satisfied: boolean; missing: string[] }> {
    const required = this.getRequiredOptions()
    const missing = required.filter(reqId => !selectedOptionIds.includes(reqId))

    return {
      satisfied: missing.length === 0,
      missing
    }
  }

  /**
   * Validate the option configuration
   */
  public async validateConfiguration(): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Validate duration reference for duration options
    if (this.isDurationOption) {
      if (!this.durationId) {
        errors.push('Duration options must have a valid duration ID')
      } else {
        // Check if duration exists and is active
        const duration = await Duration.find(this.durationId)
        if (!duration) {
          errors.push(`Referenced duration ID '${this.durationId}' does not exist`)
        } else if (!duration.active) {
          errors.push(`Referenced duration '${duration.name}' is not active`)
        }
      }
    } else {
      // Non-duration options should not have duration ID
      if (this.durationId) {
        errors.push('Non-duration options should not have a duration ID')
      }
    }

    // Validate constraints
    if (this.constraints) {
      if (this.constraints.requires && !Array.isArray(this.constraints.requires)) {
        errors.push('Constraint "requires" must be an array')
      }
      if (this.constraints.excludes && !Array.isArray(this.constraints.excludes)) {
        errors.push('Constraint "excludes" must be an array')
      }
    }

    // Validate default price adjustment
    if (typeof this.defaultPriceAdjustment !== 'number') {
      errors.push('Default price adjustment must be a number')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Create option templates for common option types
   */
  public static createTemplate(
    name: string,
    type: ServiceOptionType,
    vendorId: string | null = null,
    options: Partial<ServiceOption> = {}
  ): Partial<ServiceOption> {
    const defaultsByType = {
      [ServiceOptionType.DURATION]: {
        description: 'Service duration option',
        defaultPriceAdjustment: 0,
        constraints: {}
      },
      [ServiceOptionType.PERSONNEL]: {
        description: 'Personnel/staff option',
        defaultPriceAdjustment: 0,
        constraints: {}
      },
      [ServiceOptionType.EQUIPMENT]: {
        description: 'Equipment option',
        defaultPriceAdjustment: 0,
        constraints: {}
      },
      [ServiceOptionType.LOCATION]: {
        description: 'Location/delivery option',
        defaultPriceAdjustment: 0,
        constraints: {}
      },
      [ServiceOptionType.ADD_ON]: {
        description: 'Additional service option',
        defaultPriceAdjustment: 25,
        constraints: {}
      },
      [ServiceOptionType.DELIVERY_METHOD]: {
        description: 'Service delivery method',
        defaultPriceAdjustment: 0,
        constraints: {}
      },
      [ServiceOptionType.EXPERTISE_LEVEL]: {
        description: 'Expertise level option',
        defaultPriceAdjustment: 0,
        constraints: {}
      },
      [ServiceOptionType.SCHEDULING]: {
        description: 'Scheduling preference option',
        defaultPriceAdjustment: 0,
        constraints: {}
      },
      [ServiceOptionType.CUSTOM]: {
        description: 'Custom service option',
        defaultPriceAdjustment: 0,
        constraints: {}
      }
    }

    return {
      name,
      type,
      vendorId,
      durationId: type === ServiceOptionType.DURATION ? null : undefined,
      active: true,
      ...defaultsByType[type],
      ...options
    }
  }

  /**
   * Get options by type
   */
  public static async getByType(type: ServiceOptionType, vendorId?: string) {
    const query = ServiceOption.query()
      .where('type', type)
      .where('active', true)

    if (vendorId) {
      query.where((builder) => {
        builder.whereNull('vendorId').orWhere('vendorId', vendorId)
      })
    }

    return await query.orderBy('name')
  }

  /**
   * Get vendor-specific options
   */
  public static async getVendorOptions(vendorId: string) {
    return await ServiceOption.query()
      .where('vendorId', vendorId)
      .where('active', true)
      .orderBy(['type', 'name'])
  }

  /**
   * Get global options (not vendor-specific)
   */
  public static async getGlobalOptions() {
    return await ServiceOption.query()
      .whereNull('vendorId')
      .where('active', true)
      .orderBy(['type', 'name'])
  }

  /**
   * Search options by name or description
   */
  public static async search(query: string, vendorId?: string) {
    const searchQuery = ServiceOption.query()
      .where((builder) => {
        builder
          .where('name', 'ILIKE', `%${query}%`)
          .orWhere('description', 'ILIKE', `%${query}%`)
      })
      .where('active', true)

    if (vendorId) {
      searchQuery.where((builder) => {
        builder.whereNull('vendorId').orWhere('vendorId', vendorId)
      })
    }

    return await searchQuery
      .preload('duration')
      .orderBy('name')
  }
}
