import { NotificationContract } from '@ioc:Verful/Notification'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import User from 'App/Models/User'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'

interface Subscription {
  id: string | number
  planName: string
  amount: number
  currency: string
  billingCycle: string
  startDate: Date
  nextBillingDate: Date
}

export default class CustomerNewSubscription implements NotificationContract {
  constructor(
    private subscription: Subscription
  ) {}

  public via(_notifiable) {
    return ['database' as const, 'mail' as const, 'fcm' as const, 'sms' as const]
  }

  public toDatabase(_notifiable: User) {
    const title = 'Subscription Activated'
    const body = `Your ${this.subscription.planName} subscription has been activated. You'll be billed ${this.subscription.currency} ${this.subscription.amount} ${this.subscription.billingCycle}.`

    const actions = [
      NotificationHelper.createAction(
        NotificationActionType.VIEW_SUBSCRIPTION,
        'View Subscription',
        { subscriptionId: this.subscription.id }
      ),
      NotificationHelper.createAction(
        NotificationActionType.VIEW_PAYMENT_HISTORY,
        'Billing History',
        {}
      ),
      NotificationHelper.createAction(
        NotificationActionType.UPDATE_SETTINGS,
        'Manage Settings',
        {}
      )
    ]

    return NotificationHelper.createNotificationData(
      title,
      body,
      actions,
      {
        category: NotificationCategory.ACCOUNT,
        notificationType: NotificationType.SUBSCRIPTION_CREATED,
        priority: NotificationPriority.MEDIUM,
        entityId: this.subscription.id,
        entityType: 'subscription',
        planName: this.subscription.planName,
        amount: this.subscription.amount,
        currency: this.subscription.currency,
        billingCycle: this.subscription.billingCycle,
        startDate: this.subscription.startDate.toISOString(),
        nextBillingDate: this.subscription.nextBillingDate.toISOString()
      },
      'https://cdn.verful.com/icons/subscription-icon.png'
    )
  }

  public toMail(notifiable: User) {
    return new CustomerMailer(notifiable, 'newSubscription', {
      greeting: `Hello ${notifiable.firstName} ${notifiable.lastName}!`,
      subject: 'New Subscription',
      html: 'You have a new subscription',
    })
  }
}
