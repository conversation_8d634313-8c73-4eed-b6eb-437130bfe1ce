import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'
import ServiceOption from 'App/Models/ServiceOption'
import ProductServiceOption from 'App/Models/ProductServiceOption'

export default class ProductServiceOptionsController {
  /**
   * Display service options for a specific product
   */
  public async index({ params, request, response }: HttpContextContract) {
    try {
      const productId = params.productId
      const type = request.input('type')

      let query = ProductServiceOption.query()
        .where('productId', productId)
        .preload('serviceOption', (optionQuery) => {
          optionQuery.preload('duration')
        })

      if (type) {
        query = query.whereHas('serviceOption', (serviceOptionQuery) => {
          serviceOptionQuery.where('type', type)
        })
      }

      const options = await query.orderBy('sortOrder')

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch product service options',
        error: error.message
      })
    }
  }

  /**
   * Add a service option to a product
   */
  public async store({ params, request, response }: HttpContextContract) {
    try {
      const productId = params.productId
      const { serviceOptionId, priceAdjustmentOverride, isDefault, sortOrder } = request.only([
        'serviceOptionId', 'priceAdjustmentOverride', 'isDefault', 'sortOrder'
      ])

      // Verify product exists
      await Product.findOrFail(productId)

      // Verify service option exists
      await ServiceOption.findOrFail(serviceOptionId)

      // Check for conflicts
      const conflictCheck = await ProductServiceOption.checkConflicts(productId, serviceOptionId)
      if (conflictCheck.hasConflicts) {
        return response.status(400).json({
          success: false,
          message: 'Cannot add service option due to conflicts',
          conflicts: conflictCheck.conflicts
        })
      }

      // Create the pivot record
      const pivot = await ProductServiceOption.createWithDefaults(productId, serviceOptionId, {
        priceAdjustmentOverride,
        isDefault: isDefault || false,
        sortOrder
      })

      // Load related data for response
      await pivot.load('serviceOption', (query) => {
        query.preload('duration')
      })

      return response.status(201).json({
        success: true,
        message: 'Service option added to product successfully',
        data: pivot
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to add service option to product',
        error: error.message
      })
    }
  }

  /**
   * Display a specific product service option
   */
  public async show({ params, response }: HttpContextContract) {
    try {
      const pivot = await ProductServiceOption.query()
        .where('id', params.id)
        .where('productId', params.productId)
        .preload('serviceOption', (query) => {
          query.preload('duration')
        })
        .preload('product')
        .firstOrFail()

      const validation = await pivot.validateConfiguration()
      const calendarBlockMinutes = await pivot.getCalendarBlockMinutes()

      return response.json({
        success: true,
        data: {
          ...pivot.toJSON(),
          validation,
          calendarBlockMinutes
        }
      })
    } catch (error) {
      return response.status(404).json({
        success: false,
        message: 'Product service option not found',
        error: error.message
      })
    }
  }

  /**
   * Update a product service option
   */
  public async update({ params, request, response }: HttpContextContract) {
    try {
      const pivot = await ProductServiceOption.query()
        .where('id', params.id)
        .where('productId', params.productId)
        .firstOrFail()

      const payload = request.only([
        'priceAdjustmentOverride', 'isDefault', 'sortOrder'
      ])

      pivot.merge(payload)

      // Validate the configuration before saving
      const validation = await pivot.validateConfiguration()
      if (!validation.valid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid configuration',
          errors: validation.errors
        })
      }

      await pivot.save()
      await pivot.load('serviceOption', (query) => {
        query.preload('duration')
      })

      return response.json({
        success: true,
        message: 'Product service option updated successfully',
        data: pivot
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to update product service option',
        error: error.message
      })
    }
  }

  /**
   * Remove a service option from a product
   */
  public async destroy({ params, response }: HttpContextContract) {
    try {
      const pivot = await ProductServiceOption.query()
        .where('id', params.id)
        .where('productId', params.productId)
        .firstOrFail()

      await pivot.delete()

      return response.json({
        success: true,
        message: 'Service option removed from product successfully'
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to remove service option from product',
        error: error.message
      })
    }
  }

  /**
   * Bulk update sort orders for product service options
   */
  public async updateSortOrders({ params, request, response }: HttpContextContract) {
    try {
      const productId = params.productId
      const { optionOrders } = request.only(['optionOrders'])

      if (!Array.isArray(optionOrders)) {
        return response.status(400).json({
          success: false,
          message: 'Option orders must be an array'
        })
      }

      await ProductServiceOption.updateSortOrders(productId, optionOrders)

      return response.json({
        success: true,
        message: 'Option sort orders updated successfully'
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Failed to update sort orders',
        error: error.message
      })
    }
  }

  /**
   * Get default options for a product
   */
  public async getDefaults({ params, response }: HttpContextContract) {
    try {
      const productId = params.productId

      const defaults = await ProductServiceOption.getDefaults(productId)

      return response.json({
        success: true,
        data: defaults
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch default options',
        error: error.message
      })
    }
  }

  /**
   * Get options by type for a product
   */
  public async getByType({ params, response }: HttpContextContract) {
    try {
      const { productId, type } = params

      const options = await ProductServiceOption.getByType(productId, type)

      return response.json({
        success: true,
        data: options
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch options by type',
        error: error.message
      })
    }
  }

  /**
   * Get statistics for a product's service options
   */
  public async getStatistics({ params, response }: HttpContextContract) {
    try {
      const productId = params.productId

      const statistics = await ProductServiceOption.getProductStatistics(productId)

      return response.json({
        success: true,
        data: statistics
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch statistics',
        error: error.message
      })
    }
  }

  /**
   * Check conflicts before adding an option
   */
  public async checkConflicts({ params, request, response }: HttpContextContract) {
    try {
      const productId = params.productId
      const { serviceOptionId } = request.only(['serviceOptionId'])

      if (!serviceOptionId) {
        return response.status(400).json({
          success: false,
          message: 'Service option ID is required'
        })
      }

      const conflictCheck = await ProductServiceOption.checkConflicts(productId, serviceOptionId)

      return response.json({
        success: true,
        data: conflictCheck
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to check conflicts',
        error: error.message
      })
    }
  }
}
