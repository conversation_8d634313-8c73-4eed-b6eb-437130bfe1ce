import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Duration from 'App/Models/Duration'

export default class extends BaseSeeder {
  public async run() {
    // Create common duration templates for different service types
    
    // Short duration services (30min - 1hr)
    await Duration.createMany([
      Duration.createTemplate('Quick Service (30 minutes)', 30, 'short', {
        description: 'Quick 30-minute service for basic tasks',
        maxConcurrent: 8,
        schedulingRules: {
          minAdvanceHours: 1,
          maxPerDay: 12,
          timeSlots: [],
          blackoutDays: []
        }
      }),
      
      Duration.createTemplate('Standard Service (1 hour)', 60, 'short', {
        description: 'Standard 1-hour service for most common tasks',
        bufferMinutes: 20,
        maxConcurrent: 6,
        schedulingRules: {
          minAdvanceHours: 2,
          maxPerDay: 8,
          timeSlots: [],
          blackoutDays: ['sunday']
        }
      })
    ])

    // Medium duration services (1.5hr - 2.5hr)
    await Duration.createMany([
      Duration.createTemplate('Extended Service (1.5 hours)', 90, 'medium', {
        description: 'Extended 90-minute service for detailed work',
        bufferMinutes: 30,
        maxConcurrent: 4,
        allowsBackToBack: true,
        schedulingRules: {
          minAdvanceHours: 4,
          maxPerDay: 6,
          timeSlots: [],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 1,
          equipmentRequired: ['basic-kit']
        }
      }),
      
      Duration.createTemplate('Comprehensive Service (2 hours)', 120, 'medium', {
        description: 'Comprehensive 2-hour service for thorough work',
        bufferMinutes: 40,
        maxConcurrent: 3,
        allowsBackToBack: false,
        requiredBreakAfter: 30,
        schedulingRules: {
          minAdvanceHours: 8,
          maxPerDay: 4,
          timeSlots: ['morning', 'afternoon'],
          blackoutDays: ['sunday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 2,
          equipmentRequired: ['basic-kit', 'premium-tools']
        }
      })
    ])

    // Long duration services (3hr - 5hr)
    await Duration.createMany([
      Duration.createTemplate('Deep Service (4 hours)', 240, 'long', {
        description: 'Deep 4-hour service for intensive work',
        bufferMinutes: 60,
        maxConcurrent: 2,
        allowsBackToBack: false,
        requiredBreakAfter: 60,
        schedulingRules: {
          minAdvanceHours: 24,
          maxPerDay: 2,
          timeSlots: ['morning', 'afternoon'],
          blackoutDays: ['sunday', 'saturday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 2,
          equipmentRequired: ['basic-kit', 'premium-tools', 'specialized-equipment']
        }
      }),
      
      Duration.createTemplate('Premium Service (5 hours)', 300, 'long', {
        description: 'Premium 5-hour service for complex projects',
        bufferMinutes: 90,
        maxConcurrent: 1,
        allowsBackToBack: false,
        requiredBreakAfter: 120,
        schedulingRules: {
          minAdvanceHours: 48,
          maxPerDay: 1,
          timeSlots: ['morning'],
          blackoutDays: ['sunday', 'saturday']
        },
        branchConstraints: {
          respectBranchHours: true,
          staffRequired: 3,
          equipmentRequired: ['basic-kit', 'premium-tools', 'specialized-equipment', 'vehicle']
        }
      })
    ])

    // Full day services (6hr+)
    await Duration.createMany([
      Duration.createTemplate('Full Day Service (8 hours)', 480, 'full-day', {
        description: 'Full day service for major projects',
        bufferMinutes: 120,
        maxConcurrent: 1,
        allowsBackToBack: false,
        requiredBreakAfter: 180,
        schedulingRules: {
          minAdvanceHours: 72,
          maxPerDay: 1,
          timeSlots: ['morning'],
          blackoutDays: ['sunday', 'saturday', 'friday']
        },
        branchConstraints: {
          respectBranchHours: false, // Full day services can extend beyond normal hours
          staffRequired: 4,
          equipmentRequired: ['basic-kit', 'premium-tools', 'specialized-equipment', 'vehicle']
        }
      })
    ])

    console.log('✅ Duration templates seeded successfully')
  }
}
