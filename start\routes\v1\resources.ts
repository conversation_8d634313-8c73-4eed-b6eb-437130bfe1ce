import Route from '@ioc:Adonis/Core/Route'

Route.group(() => {
  // V1 routes
  Route.group(() => {
    // Places API Proxy Route
    Route.any('places-proxy/*', 'PlacesProxyController.handle').as('places-proxy')

    Route.resource('addresses', 'AddressesController').apiOnly().as('addresses')
    Route.resource('campaigns', 'CampaignsController').apiOnly().as('campaigns')
    Route.resource('orders', 'OrdersController').apiOnly().as('orders')
    Route.get('orders/:id/fulfillment-status', 'OrdersController.fulfillmentStatus').as(
      'orders.fulfillment-status'
    )
    Route.post('orders/:id/auto-assign-departments', 'OrdersController.autoAssignDepartments').as(
      'orders.auto-assign-departments'
    )
    // Order Delivery Routes
    Route.group(() => {
      Route.post('assign-vendor', 'OrderDeliveryController.assignVendor').as('assignVendor')
      Route.put('status', 'OrderDeliveryController.updateStatus').as('updateStatus')
      Route.put('tracking', 'OrderDeliveryController.updateTracking').as('updateTracking')
      Route.get('tracking', 'OrderDeliveryController.getTracking').as('getTracking')
      Route.post('signature', 'OrderDeliveryController.recordSignature').as('recordSignature')
    })
      .prefix('orders/:orderId/delivery')
      .as('orders.delivery')

    // Unified order creation route (bypasses temp-order phase)
    Route.post('unified-orders', 'UnifiedOrdersController.store')
      .as('unified.orders.store')
      .middleware(['auth'])
    Route.resource('temp-orders', 'TempOrdersController')
      .apiOnly()
      .as('temp.orders')
      .middleware({ '*': ['auth'] })
    Route.post('temp-orders/:id/place-order', 'TempOrdersController.placeOrder')
      .as('temp.orders.place')
      .middleware(['auth', 'staffPermission:place-order'])
    Route.resource('notes', 'NotesController').apiOnly().as('notes')
    Route.resource('notifications', 'NotificationsController').apiOnly().as('notifications')
    Route.resource('settings', 'SettingsController').apiOnly().as('settings')
    Route.resource('forms', 'FormsController').apiOnly().as('forms')
    Route.resource('lots', 'LotsController').apiOnly().as('lots')
    Route.resource('tags', 'TagsController').apiOnly().as('tags')
    Route.resource('invoices', 'InvoicesController').apiOnly().as('invoices')
    Route.resource('form-templates', 'FormTemplatesController').apiOnly().as('forms.templates')

    // Feature Flag Routes
    Route.group(() => {
      Route.resource('feature-flags', 'FeatureFlagsController').apiOnly().as('feature-flags')
      Route.post('feature-flags/:code/enable', 'FeatureFlagsController.enable').as(
        'feature-flags.enable'
      )
      Route.post('feature-flags/:code/disable', 'FeatureFlagsController.disable').as(
        'feature-flags.disable'
      )
    }).middleware('auth:admin')

    // Charge Configuration Routes
    Route.group(() => {
      Route.resource('charge-configurations', 'ChargeConfigurationsController')
        .apiOnly()
        .as('charge-configurations')
      Route.post(
        'charge-configurations/apply-to-order',
        'ChargeConfigurationsController.applyToOrder'
      ).as('charge-configurations.apply-to-order')
    }).as('charge-configurations')

    // Customer Subscription Routes
    Route.group(() => {
      Route.resource('customer-subscriptions', 'CustomerSubscriptionsController')
        .apiOnly()
        .except(['update', 'destroy'])
        .as('customer-subscriptions')
      Route.get('customer-subscriptions/current', 'CustomerSubscriptionsController.current').as(
        'customer-subscriptions.current'
      )
      Route.post('customer-subscriptions/:id/cancel', 'CustomerSubscriptionsController.cancel').as(
        'customer-subscriptions.cancel'
      )
      Route.get('customer-subscriptions/usage', 'CustomerSubscriptionsController.usage').as(
        'customer-subscriptions.usage'
      )
    }).as('customer-subscriptions')

    // Branch routes
    Route.group(() => {
      Route.resource('branches', 'BranchesController').apiOnly().as('branches')
      Route.resource('branches.staff', 'BranchStaffController')
        .apiOnly()
        .except(['destroy'])
        .as('branches.staff')
      Route.put(
        'branches/:branch/staff/:staff/replace-role',
        'BranchStaffController.replaceRole'
      ).as('branches.staff.replace-role')
      Route.shallowResource('branches.customers', 'BranchCustomersController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.customers')
      Route.shallowResource('branches.campaigns', 'BranchCampaignsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.campaigns')
      Route.shallowResource('branches.products', 'BranchProductsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.products')
      Route.shallowResource('branches.orders', 'BranchOrdersController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.orders')
      Route.shallowResource('branches.temp-orders', 'BranchTempOrdersController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.temp-orders')
      Route.shallowResource('branches.payments', 'BranchPaymentsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.payments')
      Route.shallowResource('branches.groups', 'BranchGroupsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.groups')
      Route.shallowResource('branches.sections', 'BranchSectionsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.sections')
      Route.shallowResource('branches.lots', 'BranchLotsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('branches.lots')
    }).as('branches')

    // Device routes
    Route.group(() => {
      Route.resource('devices', 'DevicesController').apiOnly().as('devices')
    }).as('devices')

    // Group routes
    Route.group(() => {
      Route.resource('groups', 'GroupsController').apiOnly().as('resource')
      Route.shallowResource('groups.members', 'GroupMembersController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('groups.members')
    }).as('groups')

    // Message routes
    Route.group(() => {
      Route.resource('messages', 'MessagesController').apiOnly().as('resource')

      // Message Actions routes
      Route.group(() => {
        Route.resource('actions', 'MessageActionsController')
          .apiOnly()
          .except(['index', 'store'])
          .as('actions')
        Route.get('actions', 'MessageActionsController.index').as('messages.actions')
        Route.post('actions', 'MessageActionsController.store').as('messages.actions.store')

        // Action Responses routes
        Route.group(() => {
          Route.resource('responses', 'ActionResponsesController')
            .apiOnly()
            .except(['index', 'store'])
            .as('responses')
          Route.get('responses', 'ActionResponsesController.index').as('messages.actions.responses')
          Route.post('responses', 'ActionResponsesController.store').as(
            'messages.actions.responses.store'
          )
        }).prefix('actions/:actionId')
      }).prefix('messages/:messageId')

      // Action Types routes
      Route.resource('action-types', 'ActionTypesController').apiOnly().as('action-types')
      Route.post('action-types/:id/validate-config', 'ActionTypesController.validateConfig').as(
        'action-types.validate-config'
      )
    }).as('messages')

    // Message routes
    Route.group(() => {
      Route.resource('message-templates', 'MessageTemplatesController').apiOnly().as('resource')
    }).as('message-templates')

    // Partners routes
    Route.group(() => {
      Route.resource('partners', 'PartnersController').apiOnly().as('resource')
    }).as('partners')

    // Payment routes
    Route.group(() => {
      Route.resource('payments', 'PaymentsController').apiOnly().as('resource')
    }).as('payments')

    // Product routes
    Route.group(() => {
      Route.resource('products', 'ProductsController').apiOnly().as('products')

      // Product service options routes (nested)
      Route.group(() => {
        Route.get('/', 'ProductServiceOptionsController.index')
        Route.post('/', 'ProductServiceOptionsController.store')
        Route.get('/defaults', 'ProductServiceOptionsController.getDefaults')
        Route.get('/statistics', 'ProductServiceOptionsController.getStatistics')
        Route.post('/check-conflicts', 'ProductServiceOptionsController.checkConflicts')
        Route.post('/sort-orders', 'ProductServiceOptionsController.updateSortOrders')
        Route.get('/type/:type', 'ProductServiceOptionsController.getByType')
        Route.get('/:id', 'ProductServiceOptionsController.show')
        Route.put('/:id', 'ProductServiceOptionsController.update')
        Route.delete('/:id', 'ProductServiceOptionsController.destroy')
      }).prefix('/products/:productId/service-options')
      Route.resource('product-templates', 'ProductTemplatesController')
        .apiOnly()
        .as('products.templates')
      Route.resource('products.forms', 'ProductFormsController').apiOnly().as('products.forms')
      Route.resource('product-types', 'ProductTypesController').apiOnly().as('product_types')
      Route.resource('product-categories', 'ProductCategoriesController')
        .apiOnly()
        .as('product_categories')
      Route.shallowResource('product-categories.products', 'ProductCategoryProductsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('product_category_products')
      Route.shallowResource('product-types.categories', 'ProductTypeCategoriesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('product_type_categories')
      Route.get(
        'product-types/:productTypeId/categories/:categoryId/products',
        'ProductTypeCategoryProductsController.index'
      ).as('product_type_category_products')
      Route.resource('modifier-options', 'ModifierOptionsController')
        .apiOnly()
        .as('modifier-options')

      // Product Fulfillment Settings Routes
      Route.resource('products.fulfillment-settings', 'ProductFulfillmentSettingsController')
        .apiOnly()
        .as('products.fulfillment-settings')

      // Additional fulfillment routes
      Route.get(
        'products/:product_id/fulfillment',
        'ProductFulfillmentSettingsController.showByProduct'
      ).as('products.fulfillment')
    }).as('products')

    // Ratings routes
    Route.group(() => {
      Route.resource('customer-ratings', 'CustomerRatingsController').apiOnly().as('customers')
      Route.resource('product-ratings', 'ProductRatingsController').apiOnly().as('products')
      Route.resource('staff-ratings', 'StaffRatingsController').apiOnly().as('staff')
      Route.resource('vendor-ratings', 'VendorRatingsController').apiOnly().as('vendors')
    }).prefix('/ratings').as('ratings')

    // Specialities routes
    Route.group(() => {
      Route.resource('specialities', 'SpecialitiesController').apiOnly().as('resource')
      Route.resource('specialities.vendors', 'SpecialityVendorsController').apiOnly().as('vendors')
    }).as('specialities')

    // Section routes
    Route.group(() => {
      Route.resource('sections', 'SectionsController').apiOnly().as('resource')
      Route.shallowResource('sections.lots', 'SectionLotsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('sections.lots')
    }).as('sections')

    // User routes
    Route.group(() => {
      Route.resource('users', 'UsersController').apiOnly().as('users')
      Route.shallowResource('users.addresses', 'UserAddressesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('users.addresses')
      Route.shallowResource('users.vendors', 'UserVendorsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('vendors')
      Route.shallowResource('users.lots', 'UserLotsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('lots')
      Route.shallowResource('users.orders', 'UserOrdersController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('orders')
      Route.shallowResource('users.invoices', 'UserInvoicesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('invoices')
      Route.shallowResource('users.wishlist', 'UserWishlistsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('wishlist')
      // Route.shallowResource('users.groups', 'CustomerGroupsController').as('users.groups')
    }).as('users')

    // Service routes
    Route.group(() => {
      Route.resource('services', 'ServicesController').apiOnly().as('services')
      Route.shallowResource('services.products', 'ServiceProductsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('services.products')

      Route.shallowResource('services.vendors', 'ServiceVendorsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('services.vendors')

      Route.shallowResource('services.specialities', 'ServiceSpecialitiesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('services.specialities')

      Route.shallowResource('services.vendor-types', 'ServiceVendorTypesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('services.vendor_types')

      Route.shallowResource('services.vendor-categories', 'ServiceVendorCategoriesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('services.vendor_categories')

      Route.resource('services.product-types', 'ServiceProductTypesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('services.product.types')

      Route.resource('services.product-categories', 'ServiceProductCategoriesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('services.product.categories')

      // Service Actions Routes
      Route.group(() => {
        Route.get('/', 'ServiceActionsController.index').as('index')
        Route.post('/', 'ServiceActionsController.store').as('store')
        Route.put('/:id', 'ServiceActionsController.update').as('update')
        Route.delete('/:id', 'ServiceActionsController.destroy').as('destroy')
      })
        .prefix('services/:service/actions')
        .as('services.actions')

      // Service Options Routes
      Route.group(() => {
        // Duration management routes
        Route.group(() => {
          Route.get('/', 'DurationsController.index').as('index')
          Route.post('/', 'DurationsController.store').as('store')
          Route.get('/categories', 'DurationsController.categories').as('categories')
          Route.post('/template', 'DurationsController.createFromTemplate').as('template')
          Route.post('/validate', 'DurationsController.validate').as('validate')
          Route.get('/:id', 'DurationsController.show').as('show')
          Route.put('/:id', 'DurationsController.update').as('update')
          Route.delete('/:id', 'DurationsController.destroy').as('destroy')
        }).prefix('/durations').as('durations')

        // Service configuration routes
        Route.group(() => {
          Route.get('/', 'ServiceConfigurationsController.index').as('index')
          Route.post('/', 'ServiceConfigurationsController.store').as('store')
          Route.get('/search', 'ServiceConfigurationsController.search').as('search')
          Route.post('/template', 'ServiceConfigurationsController.createFromTemplate').as('template')
          Route.get('/:id', 'ServiceConfigurationsController.show').as('show')
          Route.put('/:id', 'ServiceConfigurationsController.update').as('update')
          Route.delete('/:id', 'ServiceConfigurationsController.destroy').as('destroy')
          Route.post('/:id/clone', 'ServiceConfigurationsController.clone').as('clone')
          Route.get('/:id/validate', 'ServiceConfigurationsController.validate').as('validate')
          Route.get('/:id/statistics', 'ServiceConfigurationsController.statistics').as('statistics')

          // Configuration options routes (nested)
          Route.group(() => {
            Route.get('/', 'ServiceConfigurationOptionsController.index').as('index')
            Route.post('/', 'ServiceConfigurationOptionsController.store').as('store')
            Route.get('/types', 'ServiceConfigurationOptionsController.getTypes').as('types')
            Route.get('/defaults', 'ServiceConfigurationOptionsController.getDefaults').as('defaults')
            Route.post('/template', 'ServiceConfigurationOptionsController.createFromTemplate').as('template')
            Route.post('/sort-orders', 'ServiceConfigurationOptionsController.updateSortOrders').as('sort-orders')
            Route.get('/type/:type', 'ServiceConfigurationOptionsController.getByType').as('by-type')
            Route.get('/:id', 'ServiceConfigurationOptionsController.show').as('show')
            Route.put('/:id', 'ServiceConfigurationOptionsController.update').as('update')
            Route.delete('/:id', 'ServiceConfigurationOptionsController.destroy').as('destroy')
          }).prefix('/:configurationId/options').as('options')

        }).prefix('/service-configurations').as('service-configurations')

        // Option resolution routes
        Route.group(() => {
          Route.get('/product/:productId', async ({ params, response }) => {
            const OptionResolutionService = (await import('App/Services/OptionResolutionService')).default
            try {
              const options = await OptionResolutionService.resolveProductOptions(params.productId)
              return response.json({ success: true, data: options })
            } catch (error) {
              return response.status(404).json({ success: false, message: 'Product not found', error: error.message })
            }
          }).as('product-options')
          Route.delete('/cache/product/:productId', async ({ params, response }) => {
            const OptionResolutionService = (await import('App/Services/OptionResolutionService')).default
            await OptionResolutionService.clearProductCache(params.productId)
            return response.json({ success: true, message: 'Cache cleared' })
          }).as('clear-product-cache')
          Route.delete('/cache/all', async ({ response }) => {
            const OptionResolutionService = (await import('App/Services/OptionResolutionService')).default
            await OptionResolutionService.clearAllCache()
            return response.json({ success: true, message: 'All cache cleared' })
          }).as('clear-all-cache')
        }).prefix('/options').as('option-resolution')

        // Direct service options routes
        Route.group(() => {
          Route.get('/', 'ServiceOptionsController.index').as('index')
          Route.post('/', 'ServiceOptionsController.store').as('store')
          Route.get('/search', 'ServiceOptionsController.search').as('search')
          Route.get('/types', 'ServiceOptionsController.getTypes').as('types')
          Route.get('/global', 'ServiceOptionsController.getGlobalOptions').as('global')
          Route.post('/template', 'ServiceOptionsController.createFromTemplate').as('template')
          Route.get('/type/:type', 'ServiceOptionsController.getByType').as('by-type')
          Route.get('/vendor/:vendorId', 'ServiceOptionsController.getVendorOptions').as('vendor-options')
          Route.get('/:id', 'ServiceOptionsController.show').as('show')
          Route.put('/:id', 'ServiceOptionsController.update').as('update')
          Route.delete('/:id', 'ServiceOptionsController.destroy').as('destroy')
        }).prefix('/direct-options').as('direct-options')

      }).prefix('/service-options').as('service-options')

    }).as('services')

    // Booking routes
    Route.group(() => {
      // Booking availability and slot management
      Route.group(() => {
        Route.post('/available-slots', 'BookingAvailabilityController.getAvailableSlots').as('available-slots')
        Route.post('/multi-day-availability', 'BookingAvailabilityController.getMultiDayAvailability').as('multi-day-availability')
        Route.post('/reserve-slot', 'BookingAvailabilityController.reserveSlot').as('reserve-slot')
        Route.post('/validate-booking', 'BookingAvailabilityController.validateBooking').as('validate-booking')
      }).prefix('/availability').as('availability')

      // Calendar views
      Route.group(() => {
        Route.get('/branch/:branchId', 'BookingCalendarController.getBranchCalendar').as('branch-calendar')
        Route.get('/customer/:customerId', 'BookingCalendarController.getCustomerCalendar').as('customer-calendar')
        Route.get('/branch/:branchId/stats', 'BookingCalendarController.getCalendarStats').as('calendar-stats')
      }).prefix('/calendar').as('calendar')

      // Booking management
      Route.resource('bookings', 'BookingsController').apiOnly().as('bookings')
      Route.post('/bookings/:id/confirm', 'BookingsController.confirm').as('bookings.confirm')
      Route.post('/bookings/:id/cancel', 'BookingsController.cancel').as('bookings.cancel')
      Route.post('/bookings/:id/start-service', 'BookingsController.startService').as('bookings.start-service')
      Route.post('/bookings/:id/complete-service', 'BookingsController.completeService').as('bookings.complete-service')
      Route.post('/bookings/:id/mark-no-show', 'BookingsController.markNoShow').as('bookings.mark-no-show')

      // Integrated booking-order management
      Route.group(() => {
        Route.post('/create', 'BookingOrderController.createBookingOrder').as('create')
        Route.put('/:bookingId/update', 'BookingOrderController.updateBookingOrder').as('update')
        Route.post('/:bookingId/cancel', 'BookingOrderController.cancelBookingOrder').as('cancel')
        Route.post('/:bookingId/confirm', 'BookingOrderController.confirmBookingOrder').as('confirm')
        Route.get('/:bookingId/details', 'BookingOrderController.getBookingWithOrder').as('details')
        Route.get('/customer/:customerId/history', 'BookingOrderController.getCustomerBookingHistory').as('customer-history')
        Route.get('/vendor/:vendorId/summary', 'BookingOrderController.getVendorBookingSummary').as('vendor-summary')
      }).prefix('/orders').as('orders')

    }).prefix('/bookings').as('bookings')

    //  Subscription routes
    Route.group(() => {
      Route.get('/', 'SubscriptionPlansController.index').as('index')
      Route.post('/', 'SubscriptionPlansController.store').as('store')
      Route.get('/:id', 'SubscriptionPlansController.show').as('show')
      Route.put('/:id', 'SubscriptionPlansController.update').as('update')
      Route.delete('/:id', 'SubscriptionPlansController.destroy').as('destroy')
    })
      .prefix('subscription-plans')
      .as('subscriptions')

    // Task routes
    Route.group(() => {
      Route.resource('tasks', 'TasksController').apiOnly().as('tasks')
      Route.resource('tasks.services', 'TaskServicesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('tasks.services')
      Route.resource('tasks.vendors', 'TaskVendorsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('tasks.vendors')
      Route.resource('tasks.products', 'TaskProductsController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('tasks.products')
    }).as('tasks')

    // Notification Tracking Routes
    Route.group(() => {
      Route.get('/stats', 'NotificationTrackingController.getStats').as('stats')
      Route.get('/delivery-rates', 'NotificationTrackingController.getDeliveryRates').as(
        'delivery-rates'
      )
    })
      .prefix('/notification-tracking')
      .middleware(['auth', 'admin'])
      .as('notification-tracking')

    // Role routes
    Route.group(() => {
      Route.resource('roles', 'RolesController').apiOnly().as('roles')
    }).as('roles')

    // Staff routes
    Route.group(() => {
      Route.resource('staff', 'StaffController').apiOnly().as('staff')
    }).as('staff')

    // Vendor routes
    Route.group(() => {
      Route.resource('vendors', 'VendorsController').apiOnly().as('vendors')
      Route.resource('vendor-types', 'VendorTypesController').as('vendor.types')
      Route.shallowResource('vendors.branches', 'VendorBranchesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('vendors.branches')
      Route.resource('vendors.customers', 'VendorCustomersController')
        .apiOnly()
        .as('vendors.customers')
      Route.resource('vendors.tasks', 'VendorTasksController')
        .apiOnly()
        .except(['destroy'])
        .as('vendors.tasks')
      Route.delete('vendors/:vendorId/tasks/:taskId', 'VendorTasksController.detach').as(
        'vendors.tasks.detach'
      )
      Route.resource('vendors.services', 'VendorServicesController')
        .apiOnly()
        .as('vendors.services')
      Route.get(
        'vendors/:vendorId/services/:serviceId/products',
        'VendorServiceProductsController.index'
      ).as('vendors.services.products')
      Route.put('vendors/:id/category', 'VendorsController.updateCategory').as(
        'vendors.updateCategory'
      )
      Route.post('/vendors/:vendorId/bulk-products', 'VendorBulkProductsController.store').as(
        'vendors.bulk-products.store'
      )
      Route.get('/bulk-products/jobs/:jobId', 'VendorBulkProductsController.getJobStatus').as(
        'vendors.bulk-products.job-status'
      )
      Route.resource('vendors/:vendorId/products', 'VendorProductsController')
        .apiOnly()
        .except(['show', 'destroy'])
        .as('vendors.products')
      Route.resource('vendors/:vendorId/modifier-options', 'VendorModifierOptionsController')
        .apiOnly()
        .as('vendors.modifier-options')
      Route.resource('vendors/:vendorId/packaging-options', 'VendorPackagingOptionsController')
        .apiOnly()
        .as('vendors.packaging-options')
      Route.resource('vendors.specialities', 'VendorSpecialitiesController')
        .apiOnly()
        .except(['show', 'update'])
        .as('vendors.specialities')
      Route.shallowResource('vendors.orders', 'VendorOrdersController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('vendors.orders')
      Route.resource('vendor-types.categories', 'VendorTypeCategoriesController')
        .apiOnly()
        .except(['show', 'update', 'destroy'])
        .as('vendor-types.categories')
      Route.resource('vendor-categories', 'VendorCategoriesController')
        .apiOnly()
        .as('vendor.categories')
      Route.resource('vendor-categories.vendors', 'VendorCategoryVendorsController')
        .apiOnly()
        .as('vendor.categories.vendors')
      Route.post(
        '/vendors/:vendorId/products/bulk-upload',
        'VendorBulkProductsController.store'
      ).as('vendors.products.bulk-upload')

      // Vendor Service Areas Routes
      Route.group(() => {
        Route.get('/', 'VendorServiceAreasController.index').as('index')
        Route.post('/', 'VendorServiceAreasController.store').as('store')
        Route.get('/check', 'VendorServiceAreasController.check').as('check')
        Route.get('/:serviceAreaId', 'VendorServiceAreasController.show').as('show')
        Route.put('/:serviceAreaId', 'VendorServiceAreasController.update').as('update')
        Route.delete('/:serviceAreaId', 'VendorServiceAreasController.destroy').as('destroy')
      })
        .prefix('vendors/:vendorId/service-areas')
        .as('vendors.service-areas')

      // Vendor Delivery Settings Routes
      Route.group(() => {
        Route.get('/', 'VendorDeliverySettingsController.show').as('show')
        Route.put('/preferences', 'VendorDeliverySettingsController.updatePreferences').as(
          'updatePreferences'
        )
        Route.put('/availability', 'VendorDeliverySettingsController.updateAvailability').as(
          'updateAvailability'
        )
        Route.put('/pricing', 'VendorDeliverySettingsController.updatePricing').as('updatePricing')
        Route.put('/verification', 'VendorDeliverySettingsController.updateVerification')
          .middleware('auth:admin')
          .as('updateVerification')
      })
        .prefix('vendors/:vendorId/delivery-settings')
        .as('vendors.delivery-settings')
    }).as('vendors')

    // Wishlist routes
    Route.group(() => {
      Route.resource('wishlists', 'WishlistsController').apiOnly().as('wishlists')
    }).as('wishlists')

    Route.get('/search', 'UserSearchController.search').as('search')
  })
    .as('v1')
    .prefix('v1')
}).middleware('auth')

Route.group(() => {
  Route.get('getcart', 'CartController.getCart').middleware('auth')
  Route.post('postcart', 'CartController.postCart').middleware('auth')

  // Cart fulfillment routes
  Route.post('fulfillment-options', 'CartFulfillmentController.getFulfillmentOptions').middleware(
    'auth'
  )
  Route.post('validate-fulfillment', 'CartFulfillmentController.validateFulfillment').middleware(
    'auth'
  )
  Route.post('delivery-options', 'CartFulfillmentController.getDeliveryOptionsForCart').middleware(
    'auth'
  )
}).prefix('v1/cart')

Route.group(() => {
  Route.post('multi-payments', 'PaymentsController.multiPayment').middleware('auth')
  Route.get('get-payments', 'PaymentsController.getMultiPayment').middleware('auth')
  Route.get('status/:id', 'PaymentsController.checkStatus').middleware('auth') // Optimized status check
  Route.get('test-polling', 'PaymentsController.testStatusPolling').middleware('auth') // Test status polling
  Route.get('debug/:id', 'PaymentsController.debugPayment').middleware('auth') // Debug payment
  Route.patch('manual-update/:id', 'PaymentsController.manualUpdate').middleware('auth') // Manual update for testing
}).prefix('v1/pay')

Route.group(() => {
  Route.post('pop-up', 'NotificationsController.doPopUp').middleware('auth')
}).prefix('v1/notif')

Route.group(() => {
  Route.get('bystaffid', 'BranchOrdersController.ordersbystaffid').middleware('auth')
}).prefix('v1/getorders')

Route.group(() => {
  Route.get('bystaffid', 'BranchTempOrdersController.ordersbystaffid').middleware('auth')
}).prefix('v1/gettemporders')

// M-Pesa callback routes - NO AUTHENTICATION REQUIRED
Route.group(() => {
  Route.post('validate', 'PaymentsController.validate')
  Route.post('confirm', 'PaymentsController.confirm')
  Route.post('timeout', 'PaymentsController.timeout')
  Route.post('result', 'PaymentsController.result')
  Route.get('health', 'PaymentsController.mpesaHealth') // Health check endpoint
})
  .prefix('v1/mpsa/e59ed6a68b83')
  .middleware('mpesaCallback')

// M-Pesa Migration Testing Routes - REQUIRES AUTHENTICATION
Route.group(() => {
  Route.get('compare', 'MpesaMigrationController.compare')
  Route.get('status', 'MpesaMigrationController.migrationStatus')
  Route.post('test-official-stk', 'MpesaMigrationController.testOfficialSTKPush')
  Route.post('test-current-stk', 'MpesaMigrationController.testCurrentSTKPush')
  Route.post('register-c2b', 'MpesaMigrationController.registerC2BUrls')
  Route.get('check-balance', 'MpesaMigrationController.checkBalance')
})
  .prefix('v1/mpesa-migration')
  .middleware('auth')

// M-Pesa Callback Debugging Routes - REQUIRES AUTHENTICATION
Route.group(() => {
  Route.get('test-urls', 'MpesaCallbackDebugController.testCallbackUrls')
  Route.post('simulate', 'MpesaCallbackDebugController.simulateCallback')
  Route.get('config', 'MpesaCallbackDebugController.getCallbackConfig')
  Route.get('monitor', 'MpesaCallbackDebugController.monitorCallbacks')
})
  .prefix('v1/mpesa-debug')
  .middleware('auth')

Route.any('v1/payment/:id', 'PaymentIpnController').as('v1.payments.ipn')
