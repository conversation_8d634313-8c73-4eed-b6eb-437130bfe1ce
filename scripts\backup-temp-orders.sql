-- Temp Orders Backup Script
-- Date: 2025-06-19
-- Purpose: Create backup before migrating to unified order system

-- 1. Create backup table
CREATE TABLE IF NOT EXISTS temp_orders_backup AS 
SELECT * FROM temp_orders;

-- 2. Verify backup integrity
DO $$
DECLARE
    original_count INTEGER;
    backup_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO original_count FROM temp_orders;
    SELECT COUNT(*) INTO backup_count FROM temp_orders_backup;
    
    IF original_count = backup_count THEN
        RAISE NOTICE 'Backup successful: % records backed up', backup_count;
    ELSE
        RAISE EXCEPTION 'Backup failed: original=%, backup=%', original_count, backup_count;
    END IF;
END $$;

-- 3. Create backup metadata table
CREATE TABLE IF NOT EXISTS temp_orders_backup_metadata (
    backup_date TIMESTAMP DEFAULT NOW(),
    original_count INTEGER,
    backup_count INTEGER,
    oldest_record TIMESTAMP,
    newest_record TIMESTAMP,
    migration_status TEXT DEFAULT 'backup_created'
);

-- 4. Insert backup metadata
INSERT INTO temp_orders_backup_metadata (
    original_count,
    backup_count,
    oldest_record,
    newest_record
)
SELECT 
    (SELECT COUNT(*) FROM temp_orders),
    (SELECT COUNT(*) FROM temp_orders_backup),
    (SELECT MIN(created_at) FROM temp_orders),
    (SELECT MAX(created_at) FROM temp_orders);

-- 5. Create indexes on backup table for faster queries
CREATE INDEX IF NOT EXISTS idx_temp_orders_backup_status ON temp_orders_backup(status);
CREATE INDEX IF NOT EXISTS idx_temp_orders_backup_created_at ON temp_orders_backup(created_at);
CREATE INDEX IF NOT EXISTS idx_temp_orders_backup_vendor_id ON temp_orders_backup(vendor_id);
CREATE INDEX IF NOT EXISTS idx_temp_orders_backup_branch_id ON temp_orders_backup(branch_id);

-- 6. Verify data integrity
SELECT 
    'temp_orders_backup' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_count,
    MIN(created_at) as oldest_record,
    MAX(created_at) as newest_record,
    COUNT(CASE WHEN meta IS NOT NULL THEN 1 END) as records_with_meta,
    COUNT(CASE WHEN items IS NOT NULL THEN 1 END) as records_with_items
FROM temp_orders_backup;

-- 7. Sample data verification
SELECT 
    'Sample backup data' as description,
    id,
    status,
    vendor_id,
    branch_id,
    created_at
FROM temp_orders_backup 
ORDER BY created_at DESC 
LIMIT 5;

COMMIT;
