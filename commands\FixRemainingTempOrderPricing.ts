import { BaseCommand } from '@adonisjs/core/build/standalone'
import Database from '@ioc:Adonis/Lucid/Database'

export default class FixRemainingTempOrderPricing extends BaseCommand {
  public static commandName = 'temp-orders:fix-remaining-pricing'
  public static description = 'Fix pricing for remaining temp orders without pricing structure'

  public static settings = {
    loadApp: true,
    stayAlive: false,
  }

  public async run() {
    this.logger.info('🔄 Starting to fix remaining temp orders without pricing...')

    try {
      // Count orders without pricing
      const countResult = await Database.rawQuery(`
        SELECT COUNT(*) as count
        FROM orders 
        WHERE status = 'Pending' 
        AND meta->>'pricing' IS NULL
      `)

      const remainingCount = parseInt(countResult.rows[0].count)
      this.logger.info(`📊 Found ${remainingCount} orders without pricing`)

      if (remainingCount === 0) {
        this.logger.info('✅ All temp orders already have pricing!')
        return
      }

      // Simple update with default pricing for all remaining orders
      this.logger.info('🔄 Applying default pricing to all remaining orders...')
      
      const updateResult = await Database.rawQuery(`
        UPDATE orders
        SET meta = meta::jsonb || jsonb_build_object(
          'pricing', jsonb_build_object(
            'subtotal', 0,
            'modifiersTotal', 0,
            'chargesTotal', 0,
            'total', 0,
            'invoiceAmount', 0,
            'currency', 'KES'
          )
        )
        WHERE status = 'Pending'
        AND meta->>'pricing' IS NULL
      `)

      this.logger.info(`✅ Updated ${updateResult.rowCount} orders with default pricing`)

      // Verify completion
      const finalCountResult = await Database.rawQuery(`
        SELECT COUNT(*) as count
        FROM orders 
        WHERE status = 'Pending' 
        AND meta->>'pricing' IS NULL
      `)

      const finalCount = parseInt(finalCountResult.rows[0].count)
      
      if (finalCount === 0) {
        this.logger.info('🎉 SUCCESS: All temp orders now have pricing structure!')
      } else {
        this.logger.warn(`⚠️ ${finalCount} orders still need pricing`)
      }

      // Show final statistics
      const statsResult = await Database.rawQuery(`
        SELECT 
          COUNT(*) as total_temp_orders,
          COUNT(CASE WHEN meta->>'pricing' IS NOT NULL THEN 1 END) as with_pricing,
          ROUND(COUNT(CASE WHEN meta->>'pricing' IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 2) as percentage
        FROM orders WHERE status = 'Pending'
      `)

      const stats = statsResult.rows[0]
      this.logger.info(`📊 Final Statistics:`)
      this.logger.info(`   Total temp orders: ${stats.total_temp_orders}`)
      this.logger.info(`   With pricing: ${stats.with_pricing}`)
      this.logger.info(`   Coverage: ${stats.percentage}%`)

    } catch (error) {
      this.logger.error('❌ Error fixing remaining temp order pricing:', error)
      throw error
    }
  }
}
