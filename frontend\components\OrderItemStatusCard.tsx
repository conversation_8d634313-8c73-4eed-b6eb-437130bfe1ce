import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { 
  Clock, 
  User, 
  AlertTriangle, 
  Timer, 
  MessageSquare,
  MoreHorizontal,
  Play,
  CheckCircle
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import OrderItemStatusManager, { OrderItemData } from './OrderItemStatusManager'

interface OrderItemStatusCardProps {
  orderItem: OrderItemData
  onStatusChange?: (itemId: number, newStatus: string, metadata?: Record<string, any>) => void
  onNotesUpdate?: (itemId: number, notes: string) => void
  showQuickActions?: boolean
  showModifiers?: boolean
  className?: string
}

export const OrderItemStatusCard: React.FC<OrderItemStatusCardProps> = ({
  orderItem,
  onStatusChange,
  onNotesUpdate,
  showQuickActions = true,
  showModifiers = true,
  className = ''
}) => {
  const [showDetailDialog, setShowDetailDialog] = useState(false)

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'secondary'
      case 'preparing': return 'warning'
      case 'ready': return 'success'
      case 'served': return 'default'
      default: return 'default'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: number, isOverdue: boolean) => {
    if (isOverdue) return 'destructive'
    if (priority === 1) return 'destructive'
    if (priority === 2) return 'warning'
    return 'default'
  }

  // Calculate preparation progress
  const getPreparationProgress = () => {
    if (!orderItem.preparation_started_at || !orderItem.estimated_preparation_time) return 0
    
    const startTime = new Date(orderItem.preparation_started_at)
    const now = new Date()
    const elapsedMinutes = (now.getTime() - startTime.getTime()) / (1000 * 60)
    
    return Math.min((elapsedMinutes / orderItem.estimated_preparation_time) * 100, 100)
  }

  // Get next logical status
  const getNextStatus = () => {
    switch (orderItem.status) {
      case 'pending': return 'preparing'
      case 'preparing': return 'ready'
      case 'ready': return 'served'
      default: return null
    }
  }

  // Handle quick status update
  const handleQuickStatusUpdate = (newStatus: string) => {
    onStatusChange?.(orderItem.id, newStatus, {
      updated_via: 'quick_action',
      timestamp: new Date().toISOString()
    })
  }

  return (
    <>
      <Card className={`hover:shadow-md transition-shadow cursor-pointer ${className}`}>
        <CardContent className="p-4">
          {/* Header */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm truncate">{orderItem.product_name}</h4>
              <p className="text-xs text-muted-foreground">
                Order #{orderItem.order_id} • Qty: {orderItem.quantity}
              </p>
            </div>
            <div className="flex gap-1 ml-2">
              <Badge variant={getPriorityColor(orderItem.priority_level, orderItem.is_overdue)}>
                {orderItem.is_overdue 
                  ? `${orderItem.overdue_minutes}m overdue` 
                  : `P${orderItem.priority_level}`
                }
              </Badge>
              <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="w-3 h-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <OrderItemStatusManager
                    orderItemId={orderItem.id}
                    orderId={orderItem.order_id}
                    initialData={orderItem}
                    onStatusChange={onStatusChange}
                    onNotesUpdate={onNotesUpdate}
                    showModifiers={showModifiers}
                  />
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Status Badge */}
          <div className="flex items-center justify-between mb-3">
            <Badge variant={getStatusColor(orderItem.status)} className="text-xs">
              {orderItem.status.charAt(0).toUpperCase() + orderItem.status.slice(1)}
            </Badge>
            
            {orderItem.estimated_preparation_time && orderItem.status === 'preparing' && (
              <div className="text-xs text-muted-foreground">
                <Timer className="w-3 h-3 inline mr-1" />
                {orderItem.estimated_preparation_time}m est.
              </div>
            )}
          </div>

          {/* Progress Bar for Preparing Items */}
          {orderItem.status === 'preparing' && orderItem.estimated_preparation_time && (
            <div className="mb-3">
              <Progress value={getPreparationProgress()} className="h-1" />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>In progress</span>
                <span>{Math.round(getPreparationProgress())}%</span>
              </div>
            </div>
          )}

          {/* Special Instructions */}
          {orderItem.special_instructions && (
            <div className="mb-3 p-2 bg-orange-50 border border-orange-200 rounded text-xs">
              <AlertTriangle className="w-3 h-3 inline mr-1 text-orange-600" />
              <span className="text-orange-800">{orderItem.special_instructions}</span>
            </div>
          )}

          {/* Staff Assignment */}
          {orderItem.assigned_staff_name && (
            <div className="flex items-center gap-1 mb-3 text-xs">
              <User className="w-3 h-3 text-muted-foreground" />
              <span className="text-muted-foreground">Assigned to:</span>
              <span className="font-medium">{orderItem.assigned_staff_name}</span>
            </div>
          )}

          {/* Preparation Time */}
          {orderItem.preparation_started_at && (
            <div className="flex items-center gap-1 mb-3 text-xs">
              <Clock className="w-3 h-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                Started {formatDistanceToNow(new Date(orderItem.preparation_started_at))} ago
              </span>
            </div>
          )}

          {/* Notes Indicator */}
          {orderItem.preparation_notes && (
            <div className="flex items-center gap-1 mb-3 text-xs">
              <MessageSquare className="w-3 h-3 text-blue-500" />
              <span className="text-blue-600">Has notes</span>
            </div>
          )}

          {/* Modifiers */}
          {showModifiers && orderItem.modifiers.length > 0 && (
            <div className="mb-3">
              <div className="flex flex-wrap gap-1">
                {orderItem.modifiers.slice(0, 3).map(modifier => (
                  <Badge
                    key={modifier.id}
                    variant={getStatusColor(modifier.status)}
                    className="text-xs"
                  >
                    {modifier.name}
                  </Badge>
                ))}
                {orderItem.modifiers.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{orderItem.modifiers.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          {showQuickActions && (
            <div className="flex gap-1">
              {orderItem.status === 'pending' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickStatusUpdate('preparing')}
                  className="flex-1 text-xs"
                >
                  <Play className="w-3 h-3 mr-1" />
                  Start
                </Button>
              )}
              
              {orderItem.status === 'preparing' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickStatusUpdate('ready')}
                  className="flex-1 text-xs"
                >
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Ready
                </Button>
              )}
              
              {orderItem.status === 'ready' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleQuickStatusUpdate('served')}
                  className="flex-1 text-xs"
                >
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Served
                </Button>
              )}

              {getNextStatus() && (
                <Button
                  size="sm"
                  onClick={() => handleQuickStatusUpdate(getNextStatus()!)}
                  className="flex-1 text-xs"
                >
                  {getNextStatus() === 'preparing' && 'Start'}
                  {getNextStatus() === 'ready' && 'Ready'}
                  {getNextStatus() === 'served' && 'Served'}
                </Button>
              )}

              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowDetailDialog(true)}
                className="text-xs"
              >
                Details
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  )
}

export default OrderItemStatusCard
