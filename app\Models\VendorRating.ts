import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import VendorRatingFilter from './Filters/VendorRatingFilter'
import User from './User'
import Vendor from './Vendor'

export default class VendorRating extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => VendorRatingFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public customerId: string

  @column()
  public vendorId: string

  @column()
  public name: string

  @column()
  public points: number

  @column()
  public comment: string

  @column()
  public meta: JSON

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User, {
    foreignKey: 'customerId',
  })
  public customer: BelongsTo<typeof User>

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>
}
