import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import TagFilter from './Filters/TagFilter'

export default class Tag extends compose(BaseModel, Filterable) {
  public static $filter = () => TagFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public details: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
