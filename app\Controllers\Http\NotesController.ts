import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Note from '../../Models/Note'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Note management
 * @version 1.0.0
 * @description Note management for the application
 */
export default class NotesController {
  /**
   * @index
   * @summary List all notes
   * @description List all notes, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const noteQuery = Note.filter(filters)

    return await noteQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a note
   * @description Create a note with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Note>
   */
  public async store({ request, response }: HttpContextContract) {
    const { content, branchId, staffId, notes = null } = request.all()

    if (notes) {
      notes.map(async (n) => {
        await Note.create({
          content: n.content || 'N/A',
          meta: n.meta,
          type: n.type,
          branchId,
          userId: staffId,
        })
      })
    } else {
      await Note.create({ content })
    }

    return response.json({ message: 'Reported' })
  }

  @bind()
  /**
   * @show
   * @summary Show a single note
   * @description Show a note with their details (name and details)
   * @paramPath id required number - Note ID
   * @responseBody 200 - <Note>
   * @response 404 - Note not found
   */
  public async show({ response }: HttpContextContract, note: Note) {
    return response.json(note)
  }

  @bind()
  /**
   * @update
   * @summary Update a note
   * @description Update a note with their details (name and details)
   * @paramPath id required number - Note ID
   * @requestBody <Note>
   * @responseBody 200 - <Note>
   * @response 404 - Note not found
   */
  public async update({ request, response }: HttpContextContract, note: Note) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await note.merge(input).save()

    return response.json(note)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a Note
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, note: Note) {
    return await note.delete()
  }
}
