import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'vendors'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('user_id').references('id').inTable('users').onDelete('CASCADE')
      table.string('service_id').references('id').inTable('services').onDelete('CASCADE').nullable()
      table.string('name')
      table.string('slug')
      table.text('details').nullable()
      table.string('email', 255).notNullable()
      table.string('phone', 13).notNullable()
      table.string('reg', 20).nullable().unique()
      table.string('permit').nullable().unique()
      table.string('kra').notNullable().unique()
      table.json('logo').nullable()
      table.json('cover').nullable()
      table.boolean('active').defaultTo(0)
      table.boolean('featured').defaultTo(0)
      table.json('settings').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
