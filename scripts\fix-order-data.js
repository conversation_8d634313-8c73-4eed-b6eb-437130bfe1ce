#!/usr/bin/env node

/**
 * Order Data Fix Utility
 *
 * Simple, incremental approach to fix order data issues:
 * - Fixes missing order numbers for non-Pending orders
 * - Uses JavaScript for data manipulation (avoiding SQL JSON issues)
 * - Processes one order at a time for safety and clarity
 *
 * Usage: node scripts/fix-order-data.js
 */

const fs = require('fs')
const { Client } = require('pg')

function readEnv() {
  const envContent = fs.readFileSync('.env', 'utf8')
  const envVars = {}
  envContent.split('\n').forEach((line) => {
    const [key, value] = line.split('=')
    if (key && value) envVars[key.trim()] = value.trim()
  })
  return envVars
}

function createDbClient(envVars) {
  return new Client({
    host: envVars.PG_HOST,
    port: parseInt(envVars.PG_PORT),
    user: envVars.PG_USER,
    password: envVars.PG_PASSWORD,
    database: envVars.PG_DB_NAME,
    ssl: false,
  })
}

async function fixOneOrder(client, orderId) {
  console.log(`\n🔧 Fixing order: ${orderId}`)

  // Get the order
  const orderResult = await client.query('SELECT * FROM orders WHERE id = $1', [orderId])
  const order = orderResult.rows[0]

  if (!order) {
    console.log('❌ Order not found')
    return false
  }

  console.log(`📋 Current state: Status=${order.status}, OrderNumber=${order.order_number}`)

  // Generate order number if missing
  if (!order.order_number) {
    const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`

    await client.query('UPDATE orders SET order_number = $1, updated_at = NOW() WHERE id = $2', [
      orderNumber,
      orderId,
    ])

    console.log(`✅ Added order number: ${orderNumber}`)
    return true
  }

  console.log('ℹ️  Order already has order number')
  return false
}

async function main() {
  console.log('🚀 Simple Order Fix')
  console.log('==================')

  const envVars = readEnv()
  const client = createDbClient(envVars)

  try {
    await client.connect()
    console.log('✅ Database connected')

    // Get orders that need fixing
    const result = await client.query(`
            SELECT id, status, order_number
            FROM orders
            WHERE status != 'Pending' AND order_number IS NULL
            ORDER BY created_at
        `)

    console.log(`\nFound ${result.rows.length} orders needing order numbers:`)

    for (const order of result.rows) {
      console.log(`- ${order.id} (${order.status})`)
    }

    // Fix each order one by one
    let fixedCount = 0
    for (const order of result.rows) {
      const wasFixed = await fixOneOrder(client, order.id)
      if (wasFixed) fixedCount++
    }

    console.log(`\n🎉 Fixed ${fixedCount} orders`)

    // Show final state
    const finalResult = await client.query(`
            SELECT
                status,
                COUNT(*) as count,
                COUNT(CASE WHEN order_number IS NULL THEN 1 END) as missing_order_numbers
            FROM orders
            GROUP BY status
            ORDER BY status
        `)

    console.log('\n📊 Final state:')
    console.table(finalResult.rows)
  } catch (error) {
    console.error('❌ Error:', error.message)
  } finally {
    await client.end()
  }
}

main().catch(console.error)
