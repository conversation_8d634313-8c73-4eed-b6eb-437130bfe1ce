import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Product from '../Product'

export default class ProductFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Product, Product>

  public price(value: number) {
    this.$query.where('price', value)
  }

  public priceBetween(value: string) {
    const values = value.split(',')
    this.$query.whereBetween('price', [parseInt(values[0]), parseInt(values[1])])
  }

  public featured(value: boolean) {
    this.$query.where('featured', Boolean(value))
  }

  public active(value: boolean) {
    this.$query.where('active', Boolean(value))
  }

  public productCategoryId(value: string) {
    this.$query.where('productCategoryId', value)
  }

  public service(value: string) {
    this.$query.where('serviceId', value)
  }

  public vendor(value: string) {
    this.$query.where('vendorId', value)
  }

  public branch(value: string) {
    this.$query.where('branchId', value)
  }

  public payment(value: string) {
    this.$query.where('payment', value)
  }

  public shipping(value: string) {
    this.$query.where('shipping', value)
  }

  public condition(value: string) {
    this.$query.where('condition', value)
  }

  public type(value: string) {
    this.$query.whereHas('category', (cq) => {
      cq.where('productTypeId', value)
    })
  }

  public vendorTypes(value: string) {
    const types = value.split(',')
    this.$query.whereHas('vendor', (vq) => {
      vq.whereIn('type', types)
    })
  }

  public specialities(value: string) {
    const specialities = value.split(',')
    this.$query.whereHas('vendor', (vq) => {
      vq.whereHas('specialities', (sq) => {
        sq.whereIn('specialities.id', specialities)
      })
    })
  }

  public status(value: any): void {
    this.$query.where('status', value)
  }

  public s(name: string) {
    this.$query
      .whereILike('name', `%${name}%`)
      .whereHas('service', (sq) => sq.where('active', true))
  }
}
