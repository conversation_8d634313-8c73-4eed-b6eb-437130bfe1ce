import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import OrderItem from '../../Models/OrderItem'
import OrderItemModifier from '../../Models/OrderItemModifier'
import Order from '../../Models/Order'
import { bind } from '@adonisjs/route-model-binding'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Database from '@ioc:Adonis/Lucid/Database'
import StatusTransitionValidator from '../../Services/StatusTransitionValidator'
import OrderCompletionDetector from '../../Services/OrderCompletionDetector'
import StatusChangeBroadcaster from '../../Services/StatusChangeBroadcaster'

/**
 * @name Order Item Status Management
 * @version 1.0.0
 * @description Granular status updates for order items and modifiers
 */
export default class OrderItemStatusController {
  /**
   * @summary Update order item status
   * @description Update the status of a specific order item with validation
   * @paramPath id required number - Order Item ID
   * @requestBody Status update data
   * @responseBody 200 - Updated order item
   */
  @bind()
  public async updateItemStatus({ request, response }: HttpContextContract, orderItem: OrderItem) {
    const validationSchema = schema.create({
      status: schema.enum(['pending', 'preparing', 'ready', 'served', 'cancelled', 'on_hold', 'delayed'] as const),
      staff_id: schema.string.optional({}, [rules.uuid()]),
      notes: schema.string.optional({}, [rules.maxLength(500)]),
      estimated_preparation_time: schema.number.optional([rules.range(1, 300)]),
      priority_level: schema.number.optional([rules.range(1, 5)]),
      special_instructions: schema.string.optional({}, [rules.maxLength(500)])
    })

    const { status, staff_id, notes, estimated_preparation_time, priority_level, special_instructions } =
      await request.validate({ schema: validationSchema })

    // Comprehensive status transition validation
    const validation = await StatusTransitionValidator.validateItemTransition(orderItem, status, {
      staffId: staff_id,
      notes,
      priority: priority_level
    })

    if (!validation.isValid) {
      return response.badRequest({
        error: 'Status transition validation failed',
        details: validation.errors,
        required_fields: validation.requiredFields,
        validation_summary: StatusTransitionValidator.getValidationSummary(validation)
      })
    }

    // Check if we can proceed with warnings
    if (validation.warnings.length > 0) {
      // Log warnings but continue
      console.warn('Status transition warnings:', validation.warnings)
    }

    try {
      // Use model methods for status transitions
      switch (status) {
        case 'preparing':
          if (!staff_id) {
            return response.badRequest({ error: 'Staff ID is required when starting preparation' })
          }
          await orderItem.startPreparation(staff_id)
          break

        case 'ready':
          await orderItem.completePreparation()
          break

        case 'served':
          await orderItem.markAsServed(staff_id)
          break

        case 'cancelled':
          if (!notes) {
            return response.badRequest({ error: 'Cancellation reason is required' })
          }
          await orderItem.cancel(notes, staff_id)
          break

        default:
          // For other status changes, update directly
          orderItem.status = status
          if (staff_id) orderItem.assignedStaffId = staff_id
          if (notes) {
            const currentNotes = orderItem.preparationNotes || {}
            currentNotes[`update_${Date.now()}`] = {
              notes,
              timestamp: new Date().toISOString(),
              staffId: staff_id
            }
            orderItem.preparationNotes = currentNotes
          }
          await orderItem.save()
      }

      // Update additional fields if provided
      if (estimated_preparation_time) {
        orderItem.estimatedPreparationTime = estimated_preparation_time
      }
      if (priority_level) {
        orderItem.priorityLevel = priority_level
      }
      if (special_instructions) {
        orderItem.specialInstructions = special_instructions
      }

      await orderItem.save()
      await orderItem.load('department')
      await orderItem.load('assignedStaff')
      await orderItem.load('modifiers')

      // Broadcast status change
      await StatusChangeBroadcaster.broadcastItemStatusChange(
        orderItem,
        previousStatus,
        auth.user?.name,
        {
          estimated_preparation_time,
          priority_level,
          special_instructions,
          updated_via: 'api'
        }
      )

      // Broadcast staff assignment if staff was assigned
      if (staff_id && staff_id !== previousStatus) {
        await StatusChangeBroadcaster.broadcastStaffAssignment(
          orderItem,
          staff_id,
          auth.user?.name || 'system'
        )
      }

      // Check if order status needs updating using completion detector
      const completionResult = await OrderCompletionDetector.checkItemCompletion(orderItem.id)

      return response.json({
        message: 'Order item status updated successfully',
        item: orderItem.serialize(),
        completion_check: {
          order_completed: completionResult.orderCompleted,
          status_changed: completionResult.statusChanged,
          previous_status: completionResult.previousStatus,
          new_status: completionResult.newStatus,
          completion_time: completionResult.completionTime
        }
      })

    } catch (error) {
      return response.badRequest({
        error: error.message || 'Failed to update order item status'
      })
    }
  }

  /**
   * @summary Update modifier status
   * @description Update the status of a specific order item modifier
   * @paramPath id required number - Order Item Modifier ID
   * @requestBody Modifier status update data
   * @responseBody 200 - Updated modifier
   */
  @bind()
  public async updateModifierStatus({ request, response }: HttpContextContract, modifier: OrderItemModifier) {
    const validationSchema = schema.create({
      status: schema.enum(['pending', 'preparing', 'completed', 'skipped', 'cancelled', 'failed', 'on_hold'] as const),
      staff_id: schema.string.optional({}, [rules.uuid()]),
      notes: schema.string.optional({}, [rules.maxLength(500)]),
      quality_check: schema.boolean.optional(),
      failure_reason: schema.string.optional({}, [rules.maxLength(500)])
    })

    const { status, staff_id, notes, quality_check, failure_reason } =
      await request.validate({ schema: validationSchema })

    // Comprehensive modifier status transition validation
    const validation = await StatusTransitionValidator.validateModifierTransition(modifier, status, {
      staffId: staff_id,
      notes
    })

    if (!validation.isValid) {
      return response.badRequest({
        error: 'Modifier status transition validation failed',
        details: validation.errors,
        required_fields: validation.requiredFields,
        validation_summary: StatusTransitionValidator.getValidationSummary(validation)
      })
    }

    // Check if we can proceed with warnings
    if (validation.warnings.length > 0) {
      console.warn('Modifier status transition warnings:', validation.warnings)
    }

    const previousStatus = modifier.status

    try {
      // Use model methods for status transitions
      switch (status) {
        case 'preparing':
          if (!staff_id) {
            return response.badRequest({ error: 'Staff ID is required when starting preparation' })
          }
          await modifier.startPreparation(staff_id)
          break

        case 'completed':
          await modifier.completePreparation()
          break

        case 'skipped':
          await modifier.skip(notes || 'Skipped by staff', staff_id)
          break

        case 'failed':
          await modifier.markAsFailed(failure_reason || notes || 'Preparation failed', staff_id)
          break

        default:
          modifier.status = status
          if (staff_id) modifier.preparedByStaffId = staff_id
          if (notes) modifier.preparationNotes = notes
          await modifier.save()
      }

      // Perform quality check if requested
      if (quality_check !== undefined && staff_id) {
        await modifier.performQualityCheck(staff_id, quality_check, notes)
      }

      await modifier.load('preparedByStaff')
      await modifier.load('orderItem')

      // Broadcast modifier status change
      await StatusChangeBroadcaster.broadcastModifierStatusChange(
        modifier,
        previousStatus,
        auth.user?.name,
        {
          quality_check,
          failure_reason,
          updated_via: 'api'
        }
      )

      // Check if parent order item can be completed using completion detector
      const completionResult = await OrderCompletionDetector.checkModifierCompletion(modifier.id)

      return response.json({
        message: 'Modifier status updated successfully',
        modifier: modifier.serialize(),
        completion_check: {
          order_completed: completionResult.orderCompleted,
          status_changed: completionResult.statusChanged,
          previous_status: completionResult.previousStatus,
          new_status: completionResult.newStatus,
          completion_time: completionResult.completionTime
        }
      })

    } catch (error) {
      return response.badRequest({
        error: error.message || 'Failed to update modifier status'
      })
    }
  }

  /**
   * @summary Bulk update item statuses
   * @description Update multiple order items at once
   * @requestBody Bulk update data
   * @responseBody 200 - Update results
   */
  public async bulkUpdateItems({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      items: schema.array().members(
        schema.object().members({
          id: schema.number(),
          status: schema.enum(['pending', 'preparing', 'ready', 'served', 'cancelled', 'on_hold', 'delayed'] as const),
          staff_id: schema.string.optional({}, [rules.uuid()]),
          notes: schema.string.optional({}, [rules.maxLength(500)])
        })
      )
    })

    const { items } = await request.validate({ schema: validationSchema })

    const results = []
    const errors = []

    // Use transaction for bulk updates
    const trx = await Database.transaction()

    try {
      for (const itemUpdate of items) {
        try {
          const orderItem = await OrderItem.findOrFail(itemUpdate.id, { client: trx })

          // Validate transition
          const isValidTransition = this.validateStatusTransition(orderItem.status, itemUpdate.status)
          if (!isValidTransition) {
            errors.push({
              id: itemUpdate.id,
              error: `Invalid status transition from ${orderItem.status} to ${itemUpdate.status}`
            })
            continue
          }

          // Update status using appropriate method
          switch (itemUpdate.status) {
            case 'preparing':
              if (!itemUpdate.staff_id) {
                errors.push({ id: itemUpdate.id, error: 'Staff ID required for preparing status' })
                continue
              }
              await orderItem.startPreparation(itemUpdate.staff_id)
              break

            case 'ready':
              await orderItem.completePreparation()
              break

            case 'served':
              await orderItem.markAsServed(itemUpdate.staff_id)
              break

            case 'cancelled':
              await orderItem.cancel(itemUpdate.notes || 'Bulk cancellation', itemUpdate.staff_id)
              break

            default:
              orderItem.status = itemUpdate.status
              if (itemUpdate.staff_id) orderItem.assignedStaffId = itemUpdate.staff_id
              await orderItem.save()
          }

          results.push({
            id: itemUpdate.id,
            status: 'updated',
            new_status: itemUpdate.status
          })

        } catch (error) {
          errors.push({
            id: itemUpdate.id,
            error: error.message
          })
        }
      }

      await trx.commit()

      return response.json({
        message: 'Bulk update completed',
        results,
        errors,
        summary: {
          total: items.length,
          successful: results.length,
          failed: errors.length
        }
      })

    } catch (error) {
      await trx.rollback()
      return response.badRequest({
        error: 'Bulk update failed',
        details: error.message
      })
    }
  }

  /**
   * @summary Get item status history
   * @description Get the complete status history for an order item
   * @paramPath id required number - Order Item ID
   * @responseBody 200 - Status history
   */
  @bind()
  public async getItemHistory({ response }: HttpContextContract, orderItem: OrderItem) {
    await orderItem.load('assignedStaff')
    await orderItem.load('department')
    await orderItem.load('modifiers', (modifierQuery) => {
      modifierQuery.preload('preparedByStaff')
    })

    return response.json({
      item_id: orderItem.id,
      current_status: orderItem.status,
      status_history: orderItem.statusHistory,
      timing: {
        created_at: orderItem.createdAt,
        preparation_started_at: orderItem.preparationStartedAt,
        preparation_completed_at: orderItem.preparationCompletedAt,
        served_at: orderItem.servedAt,
        estimated_preparation_time: orderItem.estimatedPreparationTime,
        actual_preparation_time: orderItem.actualPreparationTime
      },
      assignment: {
        department: orderItem.department?.serialize(),
        assigned_staff: orderItem.assignedStaff?.serialize()
      },
      modifiers: orderItem.modifiers.map(modifier => ({
        ...modifier.serialize(),
        status_timeline: modifier.attemptHistory
      }))
    })
  }

  /**
   * @summary Assign item to staff
   * @description Assign an order item to a specific staff member
   * @paramPath id required number - Order Item ID
   * @requestBody Staff assignment data
   * @responseBody 200 - Assignment result
   */
  @bind()
  public async assignToStaff({ request, response, auth }: HttpContextContract, orderItem: OrderItem) {
    const validationSchema = schema.create({
      staff_id: schema.string({}, [rules.uuid()]),
      department_id: schema.string.optional({}, [rules.uuid()]),
      estimated_time: schema.number.optional([rules.range(1, 300)]),
      priority_level: schema.number.optional([rules.range(1, 5)]),
      notes: schema.string.optional({}, [rules.maxLength(500)])
    })

    const { staff_id, department_id, estimated_time, priority_level, notes } =
      await request.validate({ schema: validationSchema })

    // Verify item can be assigned
    if (!['pending', 'on_hold'].includes(orderItem.status)) {
      return response.badRequest({
        error: `Cannot assign item with status: ${orderItem.status}`
      })
    }

    orderItem.assignedStaffId = staff_id
    if (department_id) orderItem.departmentId = department_id
    if (estimated_time) orderItem.estimatedPreparationTime = estimated_time
    if (priority_level) orderItem.priorityLevel = priority_level

    // Add assignment note
    if (notes) {
      const currentNotes = orderItem.preparationNotes || {}
      currentNotes[`assignment_${Date.now()}`] = {
        action: 'assigned',
        notes,
        timestamp: new Date().toISOString(),
        staffId: staff_id
      }
      orderItem.preparationNotes = currentNotes
    }

    await orderItem.save()
    await orderItem.load('assignedStaff')
    await orderItem.load('department')

    // Broadcast staff assignment
    await StatusChangeBroadcaster.broadcastStaffAssignment(
      orderItem,
      staff_id,
      auth.user?.name || 'system',
      {
        department_id,
        estimated_time,
        priority_level,
        notes
      }
    )

    return response.json({
      message: 'Item assigned successfully',
      assignment: {
        item_id: orderItem.id,
        staff: orderItem.assignedStaff?.serialize(),
        department: orderItem.department?.serialize(),
        estimated_time: orderItem.estimatedPreparationTime,
        priority_level: orderItem.priorityLevel
      }
    })
  }

  /**
   * @summary Retry failed modifier
   * @description Retry a failed modifier preparation
   * @paramPath id required number - Order Item Modifier ID
   * @requestBody Retry data
   * @responseBody 200 - Retry result
   */
  @bind()
  public async retryModifier({ request, response }: HttpContextContract, modifier: OrderItemModifier) {
    const validationSchema = schema.create({
      staff_id: schema.string({}, [rules.uuid()]),
      notes: schema.string.optional({}, [rules.maxLength(500)])
    })

    const { staff_id, notes } = await request.validate({ schema: validationSchema })

    if (modifier.status !== 'failed') {
      return response.badRequest({
        error: 'Can only retry failed modifiers'
      })
    }

    try {
      await modifier.retry(staff_id)

      if (notes) {
        modifier.preparationNotes = notes
        await modifier.save()
      }

      await modifier.load('preparedByStaff')

      return response.json({
        message: 'Modifier retry initiated successfully',
        modifier: modifier.serialize(),
        attempt_number: modifier.preparationAttempts
      })

    } catch (error) {
      return response.badRequest({
        error: error.message || 'Failed to retry modifier'
      })
    }
  }

  /**
   * @summary Get department workload
   * @description Get current workload for a specific department
   * @paramPath department_id required string - Department ID
   * @responseBody 200 - Department workload
   */
  public async getDepartmentWorkload({ params, response }: HttpContextContract) {
    const { department_id } = params

    const workloadQuery = OrderItem.query()
      .where('department_id', department_id)
      .whereIn('status', ['pending', 'preparing', 'ready'])
      .preload('order')
      .preload('assignedStaff')
      .preload('modifiers')

    const items = await workloadQuery.exec()

    const workload = {
      department_id,
      total_items: items.length,
      by_status: {
        pending: items.filter(item => item.status === 'pending').length,
        preparing: items.filter(item => item.status === 'preparing').length,
        ready: items.filter(item => item.status === 'ready').length
      },
      overdue_items: items.filter(item => item.isOverdue).length,
      high_priority_items: items.filter(item => item.priorityLevel === 1).length,
      items: items.map(item => ({
        ...item.serialize(),
        is_overdue: item.isOverdue,
        preparation_progress: item.preparationProgress,
        estimated_completion: item.estimatedCompletionTime
      }))
    }

    return response.json(workload)
  }

  // Private helper methods

  /**
   * @summary Check order completion status
   * @description Get detailed completion status for an order
   * @paramPath order_id required string - Order ID
   * @responseBody 200 - Completion status
   */
  public async checkOrderCompletionStatus({ params, response }: HttpContextContract) {
    const { order_id } = params

    try {
      const completionResult = await OrderCompletionDetector.checkOrderCompletion(order_id)

      return response.json({
        order_id,
        completion_result: completionResult,
        message: completionResult.statusChanged ?
          `Order status updated from ${completionResult.previousStatus} to ${completionResult.newStatus}` :
          'Order completion checked - no status change'
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to check order completion',
        details: error.message
      })
    }
  }

  /**
   * @summary Bulk check order completion
   * @description Check completion status for multiple orders
   * @requestBody Array of order IDs
   * @responseBody 200 - Bulk completion results
   */
  public async bulkCheckOrderCompletion({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      order_ids: schema.array().members(schema.string())
    })

    const { order_ids } = await request.validate({ schema: validationSchema })

    try {
      const results = await OrderCompletionDetector.bulkCheckOrderCompletion(order_ids)

      const summary = {
        total_checked: results.length,
        completed_orders: results.filter(r => r.statusChanged).length,
        errors: results.filter(r => r.error).length
      }

      return response.json({
        summary,
        results
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to bulk check order completion',
        details: error.message
      })
    }
  }
}
