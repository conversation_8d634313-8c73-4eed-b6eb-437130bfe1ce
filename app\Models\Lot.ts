import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  ManyToMany,
  beforeCreate,
  belongsTo,
  column,
  manyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import LotFilter from './Filters/LotFilter'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { ulid } from 'ulidx'
import Section from './Section'
import User from './User'

export default class Lot extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => LotFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public sectionId: string

  @attachment()
  public image: AttachmentContract | null

  @beforeCreate()
  public static async generateUlid(lot: Lot) {
    lot.id = ulid().toLowerCase()
  }

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Section)
  public section: BelongsTo<typeof Section>

  @manyToMany(() => User, {
    pivotTable: 'rosters',
    pivotForeignKey: 'lot_id',
    pivotRelatedForeignKey: 'staff_id',
    pivotColumns: ['start_at', 'end_at'],
  })
  public staff: ManyToMany<typeof User>
}
