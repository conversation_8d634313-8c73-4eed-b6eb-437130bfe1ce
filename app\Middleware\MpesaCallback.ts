import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

/**
 * M-Pesa Callback Middleware
 * 
 * This middleware handles M-Pesa specific request processing:
 * - Logs all M-Pesa callback requests for debugging
 * - Validates request format and headers
 * - Adds security headers for M-Pesa responses
 * - <PERSON><PERSON> request timeout scenarios
 */
export default class MpesaCallbackMiddleware {
  public async handle(
    { request, response }: HttpContextContract,
    next: () => Promise<void>
  ) {
    const startTime = Date.now()
    const requestId = this.generateRequestId()
    
    try {
      // Log incoming M-Pesa request
      console.log('=== M-PESA CALLBACK MIDDLEWARE ===')
      console.log('Request ID:', requestId)
      console.log('Method:', request.method())
      console.log('URL:', request.url())
      console.log('IP Address:', request.ip())
      console.log('User Agent:', request.header('user-agent'))
      console.log('Content Type:', request.header('content-type'))
      console.log('Content Length:', request.header('content-length'))
      console.log('Timestamp:', new Date().toISOString())
      
      // Validate content type for POST requests
      if (request.method() === 'POST') {
        const contentType = request.header('content-type')
        if (!contentType || !contentType.includes('application/json')) {
          console.warn('Invalid content type for M-Pesa callback:', contentType)
        }
      }
      
      // Add request ID to response headers for tracking
      response.header('X-Request-ID', requestId)
      response.header('X-Callback-Source', 'mpesa')
      
      // Set security headers for M-Pesa responses
      response.header('X-Content-Type-Options', 'nosniff')
      response.header('X-Frame-Options', 'DENY')
      response.header('Cache-Control', 'no-cache, no-store, must-revalidate')
      
      await next()
      
      // Log response details
      const duration = Date.now() - startTime
      console.log('=== M-PESA CALLBACK RESPONSE ===')
      console.log('Request ID:', requestId)
      console.log('Status Code:', response.getStatus())
      console.log('Duration:', `${duration}ms`)
      console.log('Response Headers:', response.getHeaders())
      
    } catch (error) {
      const duration = Date.now() - startTime
      console.error('=== M-PESA CALLBACK ERROR ===')
      console.error('Request ID:', requestId)
      console.error('Error:', error.message)
      console.error('Duration:', `${duration}ms`)
      console.error('Stack:', error.stack)
      
      // Ensure M-Pesa gets a proper response even on error
      if (!response.hasLazyBody) {
        response.status(500).json({
          "ResultCode": 1,
          "ResultDesc": "Internal server error"
        })
      }
    }
  }
  
  /**
   * Generate a unique request ID for tracking
   */
  private generateRequestId(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 5)
    return `mpesa_${timestamp}_${random}`
  }
}
