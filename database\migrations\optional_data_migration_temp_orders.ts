import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class OptionalDataMigrationTempOrders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    // Optional: Migrate existing temp_orders data to orders table if needed
    // This migration is only needed if there are temp_orders that need to be preserved
    
    // Check if temp_orders table exists and has data
    const hasData = await this.db.rawQuery(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'temp_orders'
      ) as table_exists;
    `)

    if (hasData.rows[0]?.table_exists) {
      const tempOrdersCount = await this.db.rawQuery(`
        SELECT COUNT(*) as count FROM temp_orders;
      `)

      console.log(`Found ${tempOrdersCount.rows[0]?.count || 0} temp_orders to potentially migrate`)

      // Migrate temp_orders that don't already exist in orders table
      await this.db.rawQuery(`
        INSERT INTO orders (
          id, vendor_id, branch_id, section_id, lot_id, user_id, staff_id,
          action, type, payment, delivery, status, items, meta, ref, currency,
          created_at, updated_at, accepted_at, start_at, end_at
        )
        SELECT 
          t.id, t.vendor_id, t.branch_id, t.section_id, t.lot_id, t.user_id, t.staff_id,
          t.action, t.type, t.payment, t.delivery, t.status, 
          t.items, -- Copy items directly to new items column
          CASE 
            WHEN t.meta IS NULL THEN jsonb_build_object('temp_items', t.items)
            ELSE t.meta || jsonb_build_object('temp_items', t.items)
          END as meta, -- Ensure temp_items is in meta for backward compatibility
          t.ref, t.currency,
          t.created_at, t.updated_at, t.accepted_at, t.start_at, t.end_at
        FROM temp_orders t
        WHERE NOT EXISTS (
          SELECT 1 FROM orders o WHERE o.id = t.id
        );
      `)

      const migratedCount = await this.db.rawQuery(`
        SELECT COUNT(*) as count FROM orders 
        WHERE items IS NOT NULL AND meta ? 'temp_items';
      `)

      console.log(`Migrated ${migratedCount.rows[0]?.count || 0} temp_orders to orders table`)
    }
  }

  public async down() {
    // Remove migrated temp_orders data
    await this.db.rawQuery(`
      DELETE FROM orders 
      WHERE items IS NOT NULL 
      AND meta ? 'temp_items'
      AND status = 'Pending';
    `)
  }
}
