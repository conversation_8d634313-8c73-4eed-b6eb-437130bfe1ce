import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ModifierOption from 'App/Models/ModifierOption'
import { ModifierType } from 'App/Enums/ModifierType'

export default class ModifierOptionsController {
  public async index({ request, response }: HttpContextContract) {
    const { vendorId, type } = request.qs()
    const query = ModifierOption.query()

    if (vendorId) {
      query.where('vendor_id', vendorId)
    }

    if (type && Object.values(ModifierType).includes(type)) {
      query.where('type', type)
    }

    const modifierOptions = await query.exec()
    return response.ok(modifierOptions)
  }

  public async store({ request, response }: HttpContextContract) {
    try {
      const data = request.only([
        'vendor_id',
        'name',
        'type',
        'description',
        'default_price_adjustment',
        'max_quantity',
        'active',
      ])

      // Validate required fields
      if (!data.name) {
        return response.badRequest({ message: 'Name is required' })
      }

      if (!data.vendor_id) {
        return response.badRequest({ message: 'Vendor ID is required' })
      }

      if (!data.type) {
        return response.badRequest({ message: 'Type is required' })
      }

      // Prepare payload with correct property names (camelCase for model)
      const payload = {
        vendorId: data.vendor_id,
        name: data.name,
        type: data.type as ModifierType,
        description: data.description || '',
        defaultPriceAdjustment: data.default_price_adjustment || 0,
        maxQuantity: data.max_quantity || null,
        active: data.active !== undefined ? data.active : true,
      }

      // Use the static create method
      const modifierOption = await ModifierOption.create(payload)

      return response.created(modifierOption)
    } catch (error) {
      console.error('Error creating modifier option:', error)
      return response.status(500).json({
        message: 'Failed to create modifier option',
        error: error.message,
      })
    }
  }

  public async show({ params, response }: HttpContextContract) {
    const modifierOption = await ModifierOption.findOrFail(params.id)
    return response.ok(modifierOption)
  }

  public async update({ params, request, response }: HttpContextContract) {
    try {
      const modifierOption = await ModifierOption.findOrFail(params.id)
      const data = request.only([
        'vendor_id',
        'name',
        'type',
        'description',
        'default_price_adjustment',
        'max_quantity',
        'active',
      ])

      // Validate required fields
      if (!data.name) {
        return response.badRequest({ message: 'Name is required' })
      }

      if (!data.vendor_id) {
        return response.badRequest({ message: 'Vendor ID is required' })
      }

      if (!data.type) {
        return response.badRequest({ message: 'Type is required' })
      }

      const payload = {
        vendorId: data.vendor_id,
        name: data.name,
        type: data.type as ModifierType,
        description: data.description || '',
        defaultPriceAdjustment: data.default_price_adjustment || 0,
        maxQuantity: data.max_quantity || null,
        active: data.active !== undefined ? data.active : true,
      }

      modifierOption.merge(payload)
      await modifierOption.save()

      return response.ok(modifierOption)
    } catch (error) {
      // Handle not found error
      if (error.code === '23503') {
        return response.notFound({ message: 'Modifier option not found' })
      }
      
      // Handle unique constraint violation
      if (error.code === '23505') {
        return response.status(409).json({
          message: 'A modifier option with this name already exists for this vendor',
        })
      }
      
      console.error('Error updating modifier option:', error)
      return response.status(500).json({
        message: 'Failed to update modifier option',
        error: error.message,
      })
    }
  }

  public async destroy({ params, response }: HttpContextContract) {
    const modifierOption = await ModifierOption.findOrFail(params.id)
    await modifierOption.delete()

    return response.noContent()
  }
}
