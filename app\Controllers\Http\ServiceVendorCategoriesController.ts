import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'



export default class ServiceVendorCategoriesController {
  @bind()

  /**
   * @index
   * @summary Show all ServiceVendorCategories
   * @version 1.0.0
   * @description ServiceVendorCategories management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract, service: Service) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const categoryQuery = service.related('vendorCategories').query()

    // Apply basic filters manually since HasManyThrough doesn't support filter()
    if (filters.name) {
      categoryQuery.where('name', 'like', `%${filters.name}%`)
    }

    return await categoryQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a ServiceVendorCategory
   * @description Create a ServiceVendorCategory with their details (name and details)
   * @requestBody {"name": "", "details": "", "serviceId": ""}
   */
  public async store({ request, response }: HttpContextContract, service: Service) {
    const { name, details, vendorTypeId } = request.all()

    // Verify the vendor type belongs to this service
    const vendorType = await service.related('vendorTypes')
      .query()
      .where('id', vendorTypeId)
      .firstOrFail()

    // Create the category through the vendor type
    const category = await vendorType.related('vendorCategories').create({
      name,
      details,
      vendorTypeId
    })

    const image = request.file('image')
    if (image) {
      category.merge({ image: Attachment.fromFile(image) })
      await category.save()
    }

    return response.json(category)
  }
} 