import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Branch from 'App/Models/Branch'

/**
 * @name Group management
 * @version 1.0.0
 * @description Group management for the application
 */
export default class BranchGroupsController {
  /**
   * @index
   * @summary List all groups
   * @description List all groups, paginated
   * @paramUse (filterable)
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page Number
   * @paramQuery order - Order by field
   * @paramQuery sort order - (asc, desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const groupQuery = branch.related('groups').query().filter(filters)

    return await groupQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a group
   * @description Create a group with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Group>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, branch: Branch) {
    const { name, details, vendorId } = request.all()
    const group = await branch.related('groups').create({ name, details, vendorId })

    const image = request.file('image')
    if (image) {
      await group.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(group)
  }
}
