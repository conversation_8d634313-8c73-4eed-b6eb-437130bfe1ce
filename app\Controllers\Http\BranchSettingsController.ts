import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Branch from '../../Models/Branch'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Branch management
 * @version 1.0.0
 * @description Branch management for the application
 */
export default class BranchSettingsController {
  /**
   * @index
   * @summary List all branches
   * @description List all branches, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const branchQuery = branch.related('settings').query().filter(filters)

    return await branchQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a branch
   * @description Create a branch with their options (name and options)
   * @requestBody {"name": "", "options": ""}
   * @responseBody 200 - <Branch>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, branch: Branch) {
    const { name, options, branchId } = request.all()
    branch.related('settings').updateOrCreate({ branchId, name }, { options })

    const image = request.file('image')
    if (image) {
      branch.merge({ image: Attachment.fromFile(image) })
    }

    await branch.save()

    return response.json(branch)
  }
}
