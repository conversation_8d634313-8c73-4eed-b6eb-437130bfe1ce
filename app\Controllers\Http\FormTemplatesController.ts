import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import FormTemplate from '../../Models/FormTemplate'
import { bind } from '@adonisjs/route-model-binding'

export default class FormTemplatesController {
  /**
   * @index
   * @summary Show all templates
   * @version 1.0.0
   * @description FormTemplate management for the application
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page Number
   * @paramQuery order - Order by field
   * @paramQuery sort order - (asc, desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const templateQuery = FormTemplate.filter(filters)

    return await templateQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a template
   * @description Create a template with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <FormTemplate>
   */
  public async store({ request }: HttpContextContract) {
    const { name, details, sections = [] } = request.all()
    const template = new FormTemplate()

    template.fill({ name, details, sections: sections.reduce((p, s) => ({ ...p, [s.id]: s }), {}) })

    return await template.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a Form Template
   * @description Show a form template with their details (name and details)
   * @responseBody 200 - <FormTemplate>
   * @response 404 - FormTemplate not found
   */
  public async show({ response }: HttpContextContract, template: FormTemplate) {
    return response.json(template)
  }

  @bind()

  /**
   * @update
   * @summary Update a form template
   * @description Update a form template with their details (name and details)
   * @requestBody <FormTemplate>
   * @responseBody 200 - <FormTemplate>
   * @response 404 - FormTemplate not found
   */
  public async update({ request, response }: HttpContextContract, template: FormTemplate) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await template.merge(input).save()

    return response.json(template)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Form Template
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, template: FormTemplate) {
    return await template.delete()
  }
}
