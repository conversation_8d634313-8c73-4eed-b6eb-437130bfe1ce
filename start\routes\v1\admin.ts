// start/routes/v1/admin.ts
import Route from '@ioc:Adonis/Core/Route'

Route.group(() => {
  Route.post('/vendors/bulk-setup', 'Admin/VendorBulkSetupController.store')
    .as('admin.vendors.bulk-setup.store')
  Route.get('/bulk-upload-jobs/:jobId', 'Admin/VendorBulkSetupController.getJobStatus')
    .as('admin.vendors.bulk-setup.job-status')

  // Add other admin routes here
})
  .prefix('/v1/admin')
  .middleware(['auth', 'acl:admin']) // Ensure 'auth' and an 'admin' role/permission check middleware
  .as('v1.admin') 