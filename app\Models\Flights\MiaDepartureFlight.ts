import { DateTime } from 'luxon'
import { compose } from '@ioc:<PERSON>onis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { column, BaseModel } from '@ioc:Adonis/Lucid/Orm'
import MiaDepartureFlightFilter from './Filters/MiaDepartureFlightFilter'

export default class MiaDepartureFlight extends compose(BaseModel, Filterable) {
  public static connection = 'kaa'

  public static $filter = () => MiaDepartureFlightFilter

  @column({ isPrimary: true, columnName: 'Flight_Id' })
  public flightId: number

  @column({ columnName: 'File_Path' })
  public filePath: string | null

  @column({ columnName: 'Airline' })
  public airline: string

  @column({ columnName: 'Flight_No' })
  public flightNo: string

  @column({
    columnName: 'Origin_Airport',
  })
  public originAirport: string

  @column.dateTime({
    columnName: 'SchTime',
  })
  public schTime: DateTime

  @column.dateTime({
    columnName: 'ETA',
  })
  public eta: DateTime

  @column({
    columnName: 'Remarks',
  })
  public remarks: string

  @column({
    columnName: 'Terminal',
  })
  public terminal: string | null

  @column({
    columnName: 'Belt',
  })
  public belt: string | null
}
