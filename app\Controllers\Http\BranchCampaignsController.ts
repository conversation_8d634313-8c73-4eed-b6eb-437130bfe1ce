import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Campaign from '../../Models/Campaign'
import { bind } from '@adonisjs/route-model-binding'
import Branch from 'App/Models/Branch'

/**
 * @name Campaign management
 * @version 1.0.0
 * @description Campaign management for the application
 */
export default class BranchCampaignsController {
  /**
   * @index
   * @summary List all campaigns
   * @description List all campaigns, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const campaignQuery = Campaign.filter(filters).where('branchId', branch.id).preload('vendor')

    return await campaignQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a campaign
   * @description Create a campaign with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Campaign>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, branch: Branch) {
    const { name, details, vendorId, startDate, endDate } = request.all()

    const campaign = new Campaign()

    // Validate and set startDate
    if (!startDate || startDate === 'undefined') {
      return response.status(400).json({
        error: 'Start date is required and must be a valid timestamp'
      })
    }

    // Validate and set endDate
    if (!endDate) {
      return response.status(400).json({
        error: 'End date is required and must be a valid timestamp'
      })
    }

    campaign.fill({ name, details, vendorId, branchId: branch.id, startDate, endDate })

    const image = request.file('image')
    if (image) {
      campaign.merge({ image: Attachment.fromFile(image) })
    }

    await campaign.save()

    return response.json(campaign)
  }
}
