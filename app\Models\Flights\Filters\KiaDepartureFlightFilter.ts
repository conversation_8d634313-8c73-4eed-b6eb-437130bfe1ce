import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import KiaDepartureFlight from '../KiaDepartureFlight'

export default class KiaDepartureFlightFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof KiaDepartureFlight>

  public name(value: string): void {
    this.$query.where('name', value)
  }
}
