import { BaseMailer, MessageContract } from '@ioc:Adonis/Addons/Mail'
import Env from '@ioc:Adonis/Core/Env'

import User from '../Models/User'

/**
 * @see https://docs.adonisjs.com/guides/mailer#configuring-new-mailers
 */

export default class CustomerMailer extends BaseMailer {
  constructor(
    private user: User,
    private template: string,
    public data: any
  ) {
    super()
  }

  public prepare(message: MessageContract) {
    message
      .subject(this.data.subject || 'Hello there')
      .from(Env.get('MAIL_DOMAIN') ?? '<EMAIL>', 'AppInApp')
      .to(this.user.email)
      .htmlView(this.template, this.data)
  }
}
