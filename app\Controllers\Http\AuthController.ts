import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// FIX: Correct import paths for models assuming standard Adonis structure
import User from 'App/Models/User'
import Account from 'App/Models/Account'
import Vendor from 'App/Models/Vendor'
import Branch from 'App/Models/Branch'
import Order from 'App/Models/Order'
// import Role from 'App/Models/Role' // FIX: Removed unused import

// Import Notifications (adjust paths if necessary)
// import SendOtp from 'App/Notifications/SendOtp' // Uncomment if used
import SendPasswordResetLink from 'App/Notifications/SendPasswordResetLink'
import SendSmsOtp from 'App/Notifications/SendSmsOtp'
import CustomerWelcomeOtp from 'App/Notifications/Customer/CustomerWelcomeOtp'

import crypto from 'crypto'
import { DateTime } from 'luxon'
import Database from '@ioc:Adonis/Lucid/Database'
// Add import for UI actions configuration
import { allActionCards, roleActionMapping } from 'Config/ui_actions'
import Env from '@ioc:Adonis/Core/Env'
import StaffRegisterValidator from 'App/Validators/StaffRegisterValidator'
import OrderPricingService from 'App/Services/OrderPricingService'
import HierarchicalAccessControlService from 'App/Services/HierarchicalAccessControlService'

/**
 * @summary Authentication and authorization
 * @group Auth
 * @version 1.0.0
 * @description Authentication and authorization
 */
export default class AuthController {
  /**
   * @login
   * @summary Login a user
   * @description Login a user with their username (phone or email address) and password
   * @requestBody {"username": "", "password": ""}
   *
   * @responseBody 200 - { user: User, type: string, token: string, refreshToken?: string }
   * @response 400 - {"status": "error", "message": "Invalid credentials, no user found"}
   */
  public async login({ auth, request, response }: HttpContextContract) {
    const { username, password, portal = false } = request.all()

    const trimmed = username.trim().toLowerCase()

    try {
      const user = trimmed.includes('@')
        ? await User.findBy('email', trimmed)
        : await User.findBy('phone', trimmed.replace(/^\+/, ''))

      if (user) {
        if (user.status !== 'Active') {
          return response
            .status(403)
            .json({ error: `User account is ${user.status}. Please contact support.` })
        }

        const jwt = await auth.use('api').attempt(trimmed, password, {
          expiresIn: '7days',
        })

        // Generate a refresh token
        const refreshToken = crypto.randomBytes(32).toString('hex')

        // Store the refresh token in the database
        await Database.from('api_tokens')
          .where('token', jwt.token)
          .update({
            refresh_token: refreshToken,
            refresh_token_expires_at: DateTime.now().plus({ days: 30 }).toSQL(),
          })

        console.log('Token Response:', {
          type: jwt.type,
          token: jwt.token,
          refreshToken: refreshToken,
        })

        await user.load('roles')

        if (portal) {
          const hasAdminRole = user.roles.some((role) => role.name === 'admin')

          return hasAdminRole
            ? response.json({
                user: user.serialize(),
                type: jwt.type,
                token: jwt.token,
                refreshToken,
              })
            : response
                .status(403)
                .json({ error: 'Forbidden: Insufficient privileges for portal access.' })
        } else {
          return response.json({
            user: user.serialize(),
            type: jwt.type,
            token: jwt.token,
            refreshToken,
          })
        }
      }

      return response.badRequest({ error: 'Invalid credentials, no user found' })
    } catch (e) {
      console.error('Login Error:', { error: e.message, username: trimmed })

      if (e.code === 'E_INVALID_AUTH_UID' || e.code === 'E_INVALID_AUTH_PASSWORD') {
        return response.badRequest({ error: 'Invalid credentials.' })
      }

      return response.internalServerError({ error: 'An unexpected error occurred during login.' })
    }
  }

  /**
   * @staffLogin
   * @summary Login a vendor staff
   * @description Login a staff member with their username (phone or email address), along with their identifier and password
   * @requestBody {"username": "", "password": "", "identifier": ""}
   * @responseBody 200 - { user: User, vendor: Vendor | null, branch: Branch | null, type: string, token: string, refreshToken?: string }
   */
  public async staffLogin({ auth, request, response }: HttpContextContract) {
    const { username, password, identifier } = request.all()

    const trimmed = username.trim()
    const usernameField = trimmed.includes('@') ? 'email' : 'phone'
    const usernameValue =
      usernameField === 'phone' ? trimmed.replace(/^\+/, '') : trimmed.toLowerCase()

    try {
      const user = await User.query().where(usernameField, usernameValue).first()

      if (!user) {
        return response.forbidden({ error: 'Invalid credentials, user not found.' })
      }

      if (user.status !== 'Active') {
        return response
          .status(403)
          .json({ error: `User account is ${user.status}. Please contact support.` })
      }

      const employerVendor = await user
        .related('employers')
        .query()
        .wherePivot('identifier', identifier)
        .first()

      const stationBranch = await user
        .related('stations')
        .query()
        .wherePivot('identifier', identifier)
        .first()

      if (!employerVendor && !stationBranch) {
        return response.forbidden({
          error:
            'You are not registered as staff with this identifier for any organization or branch.',
        })
      }

      const jwt = await auth.use('api').attempt(usernameValue, password, {
        expiresIn: '7days',
      })

      // Generate a refresh token
      const refreshToken = crypto.randomBytes(32).toString('hex')

      // Store the refresh token in the database
      await Database.from('api_tokens')
        .where('token', jwt.token)
        .update({
          refresh_token: refreshToken,
          refresh_token_expires_at: DateTime.now().plus({ days: 30 }).toSQL(),
        })

      await user.load('roles')

      // Get role actions for the user
      const primaryRole = user.roles[0].name.toLowerCase()
      const allowedActionIds = roleActionMapping[primaryRole] ?? roleActionMapping['*']
      const allowedCards = allActionCards.filter((card) => allowedActionIds.includes(card.id))
      const allowedActionNames = allowedCards.map((card) => card.name)

      return response.json({
        user: user.serialize(),
        vendor: employerVendor?.serialize(),
        branch: stationBranch?.serialize(),
        type: jwt.type,
        token: jwt.token,
        refreshToken,
        roleActions: allowedActionNames,
        expiresAt: DateTime.now().plus({ days: 7 }).toISO(),
      })
    } catch (e) {
      console.error('Staff Login Error:', { error: e.message, username: trimmed, identifier })

      if (e.code === 'E_INVALID_AUTH_UID' || e.code === 'E_INVALID_AUTH_PASSWORD') {
        return response.badRequest({ error: 'Invalid credentials.' })
      }
      if (e.code === 'E_ROW_NOT_FOUND') {
        return response.forbidden({ error: 'Invalid credentials, user not found.' })
      }

      return response.internalServerError({
        error: 'An unexpected error occurred during staff login.',
      })
    }
  }

  /**
   * @register
   * @summary Register a user
   * @description Create an account for a user with their details (phone and/or email address) and password
   * @requestBody {"firstName": "", "lastName": "", "phone": "", "email": "", "password": "", "role": ""}
   * @responseBody 201 - <User>
   * @response 400 - { status: 'error', message: 'Invalid data'}
   * @response 409 - Conflict (User already exists)
   */
  public async register({ auth, request, response }: HttpContextContract) {
    const {
      firstName,
      lastName,
      email,
      phone: postedPhone,
      password: postedPassword,
      role = 'customer', // Default role
      vendorId = null,
      branchId = null,
    } = request.all()

    if (await auth.check()) {
      return response
        .status(400)
        .json({ status: 'error', message: 'Already authorized. Please logout to register.' })
    }

    if (!email || !firstName || !lastName) {
      return response.badRequest({
        status: 'error',
        message: 'Missing required fields (email, firstName, lastName).',
      })
    }
    if (!postedPassword) {
      return response.badRequest({ status: 'error', message: 'Password is required.' })
    }

    const cleanedPhone = postedPhone ? postedPhone.replace(/^\+/, '') : null
    const lowerCaseEmail = email.trim().toLowerCase()

    let password = postedPassword

    let query = User.query().where('email', lowerCaseEmail)
    if (cleanedPhone) {
      query = query.orWhere('phone', cleanedPhone)
    }
    const existingUser = await query.first()

    if (existingUser) {
      const conflictField = existingUser.email === lowerCaseEmail ? 'Email' : 'Phone number'
      return response
        .status(409)
        .json({ status: 'error', message: `${conflictField} already in use.` })
    }

    const trx = await Database.transaction()

    try {
      const otp = crypto.randomInt(100000, 999999)

      const user = await User.create(
        {
          firstName,
          lastName,
          otp,
          phone: cleanedPhone,
          email: lowerCaseEmail,
          password,
          status: 'Inactive',
        },
        { client: trx }
      )

      await user.useTransaction(trx).assignRole(role)

      if (vendorId && branchId && role !== 'customer' && role !== 'admin') {
        await user
          .useTransaction(trx)
          .related('employers')
          .attach({
            [vendorId]: {
              identifier: crypto.randomBytes(8).toString('hex'),
              branchId,
            },
          })
      }

      await user.useTransaction(trx).notify(new CustomerWelcomeOtp())

      await trx.commit()

      const userJson = user.toJSON()
      delete userJson.password
      delete userJson.otp

      return response.created(userJson)
    } catch (e) {
      await trx.rollback()
      console.error('Registration Error:', e)
      return response
        .status(500)
        .json({ status: 'error', message: 'Failed to create user account.', error: e.message })
    }
  }

  /**
   * @staffRegister
   * @summary Register a customer via staff member
   * @description Create a customer account through a staff member, enabling tracking of registrations per staff
   * @paramPath staffId required string - Staff member ID performing the registration
   * @requestBody {"firstName": "", "lastName": "", "phone": "", "email": "", "password": ""}
   * @responseBody 201 - <User>
   * @response 400 - { status: 'error', message: 'Invalid data'}
   * @response 403 - { status: 'error', message: 'Staff member not authorized'}
   * @response 409 - Conflict (User already exists)
   */
  public async staffRegister({ auth, request, response, params }: HttpContextContract) {
    const { staffId } = params

    // Validate request data
    const validatedData = await request.validate(StaffRegisterValidator)
    const {
      firstName,
      lastName,
      email,
      phone: postedPhone,
      password: postedPassword,
      gender = null,
      dob = null,
      idpass = null,
    } = validatedData

    const cleanedPhone = postedPhone ? postedPhone.replace(/^\+/, '') : null
    const lowerCaseEmail = email.trim().toLowerCase()

    // Ensure authenticated user can only register for themselves or has admin privileges
    const authenticatedUser = auth.user!
    const isAdmin = await authenticatedUser.hasRole('admin')

    if (!isAdmin && authenticatedUser.id !== staffId) {
      return response.status(403).json({
        status: 'error',
        message: 'You can only register customers for yourself unless you have admin privileges.',
      })
    }

    // Verify staff member exists and get their vendor/branch info
    const staffMember = await User.query()
      .where('id', staffId)
      .whereHas('employers', (vendorQuery) => {
        vendorQuery.whereNotNull('staff.vendor_id')
      })
      .preload('employers', (vendorQuery) => {
        vendorQuery.pivotColumns(['vendor_id', 'branch_id', 'identifier'])
      })
      .first()

    if (!staffMember) {
      return response.status(403).json({
        status: 'error',
        message: 'Staff member not found or not associated with any vendor.',
      })
    }

    // Check if staff member has permission to register customers
    const hasPermission = await staffMember.hasAnyRoles(
      'staff',
      'manager',
      'admin',
      'vendor',
      'waiter',
      'barman',
      'barista',
      'rider'
    )
    if (!hasPermission) {
      return response.status(403).json({
        status: 'error',
        message: 'Staff member does not have permission to register customers.',
      })
    }

    // Get vendor and branch info from staff member
    const staffEmployer = staffMember.employers[0]
    if (!staffEmployer) {
      return response.status(403).json({
        status: 'error',
        message: 'Staff member is not associated with any vendor/branch.',
      })
    }

    const vendorId = staffEmployer.$extras.pivot_vendor_id
    const branchId = staffEmployer.$extras.pivot_branch_id

    // Check if user already exists
    let query = User.query().where('email', lowerCaseEmail)
    if (cleanedPhone) {
      query = query.orWhere('phone', cleanedPhone)
    }
    const existingUser = await query.first()

    if (existingUser) {
      const conflictField = existingUser.email === lowerCaseEmail ? 'Email' : 'Phone number'
      return response
        .status(409)
        .json({ status: 'error', message: `${conflictField} already in use.` })
    }

    const trx = await Database.transaction()

    try {
      const otp = crypto.randomInt(100000, 999999)

      // Create the customer user
      const customer = await User.create(
        {
          firstName,
          lastName,
          otp,
          phone: cleanedPhone,
          email: lowerCaseEmail,
          password: postedPassword,
          gender,
          dob: dob ? dob.toFormat('yyyy-MM-dd') : null,
          idpass,
          status: 'Active', // Staff-registered customers are immediately active
        },
        { client: trx }
      )

      // Assign customer role
      await customer.useTransaction(trx).assignRole('customer')

      // Link customer to vendor/branch with staff tracking
      await customer
        .useTransaction(trx)
        .related('branches')
        .attach({
          [branchId]: {
            vendor_id: vendorId,
            active: true,
            registered_by_staff_id: staffId,
          },
        })

      // Send welcome notification
      await customer.useTransaction(trx).notify(new CustomerWelcomeOtp())

      await trx.commit()

      const customerJson = customer.toJSON()
      delete customerJson.password
      delete customerJson.otp

      return response.created({
        ...customerJson,
        registeredBy: {
          staffId: staffId,
          staffName: `${staffMember.firstName} ${staffMember.lastName}`,
          vendorId: vendorId,
          branchId: branchId,
        },
      })
    } catch (e) {
      await trx.rollback()
      console.error('Staff Registration Error:', e)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to create customer account.',
        error: e.message,
      })
    }
  }

  /**
   * @getStaffRegistrationStats
   * @summary Get registration statistics for staff members
   * @description Get count of customers registered by each staff member
   * @queryParam staffId optional string - Filter by specific staff member
   * @queryParam vendorId optional string - Filter by vendor
   * @queryParam branchId optional string - Filter by branch
   * @queryParam startDate optional string - Start date for filtering (YYYY-MM-DD)
   * @queryParam endDate optional string - End date for filtering (YYYY-MM-DD)
   * @responseBody 200 - Array of staff registration statistics
   */
  public async getStaffRegistrationStats({ request, response }: HttpContextContract) {
    try {
      const { staffId, vendorId, branchId, startDate, endDate, per = 10, page = 1 } = request.qs()

      let query = Database.from('customers')
        .leftJoin('users as staff', 'customers.registered_by_staff_id', 'staff.id')
        .leftJoin('users as customer', 'customers.user_id', 'customer.id')
        .leftJoin('vendors', 'customers.vendor_id', 'vendors.id')
        .leftJoin('branches', 'customers.branch_id', 'branches.id')
        .select(
          'staff.id as staff_id',
          'staff.first_name as staff_first_name',
          'staff.last_name as staff_last_name',
          'staff.email as staff_email',
          'vendors.id as vendor_id',
          'vendors.name as vendor_name',
          'branches.id as branch_id',
          'branches.name as branch_name'
        )
        .count('customers.id as registrations_count')
        .whereNotNull('customers.registered_by_staff_id')
        .groupBy(
          'staff.id',
          'staff.first_name',
          'staff.last_name',
          'staff.email',
          'vendors.id',
          'vendors.name',
          'branches.id',
          'branches.name'
        )

      // Apply filters
      if (staffId) {
        query = query.where('customers.registered_by_staff_id', staffId)
      }
      if (vendorId) {
        query = query.where('customers.vendor_id', vendorId)
      }
      if (branchId) {
        query = query.where('customers.branch_id', branchId)
      }
      if (startDate) {
        query = query.where('customers.created_at', '>=', startDate)
      }
      if (endDate) {
        query = query.where('customers.created_at', '<=', endDate)
      }

      const result = await query.orderBy('registrations_count', 'desc').paginate(page, per)
      const paginationJson = result.toJSON()

      return response.json({
        data: paginationJson.data,
        meta: {
          total: paginationJson.meta.total,
          per_page: paginationJson.meta.per_page,
          current_page: paginationJson.meta.current_page,
          last_page: paginationJson.meta.last_page,
          from: paginationJson.meta.from,
          to: paginationJson.meta.to,
        },
      })
    } catch (error) {
      console.error('Staff Registration Stats Error:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to fetch staff registration statistics.',
        error: error.message,
      })
    }
  }

  /**
   * @getStaffCustomers
   * @summary Get customers registered by a specific staff member
   * @description Retrieve list of customers registered by a staff member with pagination
   * @paramPath staffId required string - Staff member ID
   * @queryParam page optional number - Page number (default: 1)
   * @queryParam per optional number - Items per page (default: 10)
   * @queryParam startDate optional string - Start date filter (YYYY-MM-DD)
   * @queryParam endDate optional string - End date filter (YYYY-MM-DD)
   * @responseBody 200 - Paginated list of customers
   */
  public async getStaffCustomers({ auth, request, response, params }: HttpContextContract) {
    try {
      const { staffId } = params
      const { page = 1, per = 10, startDate, endDate } = request.qs()

      // Ensure authenticated user can only view their own registrations or has admin privileges
      const authenticatedUser = auth.user!
      const isAdmin = await authenticatedUser.hasRole('admin')

      if (!isAdmin && authenticatedUser.id !== staffId) {
        return response.status(403).json({
          status: 'error',
          message: 'You can only view customers you registered unless you have admin privileges.',
        })
      }

      // Verify staff member exists
      const staffMember = await User.findOrFail(staffId)

      let query = Database.from('customers')
        .leftJoin('users as customer', 'customers.user_id', 'customer.id')
        .leftJoin('vendors', 'customers.vendor_id', 'vendors.id')
        .leftJoin('branches', 'customers.branch_id', 'branches.id')
        .select(
          'customer.id as customer_id',
          'customer.first_name',
          'customer.last_name',
          'customer.email',
          'customer.phone',
          'customer.status',
          'vendors.name as vendor_name',
          'branches.name as branch_name',
          'customers.active',
          'customers.created_at as registration_date'
        )
        .where('customers.registered_by_staff_id', staffId)

      // Apply date filters
      if (startDate) {
        query = query.where('customers.created_at', '>=', startDate)
      }
      if (endDate) {
        query = query.where('customers.created_at', '<=', endDate)
      }

      const result = await query.orderBy('customers.created_at', 'desc').paginate(page, per)
      const paginationJson = result.toJSON()

      return response.json({
        data: paginationJson.data,
        meta: {
          total: paginationJson.meta.total,
          per_page: paginationJson.meta.per_page,
          current_page: paginationJson.meta.current_page,
          last_page: paginationJson.meta.last_page,
          from: paginationJson.meta.from,
          to: paginationJson.meta.to,
        },
        staff: {
          id: staffMember.id,
          name: `${staffMember.firstName} ${staffMember.lastName}`,
          email: staffMember.email,
        },
      })
    } catch (error) {
      console.error('Get Staff Customers Error:', error)
      return response.status(500).json({
        status: 'error',
        message: 'Failed to fetch staff customers.',
        error: error.message,
      })
    }
  }

  /**
   * @addRole
   * @summary Add a new staff user with a specific role [Admin/Vendor Only]
   * @description Create an account for a staff user with details, password, and role. Assigns them to the calling Admin/Vendor's organization.
   * @requestBody {"firstName": "", "lastName": "", "phone": "", "email": "", "password": "", "role": ""}
   * @responseBody 201 - <User>
   * @response 400 - { status: 'error', message: 'Invalid data'}
   * @response 401 - Unauthorized
   * @response 403 - Forbidden (Insufficient permissions)
   * @response 409 - Conflict
   */
  public async addRole({ auth, request, response }: HttpContextContract) {
    const r = request.all()

    if (auth.use('api').isLoggedIn) {
      const loggedInUser = auth.user

      if (!loggedInUser) {
        return response
          .status(401)
          .json({ status: 'Unauthorized', message: 'User session invalid.' })
      }

      if (!r.email || !r.firstName || !r.lastName || !r.password || !r.role) {
        return response.badRequest({
          status: 'error',
          message: 'Missing required fields (email, firstName, lastName, password, role).',
        })
      }

      const targetRole = r.role.trim().toLowerCase()
      const targetEmail = r.email.trim().toLowerCase()
      const targetPhone = r.phone ? r.phone.replace(/^\+/, '') : null

      const existingUser = await User.query()
        .where('email', targetEmail)
        .orWhere((builder) => {
          if (targetPhone) {
            builder.where('phone', targetPhone)
          }
        })
        .first()

      if (existingUser) {
        const conflictField = existingUser.email === targetEmail ? 'Email' : 'Phone number'
        return response
          .status(409)
          .json({ status: 'error', message: `User with this ${conflictField} already exists.` })
      }

      const trx = await Database.transaction()

      try {
        await loggedInUser.load('roles')
        const loggedInUserRoles = loggedInUser.roles.map((role) => role.name)

        if (!loggedInUserRoles.includes('admin') && !loggedInUserRoles.includes('vendor')) {
          await trx.rollback()
          return response.status(403).json({
            status: 'Forbidden',
            message: 'You do not have permission to add staff roles.',
          })
        }

        const assignableRoles = ['waiter', 'manager', 'chef', 'staff']
        if (!assignableRoles.includes(targetRole)) {
          await trx.rollback()
          return response.badRequest({
            status: 'error',
            message: `Role '${r.role}' cannot be assigned.`,
          })
        }

        const vendor = await Vendor.query({ client: trx }).where('user_id', loggedInUser.id).first()

        if (!vendor) {
          await trx.rollback()
          return response
            .status(403)
            .json({ status: 'Forbidden', message: 'Your account is not associated with a vendor.' })
        }

        const branch = await Branch.query({ client: trx }).where('vendor_id', vendor.id).first()

        if (!branch) {
          await trx.rollback()
          return response
            .status(400)
            .json({ status: 'error', message: 'Vendor has no branches configured.' })
        }

        const otp = crypto.randomInt(100000, 999999)

        const newUser = await User.create(
          {
            firstName: r.firstName,
            lastName: r.lastName,
            otp,
            phone: targetPhone,
            email: targetEmail,
            password: r.password,
            status: 'Active',
          },
          { client: trx }
        )

        await newUser.useTransaction(trx).assignRole(targetRole)

        await newUser
          .useTransaction(trx)
          .related('employers')
          .attach({
            [vendor.id]: {
              identifier: crypto.randomBytes(8).toString('hex'),
              branch_id: branch.id,
            },
          })

        await newUser.useTransaction(trx).notify(new CustomerWelcomeOtp())

        await trx.commit()

        const newUserJson = newUser.toJSON()
        delete newUserJson.password
        delete newUserJson.otp

        return response.created(newUserJson)
      } catch (e) {
        await trx.rollback()
        console.error('Error in addRole:', e)
        return response
          .status(500)
          .json({ status: 'Error', message: 'Failed to add staff user.', error: e.message })
      }
    } else {
      return response.status(401).json({ status: 'Unauthorized' })
    }
  }

  /**
   * @getOrdersFromRoles
   * @summary Get orders based on user hierarchical role and access level [All Staff Roles]
   * @description Retrieves orders based on hierarchical access control - staff see assigned orders, supervisors see department orders, managers see branch orders, owners see vendor orders, admins see all orders.
   * @responseBody 200 - Paginated<Order[]> & { accessInfo: { level: string, description: string, totalAccessible: number } }
   * @response 401 - Unauthorized
   * @response 403 - Forbidden
   */
  public async getOrdersFromRoles({ auth, request, response }: HttpContextContract) {
    const { per = 30, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()

    if (auth.use('api').isLoggedIn) {
      const user = auth.user

      if (!user) {
        return response
          .status(401)
          .json({ status: 'Unauthorized', message: 'User session invalid.' })
      }

      try {
        // Use the imported hierarchical access control service

        // Build the base query with preloads
        const OrderQuery = Order.filter(filters)
          .preload('customer')
          .preload('branch')
          .preload('vendor')
          .preload('items', (itemQuery) => {
            itemQuery.preload('product')
            itemQuery.preload('modifiers')
          })
          .preload('staff')
          .preload('section')
          .preload('invoices', (iq) => iq.preload('payments'))

        // Apply role-based hierarchical filtering
        await HierarchicalAccessControlService.applyRoleBasedFilter(OrderQuery, user)

        // Get access information for response metadata
        const accessStats = await HierarchicalAccessControlService.getAccessibleOrderStats(user)

        // Execute the query with pagination
        const orders = await OrderQuery.orderBy(order, sort).paginate(page, per)

        // Handle temp orders (Pending status) - populate items from meta.temp_items
        // Process each order in the paginated result
        for (const order of orders) {
          if (order.status === 'Pending' && order.meta?.temp_items) {
            // This is a temp order - populate items from meta.temp_items
            const tempItems = order.meta.temp_items
            const productIds = Object.keys(tempItems)

            if (productIds.length > 0) {
              // Import Product model dynamically to avoid circular dependency
              const { default: Product } = await import('../../Models/Product')
              const products = await Product.query()
                .whereIn('id', productIds)
                .preload('category')
                .preload('gallery')
                .preload('forms')
                .preload('branch')
                .preload('vendor')
                .exec()

              // Create items array and assign to order
              const items = products.map((product) => {
                const quantity = tempItems[product.id]?.quantity || 0
                const productPrice = product.price || 0
                const totalItemCost = productPrice * quantity

                return {
                  id: null, // No order_items record yet
                  orderId: order.id,
                  productId: product.id,
                  quantity: quantity,
                  meta: null,
                  createdAt: order.createdAt,
                  updatedAt: order.updatedAt,
                  price: productPrice, // ✅ FIX: Set actual product price
                  status: 'pending',
                  departmentId: null,
                  assignedStaffId: null,
                  estimatedPreparationTime: null,
                  preparationStartedAt: null,
                  preparationCompletedAt: null,
                  servedAt: null,
                  priorityLevel: 1,
                  requiresSpecialAttention: false,
                  specialInstructions: null,
                  preparationNotes: null,
                  statusHistory: null,
                  qualityCheckStatus: 'not_required',
                  qualityCheckedBy: null,
                  customerModifications: null,
                  cancellationReason: null,
                  actualPreparationTime: null,
                  preparationAttempts: 1,
                  modifiers: [],
                  product: product.serialize(), // ✅ FIX: Use serialize() instead of toJSON()
                  actualPreparationTimeMinutes: null,
                  isOverdue: false,
                  preparationProgress: 0,
                  statusDisplayName: 'Pending',
                  canBeStarted: true,
                  canBeCompleted: false,
                  canBeServed: false,
                  requiresAttention: false,
                  estimatedCompletionTime: null,
                  allModifiersCompleted: true,
                  totalItemCost: totalItemCost, // ✅ FIX: Calculate actual cost
                }
              })

              // Assign items to the order instance
              order.$setRelated('items', items)
            }
          }
        }

        // Add pricing information using standardized service
        const ordersWithPricing = OrderPricingService.addPricingToPaginatedOrders(orders)

        // Enhance response with access information
        const response_data = ordersWithPricing

        return response.json({
          ...response_data,
          accessInfo: {
            level: accessStats.accessLevel,
            description: accessStats.accessDescription,
            totalAccessible: accessStats.totalOrders,
            canAccess: accessStats.canAccess,
          },
        })
      } catch (e) {
        console.error('Error in getOrdersFromRoles:', e)

        // Handle specific hierarchical access errors
        if (e.message === 'Staff record not found for user') {
          return response.status(403).json({
            status: 'Forbidden',
            message: 'Your staff record is not configured. Please contact your administrator.',
          })
        }

        return response
          .status(500)
          .json({ status: 'Error', message: 'Failed to retrieve orders.', error: e.message })
      }
    } else {
      return response.status(401).json({ status: 'Unauthorized' })
    }
  }

  /**
   * @addRoleToUser
   * @summary Assign an additional role to an existing user [Admin/Vendor Only]
   * @description Assigns a specified role (e.g., waiter, manager) to an existing user identified by user_id.
   * @requestBody {"user_id":"", "role": ""}
   * @responseBody 200 - <User>
   * @response 400 - Bad Request (Invalid role, missing fields)
   * @response 401 - Unauthorized
   * @response 403 - Forbidden (Insufficient permissions)
   * @response 404 - Not Found (Target user doesn't exist)
   * @response 409 - Conflict (User already has the role)
   */
  public async addRoleToUser({ auth, request, response }: HttpContextContract) {
    const r = request.all()

    if (auth.use('api').isLoggedIn) {
      const loggedInUser = auth.user

      if (!loggedInUser) {
        return response
          .status(401)
          .json({ status: 'Unauthorized', message: 'User session invalid.' })
      }

      if (!r.user_id || !r.role) {
        return response.badRequest({
          status: 'error',
          message: 'Missing required fields: user_id and role.',
        })
      }

      const targetUserId = r.user_id
      const targetRoleName = r.role.trim().toLowerCase()

      const trx = await Database.transaction()

      try {
        await loggedInUser.load('roles')
        const loggedInUserRoles = loggedInUser.roles.map((role) => role.name)

        if (!loggedInUserRoles.includes('admin') && !loggedInUserRoles.includes('vendor')) {
          await trx.rollback()
          return response
            .status(403)
            .json({ status: 'Forbidden', message: 'You do not have permission to assign roles.' })
        }

        const assignableRoles = ['waiter', 'manager', 'chef', 'staff']
        if (!assignableRoles.includes(targetRoleName)) {
          await trx.rollback()
          return response.badRequest({
            status: 'error',
            message: `Role '${r.role}' cannot be assigned.`,
          })
        }

        const targetUser = await User.query({ client: trx })
          .where('id', targetUserId)
          .preload('roles')
          .first()

        if (!targetUser) {
          await trx.rollback()
          return response
            .status(404)
            .json({ status: 'error', message: 'Target user does not exist.' })
        }

        const targetUserRoles = targetUser.roles.map((role) => role.name)
        if (targetUserRoles.includes(targetRoleName)) {
          await trx.rollback()
          return response
            .status(409)
            .json({ status: 'Conflict', message: `User already has the role '${targetRoleName}'.` })
        }

        if (loggedInUserRoles.includes('vendor')) {
          const vendor = await Vendor.query({ client: trx })
            .where('user_id', loggedInUser.id)
            .first()
          if (!vendor) {
            await trx.rollback()
            return response.status(403).json({
              status: 'Forbidden',
              message: 'Your vendor account is not properly configured.',
            })
          }

          const isStaffMember = await Database.from('staff')
            .where('user_id', targetUser.id)
            .where('vendor_id', vendor.id)
            .first()

          if (!isStaffMember) {
            await trx.rollback()
            return response.status(403).json({
              status: 'Forbidden',
              message: 'You can only assign roles to users within your organization.',
            })
          }
        }

        await targetUser.useTransaction(trx).assignRole(targetRoleName)

        await trx.commit()

        await targetUser.load('roles')

        const targetUserJson = targetUser.toJSON()
        delete targetUserJson.password

        return response.ok(targetUserJson)
      } catch (e) {
        await trx.rollback()
        console.error('Error in addRoleToUser:', e)
        return response
          .status(500)
          .json({ status: 'Error', message: 'Failed to assign role to user.', error: e.message })
      }
    } else {
      return response.status(401).json({ status: 'Unauthorized' })
    }
  }

  /**
   * @removeRoleFromUser
   * @summary Remove a role from an existing user [Admin/Vendor Only]
   * @description Removes a specified role (e.g., waiter, manager) from an existing user identified by user_id.
   * @requestBody {"user_id":"", "role": ""}
   * @responseBody 200 - <User>
   * @response 400 - Bad Request (Invalid role, missing fields)
   * @response 401 - Unauthorized
   * @response 403 - Forbidden (Insufficient permissions)
   * @response 404 - Not Found (Target user doesn't exist)
   */
  public async removeRoleFromUser({ auth, request, response }: HttpContextContract) {
    const r = request.all()

    if (auth.use('api').isLoggedIn) {
      const loggedInUser = auth.user

      if (!loggedInUser) {
        return response
          .status(401)
          .json({ status: 'Unauthorized', message: 'User session invalid.' })
      }

      if (!r.user_id || !r.role) {
        return response.badRequest({
          status: 'error',
          message: 'Missing required fields: user_id and role.',
        })
      }

      const targetUserId = r.user_id
      const targetRoleName = r.role.trim().toLowerCase()

      const trx = await Database.transaction()

      try {
        await loggedInUser.load('roles')
        const loggedInUserRoles = loggedInUser.roles.map((role) => role.name)

        if (!loggedInUserRoles.includes('admin') && !loggedInUserRoles.includes('vendor')) {
          await trx.rollback()
          return response
            .status(403)
            .json({ status: 'Forbidden', message: 'You do not have permission to remove roles.' })
        }

        const assignableRoles = ['waiter', 'manager', 'chef', 'staff']
        if (!assignableRoles.includes(targetRoleName)) {
          await trx.rollback()
          return response.badRequest({
            status: 'error',
            message: `Role '${r.role}' cannot be removed.`,
          })
        }

        const targetUser = await User.query({ client: trx })
          .where('id', targetUserId)
          .preload('roles')
          .first()

        if (!targetUser) {
          await trx.rollback()
          return response
            .status(404)
            .json({ status: 'error', message: 'Target user does not exist.' })
        }

        const targetUserRoles = targetUser.roles.map((role) => role.name)
        if (!targetUserRoles.includes(targetRoleName)) {
          await trx.rollback()
          return response
            .status(400)
            .json({ status: 'error', message: `User does not have the role '${targetRoleName}'.` })
        }

        if (loggedInUserRoles.includes('vendor')) {
          const vendor = await Vendor.query({ client: trx })
            .where('user_id', loggedInUser.id)
            .first()
          if (!vendor) {
            await trx.rollback()
            return response.status(403).json({
              status: 'Forbidden',
              message: 'Your vendor account is not properly configured.',
            })
          }

          const isStaffMember = await Database.from('staff')
            .where('user_id', targetUser.id)
            .where('vendor_id', vendor.id)
            .first()

          if (!isStaffMember) {
            await trx.rollback()
            return response.status(403).json({
              status: 'Forbidden',
              message: 'You can only remove roles from users within your organization.',
            })
          }
        }

        await targetUser.useTransaction(trx).revokeRole(targetRoleName)

        await trx.commit()

        await targetUser.load('roles')

        const targetUserJson = targetUser.toJSON()
        delete targetUserJson.password

        return response.ok(targetUserJson)
      } catch (e) {
        await trx.rollback()
        console.error('Error in removeRoleFromUser:', e)
        return response
          .status(500)
          .json({ status: 'Error', message: 'Failed to remove role from user.', error: e.message })
      }
    } else {
      return response.status(401).json({ status: 'Unauthorized' })
    }
  }

  /**
   * @forgot
   * @summary Forgot password
   * @description Send password reset link/OTP to user email/phone
   * @requestBody {"username": ""}
   * @response 200 - { success: true, message: "..." }
   * @response 400 - Bad Request
   * @response 404 - User not found
   * @response 403 - Account inactive/suspended
   */
  public async forgot({ request, response }: HttpContextContract) {
    const { username } = request.all()
    if (!username) {
      return response.badRequest({
        success: false,
        message: 'Username (email or phone) is required.',
      })
    }

    const trimmed = username.trim()
    const identifierField = trimmed.includes('@') ? 'email' : 'phone'
    const identifierValue =
      identifierField === 'phone' ? trimmed.replace(/^\+/, '') : trimmed.toLowerCase()

    try {
      const user = await User.query().where(identifierField, identifierValue).first()

      if (!user) {
        console.warn(`Password reset requested for non-existent user: ${identifierValue}`)
        return response.status(404).json({ success: false, message: `User not found.` })
      }

      if (user.status !== 'Active') {
        console.warn(`Password reset requested for inactive/suspended user: ${identifierValue}`)
        return response
          .status(403)
          .json({ success: false, message: `Account is ${user.status}. Cannot reset password.` })
      }

      const otp = crypto.randomInt(100000, 999999)

      await user
        .merge({
          otp: otp,
          // Add otpExpiresAt field if implementing expiry
        })
        .save()

      if (identifierField === 'email') {
        await user.notify(new SendPasswordResetLink())
      } else {
        // Consider using a specific SMS notification if SendPasswordResetLink is email-only
        await user.notify(new SendSmsOtp()) // Assuming you have an SMS OTP notifier
        // Or await user.notify(new SendPasswordResetLink()) if it handles SMS too
      }

      return response.ok({
        success: true,
        message: `Password reset instructions sent to your ${identifierField}.`,
      })
    } catch (e) {
      console.error('Forgot Password Error:', e)
      return response
        .status(500)
        .json({ success: false, message: 'An error occurred while processing your request.' })
    }
  }

  /**
   * @reset
   * @summary Reset password using OTP
   * @description Reset password using OTP received via email/SMS
   * @requestBody {"username": "", "password": "", "otp": ""}
   * @responseBody 200 - { user: User, success: true, message: "...", type: string, token: string, refreshToken?: string }
   * @response 400 - Invalid data/OTP
   * @response 404 - User not found
   */
  public async reset({ auth, request, response }: HttpContextContract) {
    const { username, password, otp } = request.all()

    const trimmed = username.trim()
    try {
      const username = trimmed.includes('@') ? 'email' : 'phone'
      const user = await User.query().where(username, trimmed.replace(/^\+/, '')).firstOrFail()

      if (user?.otp?.toString() === otp?.toString()) {
        await user?.merge({ password, otp: null }).save()

        const jwt = await auth.use('api').attempt(trimmed, password, {
          expiresIn: '7days',
        })

        return response.json({
          ...{ user, success: true, message: 'Password reset successful' },
          ...jwt,
        })
      }

      return response.badRequest({ success: false, message: 'Invalid OTP' })
    } catch (e) {
      console.error(e)
      return response.status(400).json({ success: false, message: 'Invalid data supplied' })
    }
  }

  /**
   * @sendOtp
   * @summary Send OTP to user (logged in or specified)
   * @description Sends an OTP for verification purposes (e.g., phone verification).
   * @requestBody {"username": ""} - Required if user is not logged in
   * @response 200 - { status: 'success', message: '...' }
   * @response 400/404/500 - Error
   */
  public async sendOtp({ request, auth, response }: HttpContextContract) {
    const { username: postedUsername } = request.all()
    let user: User | null = null
    let targetIdentifier = ''

    const otp = crypto.randomInt(100000, 999999)
    // const otpExpiry = DateTime.now().plus({ minutes: 10 }); // Add if implementing expiry

    try {
      if (await auth.check()) {
        user = auth.user!
        if (user.phone) {
          targetIdentifier = user.phone
        } else if (user.email) {
          targetIdentifier = user.email
        } else {
          return response.badRequest({
            status: 'fail',
            message: 'User has no phone or email configured.',
          })
        }
        console.log(`Sending OTP to logged-in user: ${user.id} via ${targetIdentifier}`)
      } else if (postedUsername) {
        const trimmed = postedUsername.trim()
        const identifierField = trimmed.includes('@') ? 'email' : 'phone'
        const identifierValue =
          identifierField === 'phone' ? trimmed.replace(/^\+/, '') : trimmed.toLowerCase()
        targetIdentifier = identifierValue

        user = await User.query().where(identifierField, identifierValue).first()

        if (!user) {
          console.warn(`Send OTP requested for non-existent user: ${identifierValue}`)
          return response.status(404).json({ status: 'fail', message: 'User not found.' })
        }
        console.log(`Sending OTP to specified user: ${user.id} via ${targetIdentifier}`)
      } else {
        return response.badRequest({
          status: 'fail',
          message: 'Username required or user must be logged in.',
        })
      }

      if (user) {
        await user
          .merge({
            otp: otp,
            // otpExpiresAt: otpExpiry // Add if implementing expiry
          })
          .save()

        // Choose notification based on target type
        if (targetIdentifier.includes('@')) {
          // Need an Email OTP notifier if SendSmsOtp is SMS-only
          // await user.notify(new SendEmailOtpNotification());
          // Placeholder: Using SendSmsOtp, assuming it might handle email or as fallback
          await user.notify(new SendSmsOtp())
        } else {
          await user.notify(new SendSmsOtp())
        }

        return response.ok({
          status: 'success',
          message: `OTP sent successfully to ${targetIdentifier}.`,
        })
      } else {
        // Should not be reachable if logic above is correct
        return response.status(404).json({ status: 'fail', message: 'User not found.' })
      }
    } catch (e) {
      console.error('Send OTP Error:', e)
      return response.internalServerError({
        status: 'fail',
        message: 'Failed to send OTP.',
        error: e.message,
      })
    }
  }

  /**
   * @verifyOtp
   * @summary Verify OTP
   * @description Verify OTP for logged in user
   * @requestBody {"username": "", "otp": ""}
   */
  public async verifyOtp({ auth, request, response }: HttpContextContract) {
    const { otp, createAccountOtp, username: postedUsername } = request.all()

    if (auth.use('api').isLoggedIn) {
      const user = auth.user

      if (user?.otp?.toString() === otp?.toString()) {
        await user
          ?.merge({
            otp: null,
            status: 'Active',
            ...(createAccountOtp && { phoneVerifiedAt: DateTime.now() }),
          })
          .save()

        return response.json({ status: 'success', message: 'OTP verified successfuly' })
      } else {
        return response.badRequest({ status: 'fail', message: 'Invalid OTP' })
      }
    } else if (postedUsername) {
      const trimmed = postedUsername.trim()
      const username = trimmed.includes('@') ? 'email' : 'phone'

      const user = await User.query().where(username, trimmed.replace(/^\+/, '')).firstOrFail()

      if (user.otp?.toString() === otp?.toString()) {
        await user
          .merge({
            otp: null,
            status: 'Active',
            ...(createAccountOtp && { phoneVerifiedAt: DateTime.now() }),
          })
          .save()

        return response.json({ status: 'success', message: 'OTP verified successfuly' })
      } else {
        return response.badRequest({ status: 'fail', message: 'Invalid OTP' })
      }
    }
  }

  /**
   * @me
   * @summary Get current user details
   * @description Get details for the currently logged-in user, including roles and unread notifications.
   * @responseBody 200 - <User> & { notifications: Notification[] }
   * @response 401 - Unauthorized
   */
  public async me({ auth, response }: HttpContextContract) {
    try {
      await auth.use('api').authenticate()
      const user = auth.user!

      await user.load('roles')
      const notifications = await user.unreadNotifications() // Assuming mixin provides this

      const userJson = user.toJSON()
      delete userJson.password
      delete userJson.otp

      // Return user details along with notifications
      return response.ok({ ...userJson, notifications })
    } catch (error) {
      console.error('Error fetching current user:', error)
      // Let Adonis handle the 401 from authenticate() failure
      throw error
    }
  }

  /**
   * @logout
   * @summary Logout user
   * @description Revokes the current API token used for authentication.
   * @response 200 - { revoked: true }
   * @response 401 - Unauthorized
   */
  public async logout({ auth, response }: HttpContextContract) {
    try {
      await auth.use('api').authenticate() // Ensure user is actually logged in
      await auth.use('api').revoke()
      return response.ok({ revoked: true })
    } catch (error) {
      console.error('Logout error:', error)
      if (error.code === 'E_UNAUTHORIZED_ACCESS') {
        // User wasn't logged in anyway
        return response.unauthorized({ revoked: false, message: 'Not logged in.' })
      }
      // Unexpected error during revoke
      return response.internalServerError({ revoked: false, message: 'Failed to logout.' })
    }
  }

  /**
   * @socialRedirect
   * @summary Get url redirect for social login
   * @description Get url redirect for social login
   *
   * @paramPath provider - The login provider to be used - @enum(google, facebook, apple)
   * @response 200 - { url: string }
   * @response 406 - Not Acceptable (Already logged in)
   * @response 500 - Internal Server Error
   */
  public async socialRedirect({ ally, auth, params, response }: HttpContextContract) {
    try {
      if (await auth.check()) {
        return response.status(406).json({ message: 'Already logged in.' })
      }

      // Ensure provider is valid before using it
      const providerName = params.provider as 'google' | 'facebook' | 'apple'

      // Validate provider
      const supportedProviders = ['google', 'facebook', 'apple']
      if (!supportedProviders.includes(providerName)) {
        return response.badRequest({ message: `Unsupported provider: ${providerName}` })
      }

      // Check if provider is configured
      const providerConfigs = {
        google: Env.get('GOOGLE_CLIENT_ID') && Env.get('GOOGLE_CLIENT_SECRET'),
        facebook: Env.get('FACEBOOK_CLIENT_ID') && Env.get('FACEBOOK_CLIENT_SECRET'),
        apple: Env.get('APPLE_CLIENT_ID') && Env.get('APPLE_CLIENT_SECRET'),
      }

      if (!providerConfigs[providerName]) {
        return response.badRequest({
          message: `${providerName} login is not configured on this server.`,
        })
      }

      const provider = ally.use(providerName)

      // Generate the OAuth URL - let Ally handle the redirect_uri automatically
      const url = await provider.stateless().redirectUrl()

      console.log(`[Social Redirect] Generated URL for ${providerName}:`, url)

      return response.ok({ url })
    } catch (error) {
      console.error(`Social Redirect Error (${params.provider}):`, error)
      // Handle case where provider config is missing
      if (error.message.includes('E_INVALID_ALLY_DRIVER')) {
        return response.badRequest({
          message: `Social provider '${params.provider}' is not configured.`,
        })
      }
      return response.internalServerError({
        message: `Failed to generate social login redirect for ${params.provider}.`,
      })
    }
  }

  /**
   * @socialCallback
   * @summary Callback for social login
   * @description Handles the callback from the social provider after user authentication.
   * @paramPath provider - The login provider used - @enum(google, facebook, apple)
   * @response 200 - { user: User, isNewUser: boolean, type: string, token: string, refreshToken?: string }
   * @response 400 - Bad Request (Social auth error)
   * @response 500 - Internal Server Error
   */
  public async socialCallback({ ally, auth, params, response, request }: HttpContextContract) {
    const providerName = params.provider as 'google' | 'facebook' | 'apple' // Cast for safety
    console.log(`[Social Callback] Starting callback process for provider: ${providerName}`)
    console.log(`[Social Callback] Request URL: ${request.url()}`)
    console.log(`[Social Callback] Request Query Parameters:`, request.qs())

    const socialProvider = ally.use(providerName).stateless()
    console.log(`[Social Callback] Provider initialized:`, {
      provider: providerName,
      config: {
        clientId: Env.get('GOOGLE_CLIENT_ID'),
        callbackUrl: Env.get('APP_URL') + '/v1/auth/social/google/callback',
      },
    })

    const trx = await Database.transaction()
    console.log(`[Social Callback] Database transaction started`)

    try {
      console.log(`[Social Callback] Checking access denied status`)
      if (socialProvider.accessDenied()) {
        await trx.rollback()
        console.log(`[Social Callback] Access denied by provider`)
        return response.badRequest({ message: 'Social login access denied by provider.' })
      }

      console.log(`[Social Callback] Checking for provider errors`)
      if (socialProvider.hasError()) {
        await trx.rollback()
        const error = socialProvider.getError()
        console.error(`[Social Callback] Provider Error Details:`, {
          error,
          provider: providerName,
          requestUrl: request.url(),
          queryParams: request.qs(),
          headers: request.headers(),
        })
        return response.badRequest({
          message: `Error during social login with ${providerName}.`,
          error: error,
        })
      }

      console.log(`[Social Callback] Attempting to get user from provider`)
      const socialUser = await socialProvider.user()
      console.log(`[Social Callback] Social user data received:`, {
        id: socialUser.id,
        email: socialUser.email,
        name: socialUser.name,
        nickName: socialUser.nickName,
        avatarUrl: socialUser.avatarUrl,
        emailVerified: (socialUser as any).emailVerified,
        token: socialUser.token,
      })

      if (!socialUser.email) {
        await trx.rollback()
        console.error(`[Social Callback] Missing Email Error:`, {
          socialUser,
          provider: providerName,
        })
        return response.badRequest({
          message: `Email address not provided by ${providerName}. Cannot proceed.`,
        })
      }

      console.log(`[Social Callback] Searching for existing user with email: ${socialUser.email}`)
      let user = await User.query({ client: trx }).where('email', socialUser.email).first()

      // Check for existing social account
      console.log(`[Social Callback] Checking for existing social account`)
      const existingAccount = await Account.query({ client: trx })
        .where('provider', providerName)
        .where('providerId', socialUser.id)
        .preload('user')
        .first()

      let isNewUser = false
      const isEmailVerifiedByProvider = (socialUser as any).emailVerified === true
      console.log(`[Social Callback] User lookup result:`, {
        found: !!user,
        isNewUser,
        isEmailVerifiedByProvider,
      })

      if (!user) {
        console.log(`[Social Callback] Creating new user`)
        isNewUser = true
        user = await User.create(
          {
            firstName: socialUser.nickName || socialUser.name.split(' ')[0] || 'Social',
            lastName: socialUser.name.split(' ').slice(1).join(' ') || 'User',
            email: socialUser.email,
            emailVerifiedAt: isEmailVerifiedByProvider ? DateTime.now() : null,
            status: 'Active',
            password: crypto.randomBytes(16).toString('hex'),
          },
          { client: trx }
        )

        console.log(`[Social Callback] Assigning customer role to new user`)
        await user.useTransaction(trx).assignRole('customer')
      } else {
        console.log(`[Social Callback] Updating existing user if needed`)
        const needsUpdate =
          !user.firstName || !user.lastName || (!user.emailVerifiedAt && isEmailVerifiedByProvider)
        if (needsUpdate) {
          console.log(`[Social Callback] Updating user details`)
          await user
            .useTransaction(trx)
            .merge({
              firstName:
                user.firstName || socialUser.nickName || socialUser.name.split(' ')[0] || 'Social',
              lastName: user.lastName || socialUser.name.split(' ').slice(1).join(' ') || 'User',
              emailVerifiedAt:
                user.emailVerifiedAt || (isEmailVerifiedByProvider ? DateTime.now() : null),
            })
            .save()
        }
      }

      // 🔥 CRITICAL FIX: Create Account record if it doesn't exist
      if (!existingAccount) {
        console.log(`[Social Callback] Creating social account link`)
        await Account.create(
          {
            provider: providerName,
            providerId: socialUser.id,
            userId: user.id,
            active: true,
          },
          { client: trx }
        )
      }

      console.log(`[Social Callback] Generating API token`)
      const jwt = await auth.use('api').generate(user, {
        expiresIn: '7days',
      })

      await trx.commit()
      console.log(`[Social Callback] Database transaction committed`)

      await user.load('roles')
      console.log(`[Social Callback] User roles loaded`)

      const userJson = user.toJSON()
      delete userJson.password

      console.log(`[Social Callback] Successfully completed social login`)

      // Option 1: Return JSON (current)
      if (request.header('accept')?.includes('application/json')) {
        return response.ok({
          user: userJson,
          isNewUser: isNewUser,
          type: jwt.type,
          token: jwt.token,
          refreshToken: (jwt as any).refreshToken,
        })
      }

      // Option 2: Redirect to frontend with token (easier for frontend)
      const frontendUrl = Env.get('FRONTEND_URL', Env.get('APP_URL'))
      const redirectUrl = `${frontendUrl}/auth/callback?token=${jwt.token}&isNewUser=${isNewUser}`
      return response.redirect(redirectUrl)
    } catch (error) {
      await trx.rollback()
      console.error(`[Social Callback] Error in callback process:`, {
        error,
        errorMessage: error.message,
        errorStack: error.stack,
        provider: providerName,
        requestUrl: request.url(),
        queryParams: request.qs(),
        headers: request.headers(),
      })

      if (error.message.includes('E_INVALID_ALLY_STATE')) {
        return response.badRequest({ message: 'Invalid social login state. Please try again.' })
      }
      return response.internalServerError({
        message: `An error occurred during social login processing for ${providerName}.`,
        error: error.message,
      })
    }
  }

  /**
   * @socialMobileAuth
   * @summary Mobile Google Sign-In Authentication
   * @description Handles Google Sign-In authentication for mobile apps using idToken
   * @requestBody {"idToken": "google_id_token", "provider": "google"}
   * @response 200 - { user: User, isNewUser: boolean, type: string, token: string, refreshToken?: string }
   * @response 400 - Bad Request (Invalid token or missing data)
   * @response 500 - Internal Server Error
   */
  public async socialMobileAuth({ auth, request, response }: HttpContextContract) {
    const { idToken, provider = 'google' } = request.only(['idToken', 'provider'])

    console.log(`[Mobile Social Auth] Starting mobile auth for provider: ${provider}`)

    if (!idToken) {
      return response.badRequest({ message: 'idToken is required for mobile authentication.' })
    }

    // Validate provider
    if (provider !== 'google') {
      return response.badRequest({
        message: `Provider ${provider} is not supported for mobile auth.`,
      })
    }

    // Check if Google is configured
    if (!Env.get('GOOGLE_CLIENT_ID')) {
      return response.badRequest({
        message: 'Google authentication is not configured on this server.',
      })
    }

    const trx = await Database.transaction()
    console.log(`[Mobile Social Auth] Database transaction started`)

    try {
      // Verify the Google ID token
      const { OAuth2Client } = require('google-auth-library')
      const client = new OAuth2Client(Env.get('GOOGLE_CLIENT_ID'))

      console.log(`[Mobile Social Auth] Verifying Google ID token`)
      const ticket = await client.verifyIdToken({
        idToken: idToken,
        audience: Env.get('GOOGLE_CLIENT_ID'),
      })

      const payload = ticket.getPayload()
      console.log(`[Mobile Social Auth] Token verified, payload received:`, {
        sub: payload?.sub,
        email: payload?.email,
        name: payload?.name,
        email_verified: payload?.email_verified,
      })

      if (!payload || !payload.email) {
        await trx.rollback()
        return response.badRequest({ message: 'Invalid Google token or missing email.' })
      }

      // Check for existing social account
      console.log(`[Mobile Social Auth] Checking for existing social account`)
      const existingAccount = await Account.query({ client: trx })
        .where('provider', 'google')
        .where('providerId', payload.sub)
        .preload('user')
        .first()

      let user: User
      let isNewUser = false
      const isEmailVerified = payload.email_verified === true

      if (existingAccount) {
        console.log(`[Mobile Social Auth] Found existing social account`)
        user = existingAccount.user
      } else {
        // Check if user exists by email
        const existingUser = await User.query({ client: trx }).where('email', payload.email).first()

        if (!existingUser) {
          console.log(`[Mobile Social Auth] Creating new user`)
          isNewUser = true
          user = await User.create(
            {
              firstName: payload.given_name || payload.name?.split(' ')[0] || 'Google',
              lastName:
                payload.family_name || payload.name?.split(' ').slice(1).join(' ') || 'User',
              email: payload.email,
              emailVerifiedAt: isEmailVerified ? DateTime.now() : null,
              status: 'Active',
              password: crypto.randomBytes(16).toString('hex'),
            },
            { client: trx }
          )

          console.log(`[Mobile Social Auth] Assigning customer role to new user`)
          await user.useTransaction(trx).assignRole('customer')
        } else {
          console.log(`[Mobile Social Auth] Found existing user by email`)
          user = existingUser
          // Update user if needed
          const needsUpdate =
            !user.firstName || !user.lastName || (!user.emailVerifiedAt && isEmailVerified)
          if (needsUpdate) {
            console.log(`[Mobile Social Auth] Updating existing user details`)
            await user
              .useTransaction(trx)
              .merge({
                firstName:
                  user.firstName || payload.given_name || payload.name?.split(' ')[0] || 'Google',
                lastName:
                  user.lastName ||
                  payload.family_name ||
                  payload.name?.split(' ').slice(1).join(' ') ||
                  'User',
                emailVerifiedAt: user.emailVerifiedAt || (isEmailVerified ? DateTime.now() : null),
              })
              .save()
          }
        }

        // Create social account link
        console.log(`[Mobile Social Auth] Creating social account link`)
        await Account.create(
          {
            provider: 'google',
            providerId: payload.sub,
            userId: user.id,
            active: true,
          },
          { client: trx }
        )
      }

      // Generate JWT token
      console.log(`[Mobile Social Auth] Generating API token`)
      const jwt = await auth.use('api').generate(user, {
        expiresIn: '7days',
      })

      await trx.commit()
      console.log(`[Mobile Social Auth] Database transaction committed`)

      await user.load('roles')
      console.log(`[Mobile Social Auth] User roles loaded`)

      const userJson = user.toJSON()
      delete userJson.password

      console.log(`[Mobile Social Auth] Successfully completed mobile social login`)
      return response.ok({
        user: userJson,
        isNewUser: isNewUser,
        type: jwt.type,
        token: jwt.token,
        expiresAt: DateTime.now().plus({ days: 7 }).toISO(),
      })
    } catch (error) {
      await trx.rollback()
      console.error(`[Mobile Social Auth] Error in mobile auth process:`, {
        error,
        errorMessage: error.message,
        errorStack: error.stack,
        provider,
      })

      if (error.message.includes('Invalid token')) {
        return response.badRequest({ message: 'Invalid Google ID token.' })
      }

      return response.internalServerError({
        message: 'An error occurred during mobile social authentication.',
        error: error.message,
      })
    }
  }

  /**
   * @getRoleActions
   * @summary Get allowed action card names based on user role
   * @description Returns a list of action card names for the authenticated user's role, based on configuration.
   * @responseBody 200 - ["ActionName1", "ActionName2"]
   * @response 401 - Unauthorized
   * @security BearerAuth
   */
  public async getRoleActions({ auth, response }: HttpContextContract) {
    const user = auth.user!
    await user.load('roles') // Loads the roles relation

    if (!user.roles || user.roles.length === 0) {
      console.warn(`User ${user.id} has no roles assigned.`) // Log a warning
      return response.json([]) // Return empty for users without roles
    }

    const primaryRole = user.roles[0].name.toLowerCase()

    // Get the list of allowed action IDs for the role, falling back to '*' if the role isn't specifically mapped
    const allowedActionIds = roleActionMapping[primaryRole] ?? roleActionMapping['*']

    // Filter the master list of action card objects based on the allowed IDs
    const allowedCards = allActionCards.filter((card) => allowedActionIds.includes(card.id))

    // Extract only the names from the filtered list
    const allowedActionNames = allowedCards.map((card) => card.name)

    return response.json(allowedActionNames)
  }

  /**
   * @refresh
   * @summary Refresh access token
   * @description Generate a new access token using a refresh token
   * @requestBody {"refreshToken": ""}
   * @responseBody 200 - { type: string, token: string, refreshToken: string }
   * @response 400 - Invalid refresh token
   * @response 401 - Unauthorized
   */
  public async refresh({ auth, request, response }: HttpContextContract) {
    const { refreshToken } = request.all()

    if (!refreshToken) {
      return response.badRequest({ error: 'Refresh token is required' })
    }

    try {
      // Find the token in the database
      const token = await Database.from('api_tokens')
        .where('refresh_token', refreshToken)
        .where('refresh_token_expires_at', '>', DateTime.now().toSQL())
        .first()

      if (!token) {
        return response.unauthorized({ error: 'Invalid or expired refresh token' })
      }

      // Get the user
      const user = await User.findOrFail(token.user_id)

      // Generate new tokens
      const jwt = await auth.use('api').generate(user, {
        expiresIn: '7days',
      })

      // Generate a new refresh token
      const newRefreshToken = crypto.randomBytes(32).toString('hex')

      // Update the token in the database
      await Database.from('api_tokens')
        .where('id', token.id)
        .update({
          token: jwt.token,
          refresh_token: newRefreshToken,
          expires_at: DateTime.now().plus({ days: 7 }).toSQL(),
          refresh_token_expires_at: DateTime.now().plus({ days: 30 }).toSQL(),
        })

      return response.ok({
        type: jwt.type,
        token: jwt.token,
        refreshToken: newRefreshToken,
      })
    } catch (error) {
      console.error('Refresh token error:', error)
      return response.internalServerError({ error: 'Failed to refresh token' })
    }
  }
} // End of AuthController class
