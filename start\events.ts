/*
|--------------------------------------------------------------------------
| Preloaded File
|--------------------------------------------------------------------------
|
| Any code written inside this file will be executed during the application
| boot.
|
*/
import Event from '@ioc:Adonis/Core/Event'
import Database from '@ioc:Adonis/Lucid/Database'
import Application from '@ioc:Adonis/Core/Application'

Event.on('db:query', (query) => {
  if (!Application.inProduction) {
    Database.prettyPrint(query)
  }
})

// Order completion event listeners
Event.on('order:completed', 'OrderCompletionListener.onOrderCompleted')
Event.on('customer:order:ready', 'OrderCompletionListener.onCustomerOrderReady')
Event.on('staff:order:ready', 'OrderCompletionListener.onStaffOrderReady')

// Status change event listeners
Event.on('order:status:changed', 'OrderCompletionListener.onOrderStatusChanged')
Event.on('item:status:changed', 'OrderCompletionListener.onItemStatusChanged')
Event.on('modifier:status:changed', 'OrderCompletionListener.onModifierStatusChanged')
Event.on('item:overdue', 'OrderCompletionListener.onItemOverdue')
Event.on('staff:assigned', 'OrderCompletionListener.onStaffAssigned')
Event.on('department:workload:updated', 'OrderCompletionListener.onDepartmentWorkloadUpdated')
