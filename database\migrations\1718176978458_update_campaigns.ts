import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'campaigns'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add display and position fields
      table.integer('display_duration').notNullable().defaultTo(8) // Default 8 seconds
      table.integer('position').nullable() // Carousel order

      // Add billing fields
      table.enum('billing_type', ['fixed', 'percentage']).defaultTo('fixed')
      table.decimal('billing_amount', 10, 2).defaultTo(0)
      table.string('currency').defaultTo('KES')

      // Add approval fields
      table.string('approved_by').references('id').inTable('users').onDelete('SET NULL').nullable()
      table.timestamp('approved_at', { useTz: true }).nullable()
      table.text('rejection_reason').nullable()
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove new fields
      table.dropColumn('display_duration')
      table.dropColumn('position')
      table.dropColumn('billing_type')
      table.dropColumn('billing_amount')
      table.dropColumn('currency')
      table.dropColumn('approved_by')
      table.dropColumn('approved_at')
      table.dropColumn('rejection_reason')
    })
  }
} 