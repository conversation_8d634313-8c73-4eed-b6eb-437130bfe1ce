import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Subscription from 'App/Models/Subscription'
import CustomerNewSubscription from 'App/Notifications/Customer/CustomerNewSubscription'

export default class SubscriptionsController {
  /**
   * @name Subscription management
   * @index
   * @summary List all Subscriptions
   * @description List all Subscriptions, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({}: HttpContextContract) {
    return 'Hello world'
  }

  /**
   * @store
   *
   * @summary Create a Subscription
   *
   * @description Create a Subscription with their details
   * @requestBody {"userId": "", "subscriptionPlanId": ""}
   * @responseBody 200 - <Subscription>
   * @returns Subscription
   *
   */

  public async store({ request, response }: HttpContextContract) {
    const { userId, subscriptionPlanId } = request.all()

    const subscription = await Subscription.create({
      userId,
      subscriptionPlanId,
    })

    // Load the subscription plan to get the required details for notification
    await subscription.load('planlan')

    // Create notification data with proper structure
    const notificationData = {
      id: subscription.id,
      planName: subscription.planlan.name || 'Subscription Plan',
      amount: subscription.planlan.amount || subscription.planlan.price || 0,
      currency: subscription.planlan.currency || 'KES',
      billingCycle: subscription.planlan.billingCycle || 'monthly',
      startDate: subscription.createdAt.toJSDate(),
      nextBillingDate: subscription.createdAt.plus({ months: 1 }).toJSDate()
    }

    await subscription.user.notify(new CustomerNewSubscription(notificationData))

    return response.status(201).json(subscription)
  }
}
