import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import SubscriptionPlan from 'App/Models/SubscriptionPlan'
import NotificationBillingTier from 'App/Models/NotificationBillingTier'

export default class extends BaseSeeder {
  public async run() {
    // Create subscription plans
    const plans = [
      {
        name: 'Basic',
        slug: 'basic',
        details: 'Essential features for small businesses',
        price: 999,
        duration: 1,
        duration_type: 'month',
        is_active: true,
        meta: {
          features: [
            'Basic order management',
            'Up to 100 orders per month',
            'Basic analytics',
            'Email support',
            '1 staff account'
          ]
        }
      },
      {
        name: 'Professional',
        slug: 'professional',
        details: 'Advanced features for growing businesses',
        price: 2499,
        duration: 1,
        duration_type: 'month',
        is_active: true,
        meta: {
          features: [
            'Advanced order management',
            'Up to 500 orders per month',
            'Advanced analytics',
            'Priority email support',
            'Up to 5 staff accounts',
            'Custom branding',
            'API access'
          ]
        }
      },
      {
        name: 'Enterprise',
        slug: 'enterprise',
        details: 'Complete solution for large businesses',
        price: 4999,
        duration: 1,
        duration_type: 'month',
        is_active: true,
        meta: {
          features: [
            'Complete order management',
            'Unlimited orders',
            'Advanced analytics & reporting',
            '24/7 priority support',
            'Unlimited staff accounts',
            'Custom branding',
            'API access',
            'Dedicated account manager',
            'Custom integrations'
          ]
        }
      }
    ]

    // Create notification billing tiers
    const notificationTiers = [
      {
        name: 'Starter',
        code: 'STARTER',
        description: 'Basic notification package for small businesses',
        base_price: 499,
        currency: 'KES',
        monthly_limit: 1000,
        overage_rate: 0.50,
        sms_rate: 1.50,
        email_rate: 0.10,
        push_rate: 0.05,
        is_active: true,
        meta: {
          features: ['Basic notification templates', 'Standard delivery speed']
        }
      },
      {
        name: 'Business',
        code: 'BUSINESS',
        description: 'Enhanced notification package for growing businesses',
        base_price: 1499,
        currency: 'KES',
        monthly_limit: 5000,
        overage_rate: 0.45,
        sms_rate: 1.25,
        email_rate: 0.08,
        push_rate: 0.04,
        is_active: true,
        meta: {
          features: [
            'Advanced notification templates',
            'Priority delivery',
            'Custom branding',
            'Analytics dashboard'
          ]
        }
      },
      {
        name: 'Enterprise',
        code: 'ENTERPRISE',
        description: 'Complete notification solution for large businesses',
        base_price: 4999,
        currency: 'KES',
        monthly_limit: 20000,
        overage_rate: 0.40,
        sms_rate: 1.00,
        email_rate: 0.05,
        push_rate: 0.03,
        is_active: true,
        meta: {
          features: [
            'Custom notification templates',
            'Highest priority delivery',
            'Custom branding',
            'Advanced analytics',
            'Dedicated support',
            'Custom integrations'
          ]
        }
      }
    ]

    // Create subscription plans
    for (const plan of plans) {
      await SubscriptionPlan.create(plan)
    }

    // Create notification billing tiers
    for (const tier of notificationTiers) {
      await NotificationBillingTier.create(tier)
    }
  }
} 