import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { bind } from '@adonisjs/route-model-binding'
import Service from 'App/Models/Service'

export default class ServiceSpecialitiesController {
  @bind()

  /**
   * @index
   * @summary Show all Specialities
   * @version 1.0.0
   * @description  ServiceSpecialities management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract, service: Service) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const typeQuery = service.related('specialities').query().filter(filters)

    return await typeQuery.orderBy(order, sort).paginate(page, per)
  }

  @bind()

  /**
   * @store
   * @summary Create a ServiceSpecialities
   * @description Create a ServiceSpecialities with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   */
  public async store({ request, response }: HttpContextContract, service: Service) {
    const { name, details, serviceId } = request.all()
    const type = await service.related('specialities').create({ name, details, serviceId })

    const image = request.file('image')
    if (image) {
      type.merge({ image: Attachment.fromFile(image) })
    }

    await type.save()

    return response.json(type)
  }
}
