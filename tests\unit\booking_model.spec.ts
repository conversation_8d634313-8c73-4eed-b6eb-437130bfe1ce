import test from 'japa'
import { DateTime } from 'luxon'
import Booking, { BookingStatus } from 'App/Models/Booking'
import Product from 'App/Models/Product'
import Branch from 'App/Models/Branch'
import Vendor from 'App/Models/Vendor'
import User from 'App/Models/User'

test.group('Booking Model', (group) => {
  let vendor: Vendor
  let branch: Branch
  let customer: User
  let product: Product

  group.beforeEach(async () => {
    vendor = await Vendor.create({
      name: 'Test Spa',
      email: '<EMAIL>',
      phone: '1234567890',
      active: true
    })

    branch = await Branch.create({
      vendorId: vendor.id,
      name: 'Main Branch',
      details: 'Main spa location',
      location: {
        name: 'Test Location',
        coordinates: { lat: 0, lng: 0 }
      },
      phone: '1234567890'
    })

    customer = await User.create({
      name: 'Test Customer',
      email: '<EMAIL>',
      phone: '1234567890',
      password: 'password123'
    })

    product = await Product.create({
      name: 'Relaxing Massage',
      details: 'A relaxing full-body massage',
      price: 100,
      vendorId: vendor.id,
      branchId: branch.id,
      active: true,
      type: 'Service'
    })
  })

  test('should create a booking with all required fields', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.PENDING,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    assert.exists(booking.id)
    assert.exists(booking.confirmationCode)
    assert.equal(booking.status, BookingStatus.PENDING)
    assert.equal(booking.durationMinutes, 60)
    assert.equal(booking.totalPrice, 100)
    assert.isTrue(booking.isPending)
    assert.isTrue(booking.isActive)
    assert.isTrue(booking.isUpcoming)
  })

  test('should generate unique confirmation code', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking1 = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.PENDING,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    const booking2 = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart: appointmentStart.plus({ hours: 2 }),
      appointmentEnd: appointmentEnd.plus({ hours: 2 }),
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.PENDING,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    assert.exists(booking1.confirmationCode)
    assert.exists(booking2.confirmationCode)
    assert.notEqual(booking1.confirmationCode, booking2.confirmationCode)
    assert.equal(booking1.confirmationCode!.length, 8)
    assert.equal(booking2.confirmationCode!.length, 8)
  })

  test('should confirm a pending booking', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.PENDING,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    await booking.confirm()

    assert.equal(booking.status, BookingStatus.CONFIRMED)
    assert.exists(booking.confirmedAt)
    assert.isTrue(booking.isConfirmed)
    assert.isTrue(booking.isActive)
  })

  test('should not confirm a non-pending booking', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.COMPLETED,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    try {
      await booking.confirm()
      assert.fail('Should have thrown an error')
    } catch (error) {
      assert.include(error.message, 'Cannot confirm booking with status')
    }
  })

  test('should cancel a booking', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.CONFIRMED,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    await booking.cancel(customer.id, 'Customer requested cancellation')

    assert.equal(booking.status, BookingStatus.CANCELLED)
    assert.exists(booking.cancelledAt)
    assert.equal(booking.cancelledBy, customer.id)
    assert.equal(booking.cancellationReason, 'Customer requested cancellation')
    assert.isTrue(booking.isCancelled)
    assert.isFalse(booking.isActive)
  })

  test('should start service for confirmed booking', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.CONFIRMED,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    await booking.startService()

    assert.equal(booking.status, BookingStatus.IN_PROGRESS)
    assert.isTrue(booking.isInProgress)
    assert.isTrue(booking.isActive)
  })

  test('should complete service for in-progress booking', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.IN_PROGRESS,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    await booking.completeService()

    assert.equal(booking.status, BookingStatus.COMPLETED)
    assert.isTrue(booking.isCompleted)
    assert.isFalse(booking.isActive)
  })

  test('should validate booking data', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = new Booking()
    booking.fill({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.PENDING,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    const validation = await booking.validateBooking()
    assert.isTrue(validation.valid)
    assert.equal(validation.errors.length, 0)
  })

  test('should detect invalid booking data', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.minus({ hours: 1 }) // End before start

    const booking = new Booking()
    booking.fill({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.PENDING,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    const validation = await booking.validateBooking()
    assert.isFalse(validation.valid)
    assert.isAbove(validation.errors.length, 0)
  })

  test('should calculate total duration including buffer', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const booking = await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.PENDING,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    assert.equal(booking.totalDurationMinutes, 75) // 60 + 15
    assert.equal(booking.appointmentTimeRange, '10:00 - 11:00')
  })

  group.afterEach(async () => {
    await Booking.query().delete()
    await product.delete()
    await branch.delete()
    await vendor.delete()
    await customer.delete()
  })
})
