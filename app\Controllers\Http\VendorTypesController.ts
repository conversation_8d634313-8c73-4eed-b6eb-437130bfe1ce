import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import VendorType from '../../Models/VendorType'
import { bind } from '@adonisjs/route-model-binding'

export default class VendorTypesController {
  /**
   * @name VendorType management
   * @index
   * @summary List all VendorTypes
   * @description List all VendorTypes, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */

  public async index({ request }: HttpContextContract) {
    const { per = 50, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const typeQuery = VendorType.filter(filters)

    return await typeQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a VendorType
   * @description Create a VendorType with their details (name and details)
   * @requestBody {"name": "", "details": "", "serviceId": ""}
   * @responseBody 200 - <VendorType>
   */

  public async store({ request }: HttpContextContract) {
    const { name, details, serviceId } = request.all()
    const type = new VendorType()

    type.fill({ name, details, serviceId })

    const image = request.file('image')

    if (image) {
      type.image = Attachment.fromFile(image)
    }

    return await type.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a single VendorType
   * @description Show a VendorType with their details (name and details)
   * @paramPath id required number - VendorType ID
   * @responseBody 200 - <VendorType>
   * @response 404 - VendorType not found
   */
  public async show({ response }: HttpContextContract, type: VendorType) {
    return response.json(type)
  }

  @bind()

  /**
   * @update
   * @summary Update a VendorType
   * @description Update a VendorType with their details (name and details)
   * @paramPath id required number - VendorType ID
   * @requestBody <VendorType>
   * @responseBody 200 - <VendorType>
   * @response 404 - VendorType not found
   */
  public async update({ request, response }: HttpContextContract, type: VendorType) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await type.merge(input).save()

    return response.json(type)
  }

  @bind()

  /**
   * @destroy
   * @summary destroy a vendortype
   * @rresponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, type: VendorType) {
    return await type.delete()
  }
}
