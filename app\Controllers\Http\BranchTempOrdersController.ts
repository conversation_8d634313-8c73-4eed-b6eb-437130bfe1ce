import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// Updated: Use unified Order model instead of TempOrder
import Order from '../../Models/Order'
import Product from 'App/Models/Product'
// Ensure 'bind' is imported for use in 'index' and 'store' methods
import { bind } from '@adonisjs/route-model-binding'
import Lot from 'App/Models/Lot'
import User from 'App/Models/User'
import Branch from 'App/Models/Branch'

/**
 * @name Temp TempOrder management
 * @version 1.0.0
 * @description TempOrder management for the application
 */
export default class BranchTempOrdersController {
  // Keep @bind() as the route for index likely includes a branch parameter for binding
  @bind()
  /**
   * @index
   * @summary List all TempOrders for a specific Branch
   * @description List all TempOrders for the bound branch, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract, branch: Branch) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()

    // Updated: Query unified orders table for temp orders (status = 'Pending')
    const TempOrderQuery = Order.filter(filters)
      // Use the bound branch.id for filtering
      .where('branchId', branch.id)
      .where('status', 'Pending') // Only get temp orders
      .preload('customer')
      .preload('branch')
      .preload('vendor')
      .preload('staff')
      .preload('section')

    // Get temp orders with pagination
    const tempOrders = await TempOrderQuery.orderBy(order, sort).paginate(page, per)

    // Updated: Load items for each temp order from meta.temp_items and products
    const tempOrdersWithItems = await Promise.all(
      tempOrders.map(async (tempOrder) => {
        const tempItems = tempOrder.meta?.temp_items || {}
        const items = []

        // Load product details for each temp item
        for (const [productId, itemData] of Object.entries(tempItems)) {
          const quantity = typeof itemData === 'object' ? itemData.quantity : itemData
          const product = await Product.find(productId)

          if (product) {
            items.push({
              ...product.toJSON(),
              quantity: quantity,
            })
          }
        }

        return {
          ...tempOrder.toJSON(),
          items: items,
        }
      })
    )

    return {
      ...tempOrders.toJSON(),
      data: tempOrdersWithItems,
    }
  }

  // Removed @bind() decorator as the route '/v1/gettemporders/bystaffid' doesn't have bindable parameters
  /**
   * @ordersbystaffid
   * @summary List all TempOrders filtered by staffId, status, branchId via query params
   * @description List all TempOrders, paginated, using query string filters.
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery staffId - Staff ID filter
   * @paramQuery branchId - Branch ID filter
   * @paramQuery status - Status filter
   */
  // Removed the ', branch: Branch' parameter from the signature
  public async ordersbystaffid({ request }: HttpContextContract) {
    // Extract query parameters, including branchId, staffId, status
    const {
      per = 10,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      staffId,
      branchId,
      status,
      ...filters
    } = request.qs()

    // Updated: Build the query using unified orders table for temp orders
    const TempOrderQuery = Order.filter(filters)
      .where('branchId', branchId) // Use branchId from query string
      .where('staffId', staffId) // Use staffId from query string
      .where('status', 'Pending') // Force Pending status for temp orders (ignore status param)
      .preload('customer')
      .preload('branch')
      .preload('vendor')
      .preload('staff')
      .preload('section')

    // Get temp orders with pagination
    const tempOrders = await TempOrderQuery.orderBy(order, sort).paginate(page, per)

    // Updated: Load items for each temp order from meta.temp_items and products
    const tempOrdersWithItems = await Promise.all(
      tempOrders.map(async (tempOrder) => {
        const tempItems = tempOrder.meta?.temp_items || {}
        const items = []

        // Load product details for each temp item
        for (const [productId, itemData] of Object.entries(tempItems)) {
          const quantity = typeof itemData === 'object' ? itemData.quantity : itemData
          const product = await Product.find(productId)

          if (product) {
            items.push({
              ...product.toJSON(),
              quantity: quantity,
            })
          }
        }

        return {
          ...tempOrder.toJSON(),
          items: items,
        }
      })
    )

    return {
      ...tempOrders.toJSON(),
      data: tempOrdersWithItems,
    }
  }

  // Keep @bind() as the route for store likely includes a branch parameter for binding
  @bind()
  /**
   * @store
   * @summary Create a TempOrder for a specific Branch
   * @description Create a TempOrder associated with the bound branch.
   * @requestBody {"staffId": "", "vendorId": "", "branchId": "(optional override)", "sectionId": "", "lotId": "", "action": "", "type": "", "delivery": "", "status": "", "meta": {}, "userId": "(optional)", "items": {}} - <TempOrder Payload>
   * @responseBody 200 - <TempOrder>
   */
  public async store({ request, response, auth }: HttpContextContract, branch: Branch) {
    try {
      const {
        vendorId,
        // branchId explicitly from payload (optional, defaults to bound branch.id)
        sectionId,
        lotId,
        action,
        type,
        delivery,
        status,
        meta,
        staffId: postedStaffId,
        userId = null, // Optional user ID from payload
        items = {},
      } = request.all()

      // Use branchId from payload if provided, otherwise default to the bound branch's ID
      const effectiveBranchId = request.input('branchId', branch.id)

      let staffId = postedStaffId

      // Find staff based on lot or branch availability if not provided
      if (!staffId) {
        const staff = lotId
          ? await Lot.find(lotId).then(
              async (lot) => await lot?.related('staff').query().firstOrFail()
            )
          : await User.query()
              .whereHas('employers', (q) => {
                // Filter available staff within the effective branch
                q.where('branch_id', effectiveBranchId)
                q.wherePivot('online', true)
              })
              .first()

        staffId = staff?.id
      }

      // Updated: Create temp order in unified orders table with status='Pending'
      const unifiedMeta = {
        ...meta,
        // Store items in temp_items for unified system
        temp_items: Object.keys(items).reduce(
          (acc, productId) => ({
            ...acc,
            [productId]: {
              quantity: items[productId],
            },
          }),
          {}
        ),
      }

      const order = await Order.create({
        userId: userId ? userId : auth.user?.id, // Use provided userId or authenticated user's ID
        staffId,
        vendorId,
        branchId: effectiveBranchId, // Use the effective branch ID
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status: 'Pending', // Always Pending for temp orders in unified system
        meta: unifiedMeta,
      })

      // Ensure pricing is calculated and stored for new temp order
      await order.ensureTempOrderPricing()

      // Load customer relationship for further operations
      await order.load('customer')

      // Sync customer's relationship with the effective branch
      // Ensure customer is marked as active for this branch and vendor
      order.customer.related('branches').sync(
        {
          [effectiveBranchId]: {
            active: true,
            vendor_id: order.vendorId,
            branch_id: effectiveBranchId,
          },
        },
        false // Use false to avoid detaching other relationships
      )

      // Return the created order
      return response.json(order)
    } catch (error) {
      // Log the error for debugging
      console.error('Error creating TempOrder:', error)

      // Return a user-friendly error response
      return response.badRequest({
        error: 'Failed to create temporary order.',
        details: error.message,
      })
    }
  }
}
