export default {
  path: __dirname + '/../',
  title: 'App In App API Docs',
  version: '1.0.0',
  description: 'This is the API documentation for the App In App backend',
  tagIndex: 2,
  ignore: ['/swagger', '/docs', '/v1/kplc'],
  debug: true,
  preferredPutPatch: 'PUT',
  common: {
    parameters: {
      sortable: [
        // {
        //   in: 'query',
        //   name: 'per',
        //   schema: { type: 'number', example: 15 },
        // },
        // {
        //   in: 'query',
        //   name: 'page',
        //   schema: { type: 'number', example: 1 },
        // },
        // {
        //   in: "query",
        //   name: "sort",
        //   schema: { type: "string", example: "ASC" },
        // }
      ],
    },
    headers: {},
  },
  info: {
    title: 'App In App API Docs',
    version: '1.0.0',
    description: 'This is the API documentation for the App In App application',
  },
  snakeCase: false,
  securitySchemes: {
    BearerAuth: {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT'
    }
  },
  authMiddlewares: ['auth', 'auth:api'],
  defaultSecurityScheme: 'BearerAuth',
  persistAuthorization: true,
  showFullPath: false,
  components: {
    schemas: {
      FileUpload: {
        type: 'object',
        properties: {
          vendor_setup_csv: {
            type: 'string',
            format: 'binary',
            description: 'CSV file containing vendor data'
          }
        },
        required: ['vendor_setup_csv']
      }
    },
    requestBodies: {
      FileUpload: {
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                vendor_setup_csv: {
                  type: 'string',
                  format: 'binary',
                  description: 'CSV file containing vendor data'
                }
              },
              required: ['vendor_setup_csv']
            }
          }
        }
      }
    }
  }
}
