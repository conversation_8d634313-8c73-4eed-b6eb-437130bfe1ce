import Scheduler from '@ioc:Adonis/Addons/Scheduler'
import Branch from 'App/Models/Branch'

import { Queue } from '@ioc:Rlanz/Queue'

/**
 * @see https://www.npmjs.com/package/adonisjs-scheduler
 */

Scheduler.call(async () => {
  const branches = await Branch.query()
    .whereHas('settings', (q) => q.where('name', 'hotelplus'))
    .exec()

  branches.forEach(async (branch) => {
    Queue.dispatch('App/Jobs/SyncProduct', {
      channel: 'hotelplus',
      vendor: branch.id,
    })
  })
}).daily()

Scheduler.call(async () => {
  const branches = await Branch.query()
    .whereHas('settings', (q) => q.where('name', 'hotelplus'))
    .exec()

  branches.forEach(async (branch) => {
    Queue.dispatch('App/Jobs/SyncStaff', {
      channel: 'hotelplus',
      vendor: branch.id,
    })
  })
}).daily()

// Scheduler.call(async () => {
//   const branches = await Branch.query()
//     .whereHas('settings', (q) => q.where('name', 'hotelplus'))
//     .exec()

//   branches.forEach(async (branch) => {
//     Queue.dispatch('App/Jobs/SyncOrders', {
//       channel: 'hotelplus',
//       vendor: branch.id,
//     })
//   })
// }).everyMinute()
