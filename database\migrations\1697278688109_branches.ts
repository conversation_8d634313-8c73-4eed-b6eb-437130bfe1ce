import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'branches'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('name').notNullable()
      table.string('code').nullable()
      table.text('details').nullable()
      table.string('email', 255).nullable()
      table.string('phone', 13).nullable()
      table.json('image').nullable()
      table.json('location').notNullable()
      table.geometry('geom').nullable()
      table.json('hours').nullable()

      table.string('slug').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
