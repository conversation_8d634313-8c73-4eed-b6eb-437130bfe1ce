import Order from 'App/Models/Order'
import OrderItem from 'App/Models/OrderItem'
import Product from 'App/Models/Product'
import PackagingOption from 'App/Models/PackagingOption'

export default class PackagingChargeService {
  /**
   * Calculate packaging charges for an order based on products with packaging options
   */
  public static async calculatePackagingCharges(order: Order): Promise<{
    packagingCharges: Record<string, number>
    totalPackagingCharges: number
  }> {
    const packagingCharges: Record<string, number> = {}
    let totalPackagingCharges = 0

    // Only apply packaging charges for specific delivery types
    const applicableDeliveryTypes = ['Takeaway', 'Delivery', 'Selfpick']
    if (!applicableDeliveryTypes.includes(order.delivery)) {
      return { packagingCharges, totalPackagingCharges }
    }

    // Load order items with products and their packaging options
    await order.load('items', (itemQuery) => {
      itemQuery.preload('product', (productQuery) => {
        productQuery.preload('packagingOptions')
      })
    })

    // Calculate packaging charges for each item
    for (const item of order.items) {
      if (item.product && item.product.packagingOptions) {
        for (const packagingOption of item.product.packagingOptions) {
          const chargeName = `Packaging: ${packagingOption.name}`
          const chargeAmount = packagingOption.price * item.quantity

          if (packagingCharges[chargeName]) {
            packagingCharges[chargeName] += chargeAmount
          } else {
            packagingCharges[chargeName] = chargeAmount
          }

          totalPackagingCharges += chargeAmount
        }
      }
    }

    return { packagingCharges, totalPackagingCharges }
  }

  /**
   * Apply packaging charges to an order and save them in the meta field
   */
  public static async applyPackagingCharges(order: Order): Promise<void> {
    const { packagingCharges, totalPackagingCharges } = await this.calculatePackagingCharges(order)

    if (totalPackagingCharges > 0) {
      // Get existing meta and charges
      const meta = order.meta || {}
      const existingCharges = meta.charges || {}

      // Add packaging charges to existing charges
      const updatedCharges = {
        ...existingCharges,
        ...packagingCharges,
      }

      // Update order meta
      meta.charges = updatedCharges
      order.meta = meta
      await order.save()
    }
  }

  /**
   * Calculate packaging charges for specific order items (used for order creation)
   */
  public static async calculatePackagingChargesForItems(
    items: Array<{ productId: string; quantity: number }>,
    deliveryType: string
  ): Promise<{
    packagingCharges: Record<string, number>
    totalPackagingCharges: number
  }> {
    const packagingCharges: Record<string, number> = {}
    let totalPackagingCharges = 0

    // Only apply packaging charges for specific delivery types
    const applicableDeliveryTypes = ['Takeaway', 'Delivery', 'Selfpick']
    if (!applicableDeliveryTypes.includes(deliveryType)) {
      return { packagingCharges, totalPackagingCharges }
    }

    // Load products with their packaging options
    const productIds = items.map(item => item.productId)
    const products = await Product.query()
      .whereIn('id', productIds)
      .preload('packagingOptions')

    // Create a map for quick product lookup
    const productMap = new Map(products.map(product => [product.id, product]))

    // Calculate packaging charges for each item
    for (const item of items) {
      const product = productMap.get(item.productId)
      if (product && product.packagingOptions) {
        for (const packagingOption of product.packagingOptions) {
          const chargeName = `Packaging: ${packagingOption.name}`
          const chargeAmount = packagingOption.price * item.quantity

          if (packagingCharges[chargeName]) {
            packagingCharges[chargeName] += chargeAmount
          } else {
            packagingCharges[chargeName] = chargeAmount
          }

          totalPackagingCharges += chargeAmount
        }
      }
    }

    return { packagingCharges, totalPackagingCharges }
  }

  /**
   * Get packaging charge breakdown for an order
   */
  public static async getPackagingChargeBreakdown(order: Order): Promise<{
    packagingCharges: Array<{
      name: string
      amount: number
      productName: string
      quantity: number
    }>
    totalPackagingCharges: number
  }> {
    const packagingCharges: Array<{
      name: string
      amount: number
      productName: string
      quantity: number
    }> = []
    let totalPackagingCharges = 0

    // Only show packaging charges for applicable delivery types
    const applicableDeliveryTypes = ['Takeaway', 'Delivery', 'Selfpick']
    if (!applicableDeliveryTypes.includes(order.delivery)) {
      return { packagingCharges, totalPackagingCharges }
    }

    // Load order items with products and their packaging options
    await order.load('items', (itemQuery) => {
      itemQuery.preload('product', (productQuery) => {
        productQuery.preload('packagingOptions')
      })
    })

    // Get detailed packaging charge breakdown
    for (const item of order.items) {
      if (item.product && item.product.packagingOptions) {
        for (const packagingOption of item.product.packagingOptions) {
          const chargeAmount = packagingOption.price * item.quantity

          packagingCharges.push({
            name: packagingOption.name,
            amount: chargeAmount,
            productName: item.product.name,
            quantity: item.quantity,
          })

          totalPackagingCharges += chargeAmount
        }
      }
    }

    return { packagingCharges, totalPackagingCharges }
  }
}
