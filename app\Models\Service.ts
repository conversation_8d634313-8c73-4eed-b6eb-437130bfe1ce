import { DateTime } from 'luxon'
import {
  column,
  <PERSON><PERSON>ode<PERSON>,
  beforeCreate,
  has<PERSON>any,
  HasMany,
  hasManyThr<PERSON>,
  HasManyThrough,
  belongsTo,
  BelongsTo,
  ManyToMany,
  manyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import ServiceFilter from './Filters/ServiceFilter'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import { ulid } from 'ulidx'
import VendorType from './VendorType'
import ProductType from './ProductType'
import VendorCategory from './VendorCategory'
import ProductCategory from './ProductCategory'
import Product from './Product'
import Vendor from './Vendor'
import Task from './Task'
import Speciality from './Speciality'
import ServiceAction from './ServiceAction'

export default class Service extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => ServiceFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public details: string

  @column()
  public slug: string

  @column()
  public taskId: string

  @column()
  public active: boolean

  @column()
  public order: number

  @column()
  public featured: boolean

  @attachment({ folder: 'services', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime

  @beforeCreate()
  public static async generateUlid(service: Service) {
    service.id = ulid().toLowerCase()
  }

  @beforeCreate()
  public static async slugify(service: Service) {
    service.slug =
      service.name.toLowerCase().replace('& ', '').replace(/ /g, '-') + '-' + service.taskId
  }

  @hasMany(() => VendorType)
  public vendorTypes: HasMany<typeof VendorType>

  @hasMany(() => Speciality)
  public specialities: HasMany<typeof Speciality>

  @hasManyThrough([() => VendorCategory, () => VendorType])
  public vendorCategories: HasManyThrough<typeof VendorCategory>

  @hasMany(() => Product)
  public products: HasMany<typeof Product>

  @manyToMany(() => Vendor, {
    pivotTable: 'vendor_services',
    pivotColumns: ['active'],
  })
  public vendors: ManyToMany<typeof Vendor>

  @hasMany(() => Vendor)
  public providers: HasMany<typeof Vendor>

  @hasMany(() => ProductType)
  public productTypes: HasMany<typeof ProductType>

  @hasManyThrough([() => ProductCategory, () => ProductType])
  public productCategories: HasManyThrough<typeof ProductCategory>

  @belongsTo(() => Task)
  public task: BelongsTo<typeof Task>

  @hasMany(() => ServiceAction)
  public actions: HasMany<typeof ServiceAction>

  public async isActionAllowed(action: string): Promise<boolean> {
    // Direct query to avoid TypeScript relationship typing issues
    const activeAction = await ServiceAction.query()
      .where('service_id', this.id)
      .where('action', action)
      .where('is_active', true)
      .first()

    return !!activeAction
  }
}
