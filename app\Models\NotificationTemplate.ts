import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class NotificationTemplate extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public type: string

  @column()
  public name: string

  @column()
  public subject: string

  @column()
  public body: string

  @column()
  public variables: string[]

  @column()
  public channels: string[]

  @column()
  public isActive: boolean

  @column()
  public meta: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  public static async getTemplate(type: string, name: string): Promise<NotificationTemplate | null> {
    return await this.query()
      .where('type', type)
      .where('name', name)
      .where('is_active', true)
      .first()
  }

  public formatMessage(data: Record<string, any>): { subject: string; body: string } {
    let subject = this.subject
    let body = this.body

    // Replace variables in subject and body
    for (const [key, value] of Object.entries(data)) {
      const regex = new RegExp(`{{${key}}}`, 'g')
      subject = subject.replace(regex, String(value))
      body = body.replace(regex, String(value))
    }

    return { subject, body }
  }
} 