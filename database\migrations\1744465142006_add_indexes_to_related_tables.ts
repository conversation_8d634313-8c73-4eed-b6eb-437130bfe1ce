import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  public async up() {
    // Staff table indexes
    this.schema.alterTable('staff', (table) => {
      table.index('user_id')
      table.index('branch_id')
      table.index('vendor_id')
    })

    // Order items table indexes
    this.schema.alterTable('order_items', (table) => {
      table.index('order_id')
      table.index('product_id')
    })
  }

  public async down() {
    // Remove staff table indexes
    this.schema.alterTable('staff', (table) => {
      table.dropIndex('user_id')
      table.dropIndex('branch_id')
      table.dropIndex('vendor_id')
    })

    // Remove order items table indexes
    this.schema.alterTable('order_items', (table) => {
      table.dropIndex('order_id')
      table.dropIndex('product_id')
    })
  }
} 