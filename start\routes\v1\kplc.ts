import Route from '@ioc:Adonis/Core/Route'

Route.group(() => {
  Route.get('authorize', 'KplcController.authorize').as('authorize')
  Route.get('verify-staff-identity', 'KplcController.verifyStaffIdentity').as('verifyStaffIdentity')
  Route.get('verify-contractor-identity', 'KplcController.verifyContractorIdentity').as(
    'verifyContractorIdentity'
  )
  Route.get('get-id-account', 'KplcController.getIdAccount').as('getIdAccount')
  Route.get('get-statement-pdf', 'KplcController.getStatementPdf').as('getStatementPdf')
  Route.get('get-account-balance', 'KplcController.getAccountBalance').as('getAccountBalance')
  Route.get('get-list-bills', 'KplcController.getListOfBills').as('getListOfBills')
  Route.get('get-specific-bill-text', 'KplcController.getSpecificBillText').as(
    'getSpecificBillText'
  )
  Route.get('get-specific-bill-pdf', 'KplcController.getSpecificBillPdf').as('getSpecificBillPdf')
  Route.get('authenticate-account-number', 'KplcController.authenticateAccountNumber').as(
    'authenticateAccountNumber'
  )
  Route.get('authenticate-meter-number', 'KplcController.authenticateMeterNumber').as(
    'authenticateMeterNumber'
  )
  Route.get('query-bill', 'KplcController.queryBill').as('queryBill')
  Route.get('check-self-reading-eligibility', 'KplcController.checkSelfReadingEligibility').as(
    'checkSelfReadingEligibility'
  )
  Route.get('register-self-reading', 'KplcController.registerSelfReading').as('registerSelfReading')
  Route.get('submit-self-reading', 'KplcController.submitSelfReading').as('submitSelfReading')
  Route.get('deregister-self-reading', 'KplcController.deregisterSelfReading').as(
    'deregisterSelfReading'
  )
})
  .prefix('v1/kplc')
  .as('v1.kplc')
