import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Mail from '@ioc:Adonis/Addons/Mail'
import MiaArrivalFlight from 'App/Models/Flights/MiaArrivalFlight'

/**
 * @summary MIA Arrival Flights
 * @description Controller for managing arrival flights at Kisumu International Airport (MIA).
 */
export default class MiaArrivalFlightsController {
  /**
   * @index
   *
   * @summary Fetch MIA Arrival Flights
   * @description Retrieves a list of arrival flights at MIA, optionally filtered by airline and origin airport.
   *
   * @paramQuery page - The page number for pagination.
   * @paramQuery per - The number of records per page.
   *
   * @responseBody 200 - <MiaArrivalFlight[]> - A paginated list of MiaArrivalFlight objects.
   */
  public async index({ request, response }: HttpContextContract) {
    try {
      const { page, per, ...filters } = request.qs()
      const flightQuery = MiaArrivalFlight.filter(filters)
      const flights = await flightQuery.paginate(page, per)

      return response.json(flights)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('MIA Arrival Flights Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }

  /**
   * @show
   *
   * @summary Show a Flight
   * @description Retrieves details of a specific arrival flight at MIA.
   *
   * @pathParam id - The ID of the flight to retrieve.
   *
   * @responseBody 200 - Details of the requested <MiaArrivalFlight> object.
   * @response 404 - If the flight with the given ID is not found.
   */
  public async show({ response, params }: HttpContextContract) {
    try {
      const flight = await MiaArrivalFlight.findOrFail(params.id)

      return response.json(flight)
    } catch (error) {
      await Mail.send((message) => {
        message
          .from('<EMAIL>', 'KAA API')
          .to('<EMAIL>')
          .subject('MIA Arrival Flight Error')
          .htmlView('emails/errors/server', { error })
      })
    }
  }
}
