import React, { useState, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/router'
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ArrowLeft, Settings, RefreshCw, Bell, Users, BarChart3 } from 'lucide-react'
import DepartmentWorkloadDashboard from '@/components/DepartmentWorkloadDashboard'
import { useWebSocket } from '@/hooks/useWebSocket'
import { useAuth } from '@/hooks/useAuth' // Assuming you have an auth hook

interface Department {
  id: string
  name: string
  description: string
  vendor_id: string
  branch_id?: string
  active: boolean
  average_preparation_time: number
  current_workload: number
  capacity_utilization: number
}

interface Notification {
  id: string
  type: string
  title: string
  message: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  created_at: string
  requires_acknowledgment: boolean
}

const DepartmentDashboardPage: React.FC = () => {
  const router = useRouter()
  const { departmentId } = router.query
  const { user, isAuthenticated } = useAuth()
  
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null)
  const [availableDepartments, setAvailableDepartments] = useState<Department[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // WebSocket connection for real-time updates
  const { connected, connectionStatus, lastError } = useWebSocket({
    autoConnect: true,
    reconnection: true
  })

  // API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333/api/v1'

  // Get auth token
  const getAuthToken = () => {
    return localStorage.getItem('auth_token') || ''
  }

  // Fetch departments available to the user
  const fetchDepartments = async () => {
    try {
      const token = getAuthToken()
      const response = await fetch(`${API_BASE_URL}/departments`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch departments')
      }

      const data = await response.json()
      setAvailableDepartments(data.data || [])

      // Set selected department if departmentId is in URL
      if (departmentId && data.data) {
        const dept = data.data.find((d: Department) => d.id === departmentId)
        if (dept) {
          setSelectedDepartment(dept)
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch departments')
    }
  }

  // Fetch user notifications
  const fetchNotifications = async () => {
    try {
      const token = getAuthToken()
      const response = await fetch(`${API_BASE_URL}/notifications`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }

      const data = await response.json()
      setNotifications(data.notifications || [])
    } catch (err) {
      console.error('Error fetching notifications:', err)
    }
  }

  // Acknowledge notification
  const acknowledgeNotification = async (notificationId: string) => {
    try {
      const token = getAuthToken()
      const response = await fetch(`${API_BASE_URL}/notifications/${notificationId}/acknowledge`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        setNotifications(prev => prev.filter(n => n.id !== notificationId))
      }
    } catch (err) {
      console.error('Error acknowledging notification:', err)
    }
  }

  // Handle department selection
  const handleDepartmentChange = (departmentId: string) => {
    const dept = availableDepartments.find(d => d.id === departmentId)
    if (dept) {
      setSelectedDepartment(dept)
      router.push(`/dashboard/department/${departmentId}`, undefined, { shallow: true })
    }
  }

  // Initialize data
  useEffect(() => {
    if (isAuthenticated) {
      setLoading(true)
      Promise.all([
        fetchDepartments(),
        fetchNotifications()
      ]).finally(() => {
        setLoading(false)
      })
    }
  }, [isAuthenticated, departmentId])

  // Handle real-time notifications
  useEffect(() => {
    const handleNotification = (event: CustomEvent) => {
      const notification = event.detail
      setNotifications(prev => [notification, ...prev].slice(0, 10)) // Keep only latest 10
    }

    window.addEventListener('websocket-notification', handleNotification as EventListener)
    
    return () => {
      window.removeEventListener('websocket-notification', handleNotification as EventListener)
    }
  }, [])

  // Redirect if not authenticated
  if (!isAuthenticated) {
    router.push('/login')
    return null
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => window.location.reload()} className="mt-4">
          Retry
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/dashboard')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold">Department Management</h1>
            <p className="text-muted-foreground">
              Real-time workload tracking and staff management
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Connection Status */}
          <Badge variant={connected ? 'success' : 'destructive'}>
            {connectionStatus}
          </Badge>

          {/* Notifications */}
          {notifications.length > 0 && (
            <div className="relative">
              <Button variant="outline" size="sm">
                <Bell className="w-4 h-4" />
                {notifications.length > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs">
                    {notifications.length}
                  </Badge>
                )}
              </Button>
            </div>
          )}

          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* WebSocket Error Alert */}
      {lastError && (
        <Alert variant="destructive">
          <AlertDescription>
            WebSocket Error: {lastError}
          </AlertDescription>
        </Alert>
      )}

      {/* Department Selection */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Select Department</h2>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Select
              value={selectedDepartment?.id || ''}
              onValueChange={handleDepartmentChange}
            >
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Choose a department" />
              </SelectTrigger>
              <SelectContent>
                {availableDepartments.map(dept => (
                  <SelectItem key={dept.id} value={dept.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{dept.name}</span>
                      <Badge variant={dept.active ? 'success' : 'secondary'}>
                        {dept.capacity_utilization}% capacity
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {selectedDepartment && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="w-4 h-4" />
                <span>Avg prep time: {selectedDepartment.average_preparation_time}min</span>
                <BarChart3 className="w-4 h-4 ml-2" />
                <span>Workload: {selectedDepartment.current_workload} items</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notifications Panel */}
      {notifications.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">Recent Notifications</h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {notifications.slice(0, 3).map(notification => (
                <div
                  key={notification.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Badge variant={
                        notification.priority === 'critical' ? 'destructive' :
                        notification.priority === 'high' ? 'warning' : 'default'
                      }>
                        {notification.priority}
                      </Badge>
                      <span className="font-medium">{notification.title}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {notification.message}
                    </p>
                  </div>
                  {notification.requires_acknowledgment && (
                    <Button
                      size="sm"
                      onClick={() => acknowledgeNotification(notification.id)}
                    >
                      Acknowledge
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Dashboard */}
      {selectedDepartment ? (
        <DepartmentWorkloadDashboard
          departmentId={selectedDepartment.id}
          departmentName={selectedDepartment.name}
          vendorId={selectedDepartment.vendor_id}
          branchId={selectedDepartment.branch_id}
          refreshInterval={30000}
        />
      ) : (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="space-y-4">
              <Users className="w-16 h-16 mx-auto text-muted-foreground" />
              <h3 className="text-xl font-medium">Select a Department</h3>
              <p className="text-muted-foreground">
                Choose a department from the dropdown above to view its workload dashboard
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default DepartmentDashboardPage
