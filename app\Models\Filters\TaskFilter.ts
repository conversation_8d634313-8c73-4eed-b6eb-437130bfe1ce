import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Task from '../Task'

export default class TaskFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Task, Task>

  public active(value: boolean): void {
    this.$query.where('active', value)
  }

  public with(relations: string) {
    relations.split(',').map((relation: 'services') => {
      this.$query.preload(relation)
    })
  }
}
