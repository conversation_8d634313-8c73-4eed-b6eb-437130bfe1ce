/**
 * Notification Types
 * 
 * Defines specific notification types for better categorization
 * and handling in the frontend.
 */
export class NotificationType {
  // Billing & Payment Types
  static readonly PENDING_BILL = 'PENDING_BILL'
  static readonly BILL_OVERDUE = 'BILL_OVERDUE'
  static readonly PAYMENT_RECEIVED = 'PAYMENT_RECEIVED'
  static readonly PAYMENT_FAILED = 'PAYMENT_FAILED'
  static readonly INVOICE_GENERATED = 'INVOICE_GENERATED'
  static readonly STATEMENT_AVAILABLE = 'STATEMENT_AVAILABLE'
  static readonly PAYMENT_REMINDER = 'PAYMENT_REMINDER'

  // Order Types
  static readonly ORDER_CREATED = 'ORDER_CREATED'
  static readonly ORDER_STATUS_CHANGE = 'ORDER_STATUS_CHANGE'
  static readonly ORDER_READY = 'ORDER_READY'
  static readonly ORDER_DELIVERED = 'ORDER_DELIVERED'
  static readonly ORDER_CANCELLED = 'ORDER_CANCELLED'
  static readonly TEMP_ORDER_CREATED = 'TEMP_ORDER_CREATED'
  static readonly TEMP_ORDER_APPROVED = 'TEMP_ORDER_APPROVED'
  static readonly TEMP_ORDER_REJECTED = 'TEMP_ORDER_REJECTED'

  // Promotion Types
  static readonly SPECIAL_OFFER = 'SPECIAL_OFFER'
  static readonly RATE_UPDATE = 'RATE_UPDATE'
  static readonly PRODUCT_PROMOTION = 'PRODUCT_PROMOTION'
  static readonly SEASONAL_OFFER = 'SEASONAL_OFFER'
  static readonly FLASH_SALE = 'FLASH_SALE'
  static readonly BROADCAST_ANNOUNCEMENT = 'BROADCAST_ANNOUNCEMENT'

  // Account Types
  static readonly WELCOME = 'WELCOME'
  static readonly ACCOUNT_VERIFICATION = 'ACCOUNT_VERIFICATION'
  static readonly PROFILE_INCOMPLETE = 'PROFILE_INCOMPLETE'
  static readonly PASSWORD_RESET = 'PASSWORD_RESET'
  static readonly PASSWORD_CHANGED = 'PASSWORD_CHANGED'
  static readonly SUBSCRIPTION_CREATED = 'SUBSCRIPTION_CREATED'
  static readonly SUBSCRIPTION_EXPIRING = 'SUBSCRIPTION_EXPIRING'
  static readonly SUBSCRIPTION_RENEWED = 'SUBSCRIPTION_RENEWED'

  // System Types
  static readonly SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE'
  static readonly SYSTEM_UPDATE = 'SYSTEM_UPDATE'
  static readonly SERVICE_DISRUPTION = 'SERVICE_DISRUPTION'
  static readonly FEATURE_ANNOUNCEMENT = 'FEATURE_ANNOUNCEMENT'

  // Support Types
  static readonly SUPPORT_TICKET_CREATED = 'SUPPORT_TICKET_CREATED'
  static readonly SUPPORT_TICKET_UPDATED = 'SUPPORT_TICKET_UPDATED'
  static readonly SUPPORT_TICKET_RESOLVED = 'SUPPORT_TICKET_RESOLVED'

  // OTP & Security Types
  static readonly OTP_VERIFICATION = 'OTP_VERIFICATION'
  static readonly SECURITY_ALERT = 'SECURITY_ALERT'
  static readonly LOGIN_ATTEMPT = 'LOGIN_ATTEMPT'

  /**
   * Get all billing-related notification types
   */
  static getBillingTypes(): string[] {
    return [
      this.PENDING_BILL,
      this.BILL_OVERDUE,
      this.PAYMENT_RECEIVED,
      this.PAYMENT_FAILED,
      this.INVOICE_GENERATED,
      this.STATEMENT_AVAILABLE,
      this.PAYMENT_REMINDER
    ]
  }

  /**
   * Get all order-related notification types
   */
  static getOrderTypes(): string[] {
    return [
      this.ORDER_CREATED,
      this.ORDER_STATUS_CHANGE,
      this.ORDER_READY,
      this.ORDER_DELIVERED,
      this.ORDER_CANCELLED,
      this.TEMP_ORDER_CREATED,
      this.TEMP_ORDER_APPROVED,
      this.TEMP_ORDER_REJECTED
    ]
  }

  /**
   * Get all promotion-related notification types
   */
  static getPromotionTypes(): string[] {
    return [
      this.SPECIAL_OFFER,
      this.RATE_UPDATE,
      this.PRODUCT_PROMOTION,
      this.SEASONAL_OFFER,
      this.FLASH_SALE,
      this.BROADCAST_ANNOUNCEMENT
    ]
  }

  /**
   * Get default priority for notification type
   */
  static getDefaultPriority(notificationType: string): string {
    // High priority types
    const highPriorityTypes = [
      this.BILL_OVERDUE,
      this.PAYMENT_FAILED,
      this.ORDER_CANCELLED,
      this.SYSTEM_MAINTENANCE,
      this.SERVICE_DISRUPTION,
      this.SECURITY_ALERT
    ]

    // Low priority types
    const lowPriorityTypes = [
      this.SPECIAL_OFFER,
      this.PRODUCT_PROMOTION,
      this.SEASONAL_OFFER,
      this.FLASH_SALE,
      this.BROADCAST_ANNOUNCEMENT,
      this.FEATURE_ANNOUNCEMENT
    ]

    if (highPriorityTypes.includes(notificationType)) {
      return 'HIGH'
    }

    if (lowPriorityTypes.includes(notificationType)) {
      return 'LOW'
    }

    return 'MEDIUM'
  }

  /**
   * Check if notification type is valid
   */
  static isValid(notificationType: string): boolean {
    const allTypes = [
      ...this.getBillingTypes(),
      ...this.getOrderTypes(),
      ...this.getPromotionTypes(),
      this.WELCOME,
      this.ACCOUNT_VERIFICATION,
      this.PROFILE_INCOMPLETE,
      this.PASSWORD_RESET,
      this.PASSWORD_CHANGED,
      this.SUBSCRIPTION_CREATED,
      this.SUBSCRIPTION_EXPIRING,
      this.SUBSCRIPTION_RENEWED,
      this.SYSTEM_MAINTENANCE,
      this.SYSTEM_UPDATE,
      this.SERVICE_DISRUPTION,
      this.FEATURE_ANNOUNCEMENT,
      this.SUPPORT_TICKET_CREATED,
      this.SUPPORT_TICKET_UPDATED,
      this.SUPPORT_TICKET_RESOLVED,
      this.OTP_VERIFICATION,
      this.SECURITY_ALERT,
      this.LOGIN_ATTEMPT
    ]

    return allTypes.includes(notificationType)
  }
}
