import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'service_configuration_options'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('service_configuration_id').references('id').inTable('service_configurations').onDelete('CASCADE').index()
      table.string('name').notNullable()
      table.enum('type', [
        'duration',
        'location', 
        'personnel',
        'equipment',
        'delivery_method',
        'expertise_level',
        'add_on',
        'scheduling',
        'custom'
      ]).notNullable().index()
      table.text('description').nullable()
      table.decimal('price_adjustment', 10, 2).notNullable().defaultTo(0.00)
      
      // Calendar integration
      table.string('duration_id').references('id').inTable('durations').onDelete('SET NULL').nullable().index()
      
      // Option properties
      table.boolean('is_default').notNullable().defaultTo(false).index()
      table.integer('sort_order').notNullable().defaultTo(0).index()
      table.json('constraints').notNullable().defaultTo('{}')
      table.boolean('active').notNullable().defaultTo(true).index()
      
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index(['service_configuration_id', 'type', 'active'])
      table.index(['service_configuration_id', 'sort_order'])
      table.index(['type', 'active'])
      table.index(['is_default', 'active'])
      
      // Unique constraint for name per configuration
      table.unique(['name', 'service_configuration_id'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
