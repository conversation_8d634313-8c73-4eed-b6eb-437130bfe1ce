import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'feature_flags'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      
      // Basic info
      table.string('name').notNullable()
      table.string('code').notNullable().unique()
      table.text('description').nullable()
      
      // Feature status
      table.boolean('is_enabled').defaultTo(true)
      table.jsonb('conditions').nullable().defaultTo('{}') // For conditional enabling/disabling
      
      // Scope
      table.string('scope').defaultTo('global') // global, vendor, branch, customer
      table.string('scope_id').nullable() // ID of the scoped entity (vendor_id, branch_id, etc.)
      
      // Metadata
      table.jsonb('meta').nullable()
      
      // Timestamps
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}