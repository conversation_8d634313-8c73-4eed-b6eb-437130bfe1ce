import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import SubscriptionService from 'App/Services/SubscriptionService'
import SubscriptionPlan from 'App/Models/SubscriptionPlan'
import CustomerSubscription from 'App/Models/CustomerSubscription'

export default class CustomerSubscriptionsController {
  /**
   * List customer's subscriptions
   */
  public async index({ auth, response }: HttpContextContract) {
    const customer = auth.user!
    const subscriptions = await CustomerSubscription.query()
      .where('customer_id', customer.id)
      .preload('plan')
      .orderBy('created_at', 'desc')

    return response.ok(subscriptions)
  }

  /**
   * Get current subscription
   */
  public async current({ auth, response }: HttpContextContract) {
    const customer = auth.user!
    const subscription = await SubscriptionService.getCurrentSubscription(customer)
    return response.ok(subscription)
  }

  /**
   * Subscribe to a plan
   */
  public async store({ auth, request, response }: HttpContextContract) {
    const customer = auth.user!
    const { planId, autoRenew, paymentMethodId } = request.only([
      'planId',
      'autoRenew',
      'paymentMethodId',
    ])

    const plan = await SubscriptionPlan.findOrFail(planId)
    const subscription = await SubscriptionService.createSubscription(customer, plan, {
      autoRenew,
      paymentMethodId,
    })

    return response.created(subscription)
  }

  /**
   * Cancel subscription
   */
  public async cancel({ params, request, response }: HttpContextContract) {
    const subscription = await CustomerSubscription.findOrFail(params.id)
    const { immediate } = request.only(['immediate'])

    await SubscriptionService.cancelSubscription(subscription, { immediate })
    return response.noContent()
  }

  /**
   * Get subscription usage
   */
  public async usage({ auth, request, response }: HttpContextContract) {
    const customer = auth.user!
    const { billingPeriod } = request.only(['billingPeriod'])

    const usage = await SubscriptionService.getCustomerUsage(customer, billingPeriod)
    return response.ok(usage)
  }
}