import { NotificationContract, NotificationChannelsList } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import User from 'App/Models/User'
import { DateTime } from 'luxon'

interface Product {
  id: string | number
  name: string
  originalPrice: number
  discountedPrice?: number
  currency: string
  category?: string
}

export default class BroadcastBuyNotification implements NotificationContract {
  constructor(
    private product: Product,
    private discount: number,
    private promoCode: string,
    private expiresAt: Date,
    private campaignTitle?: string,
    private campaignId?: string
  ) {}

  public via(_notifiable: User): (keyof NotificationChannelsList)[] {
    return ['database', 'fcm']
  }

  public toDatabase(notifiable: User) {
    const expiryDateTime = DateTime.fromJSDate(this.expiresAt)
    const now = DateTime.now()
    const hoursUntilExpiry = Math.ceil(expiryDateTime.diff(now, 'hours').hours)
    
    // Create urgency in title and message
    const title = this.campaignTitle || `${this.discount}% Off ${this.product.name}!`
    let urgencyText = ''
    
    if (hoursUntilExpiry <= 2) {
      urgencyText = ' - Expires in 2 hours!'
    } else if (hoursUntilExpiry <= 24) {
      urgencyText = ` - ${hoursUntilExpiry} hours left!`
    } else {
      const daysLeft = Math.ceil(hoursUntilExpiry / 24)
      urgencyText = ` - ${daysLeft} days left`
    }

    const personalizedMessage = `Hi ${notifiable.firstName || 'there'}! Special offer on ${this.product.name}. Save ${this.discount}% with code ${this.promoCode}${urgencyText}`

    // Create action context
    const actionContext = {
      productId: this.product.id,
      promoCode: this.promoCode,
      discount: this.discount,
      originalPrice: this.product.originalPrice,
      discountedPrice: this.product.discountedPrice || (this.product.originalPrice * (1 - this.discount / 100))
    }

    return NotificationHelper.createNotificationData(
      title,
      personalizedMessage,
      NotificationHelper.createPromotionActions('product', actionContext),
      {
        category: NotificationCategory.PROMOTION,
        notificationType: NotificationType.SPECIAL_OFFER,
        priority: hoursUntilExpiry <= 24 ? NotificationPriority.MEDIUM : NotificationPriority.LOW,
        entityId: this.product.id,
        entityType: 'product',
        campaignId: this.campaignId || `flash_sale_${new Date().getTime()}`,
        productName: this.product.name,
        discount: this.discount,
        promoCode: this.promoCode,
        originalPrice: this.product.originalPrice,
        discountedPrice: this.product.discountedPrice || (this.product.originalPrice * (1 - this.discount / 100)),
        currency: this.product.currency,
        expiresAt: this.expiresAt.toISOString(),
        hoursUntilExpiry
      },
      'https://cdn.verful.com/icons/special-offer-icon.png'
    )
  }

  public toFcm(_notifiable: User) {
    const expiryDateTime = DateTime.fromJSDate(this.expiresAt)
    const now = DateTime.now()
    const hoursUntilExpiry = Math.ceil(expiryDateTime.diff(now, 'hours').hours)
    
    let urgencyText = ''
    if (hoursUntilExpiry <= 2) {
      urgencyText = ' - Expires soon!'
    } else if (hoursUntilExpiry <= 24) {
      urgencyText = ` - ${hoursUntilExpiry}h left!`
    }

    const title = this.campaignTitle || `${this.discount}% Off!${urgencyText}`
    const body = `${this.product.name} - Use code ${this.promoCode}`

    return {
      title,
      body,
      url: `/products/${this.product.id}?promo=${this.promoCode}`,
      icon: 'https://cdn.verful.com/icons/special-offer-icon.png',
      actions: [
        {
          screen: 'buy_now',
          label: 'Buy Now',
          args: {
            productId: this.product.id.toString(),
            promoCode: this.promoCode || ''
          }
        },
        {
          screen: 'view_details',
          label: 'View Details',
          args: {
            productId: this.product.id.toString(),
            promoCode: this.promoCode || ''
          }
        }
      ]
    }
  }
}
