import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import { DateTime } from 'luxon'
import VendorDeliverySettingsValidator from 'App/Validators/VendorDeliverySettingsValidator'

export default class VendorDeliverySettingsController {
  /**
   * Get delivery settings
   */
  @bind()
  public async show({ response }: HttpContextContract, vendor: Vendor) {
    return response.json({
      deliveryPreferences: vendor.deliveryPreferences,
      availabilitySettings: vendor.availabilitySettings,
      pricingStructure: vendor.pricingStructure,
      verificationStatus: vendor.verificationStatus,
    })
  }

  /**
   * Update delivery preferences
   */
  @bind()
  public async updatePreferences({ request, response }: HttpContextContract, vendor: Vendor) {
    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.preferences) {
      vendor.deliveryPreferences = data.preferences
      await vendor.save()
    }
    return response.json(vendor.deliveryPreferences)
  }

  /**
   * Update availability settings
   */
  @bind()
  public async updateAvailability({ request, response }: HttpContextContract, vendor: Vendor) {
    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.settings) {
      vendor.availabilitySettings = data.settings
      await vendor.save()
    }
    return response.json(vendor.availabilitySettings)
  }

  /**
   * Update pricing structure
   */
  @bind()
  public async updatePricing({ request, response }: HttpContextContract, vendor: Vendor) {
    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.pricing) {
      vendor.pricingStructure = data.pricing
      await vendor.save()
    }
    return response.json(vendor.pricingStructure)
  }

  /**
   * Update verification status (admin only)
   */
  @bind()
  public async updateVerification({ request, response, auth }: HttpContextContract, vendor: Vendor) {
    // Ensure user is admin
    if (!auth.user?.hasRole('admin')) {
      return response.forbidden({ message: 'Only admins can update verification status' })
    }

    const data = await request.validate(VendorDeliverySettingsValidator)
    if (data.status) {
      vendor.verificationStatus = data.status as 'pending' | 'verified' | 'rejected'
      vendor.verificationNotes = data.notes || null
      vendor.verifiedAt = data.status === 'verified' ? DateTime.now() : null
      await vendor.save()
    }

    return response.json({
      verificationStatus: vendor.verificationStatus,
      verificationNotes: vendor.verificationNotes,
      verifiedAt: vendor.verifiedAt,
    })
  }
} 