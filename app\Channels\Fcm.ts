import { initializeApp, ServiceAccount, getApps } from 'firebase-admin/app'
import { Message, getMessaging } from 'firebase-admin/messaging'
import { credential } from 'firebase-admin'
import serviceCredential from '../../google-services-user.json'
import User from 'App/Models/User'
import { NotificationChannelContract } from '@ioc:Verful/Notification'
import { ChainableContract } from '@ioc:Adonis/Lucid/Database'
import { NotificationMessagePayload } from 'App/Interfaces/NotificationMessagePayload'
import NotificationTracking from 'App/Services/NotificationTracking'

export default class FcChannel implements NotificationChannelContract {
  public async send(notification: NotificationMessagePayload, notifiable: User) {
    try {
      const app = getApps().length
        ? getApps()[0]
        : initializeApp({
            credential: credential.cert(serviceCredential as ServiceAccount),
          })

      notification.body = notification.body?.replace('{firstName}', notifiable.firstName)
      notification.body = notification.body?.replace('{lastName}', notifiable.lastName)

      await notifiable.load('devices', (dq: ChainableContract) => {
        dq.whereNotNull('token')
      })

      const defaultImageUrl =
        'https://aia-website.vercel.app/_next/image?url=/_next/static/media/logo.9d956f51.png&w=96&q=75'

      // Track notification attempt
      await NotificationTracking.track(
        notification as any,
        notifiable,
        'sent',
        'fcm',
        { deviceCount: notifiable.devices.length }
      )

      const results = await Promise.allSettled(
        notifiable.devices.map(async ({ token }) => {
          const payload: Message = {
            notification: {
              title: notification.title || 'Notification',
              body: notification.body || 'Notification',
            },
            token,
            android: {
              priority: 'high',
              notification: {
                sound: 'default',
                imageUrl: notification.imageUrl || defaultImageUrl,
              },
            },
            apns: {
              payload: {
                aps: {
                  mutableContent: true,
                  contentAvailable: true,
                  sound: 'default',
                },
              },
              fcmOptions: {
                imageUrl: notification.imageUrl || defaultImageUrl,
              },
            },
            webpush: {
              notification: {
                imageUrl: notification.imageUrl || defaultImageUrl,
              },
            },
            data: {
              title: notification.title || 'Notification',
              body: notification.body || 'Notification',
              link:
                notification.actions?.reduce(
                  (acc, action) =>
                    acc +
                    Object.keys(action.args).reduce(
                      (_acc, key) => `${_acc}/${action.args[key]}`,
                      action.screen
                    ),
                  ''
                ) || '',
            },
          }

          return getMessaging(app).send(payload)
        })
      )

      // Track successful and failed deliveries
      const successfulDeliveries = results.filter(
        (result) => result.status === 'fulfilled'
      ).length

      const failedDeliveries = results.filter(
        (result) => result.status === 'rejected'
      ).length

      if (successfulDeliveries > 0) {
        await NotificationTracking.track(
          notification as any,
          notifiable,
          'delivered',
          'fcm',
          { successfulDeliveries }
        )
      }

      if (failedDeliveries > 0) {
        await NotificationTracking.track(
          notification as any,
          notifiable,
          'failed',
          'fcm',
          { failedDeliveries }
        )
      }

    } catch (error) {
      console.error(error)
      // Track failed notification attempt
      await NotificationTracking.track(
        notification as any,
        notifiable,
        'failed',
        'fcm',
        { error: error.message }
      )
    }
  }
}
