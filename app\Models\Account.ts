import { DateTime } from 'luxon'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { BaseModel, BelongsTo, belongsTo, column, beforeCreate } from '@ioc:Adonis/Lucid/Orm'
import AccountFilter from './Filters/AccountFilter'
import User from './User'
import { ulid } from 'ulidx'

export default class Account extends compose(BaseModel, Filterable) {
  public static selfAssignPrimaryKey = true

  public static $filter = () => AccountFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public provider: string

  @column()
  public providerId: string

  @column()
  public userId: string

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(account: Account) {
    account.id = ulid().toLowerCase()
  }

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>
}
