import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'products'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add individual indexes
      table.index('vendor_id')
      table.index('branch_id')
      table.index('product_category_id')
      table.index('service_id')
      table.index('name')
      table.index('ref')
      table.index('status')
      table.index('created_at')
      table.index('price')

      // Add composite index
      table.index(['active', 'featured'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove individual indexes
      table.dropIndex('vendor_id')
      table.dropIndex('branch_id')
      table.dropIndex('product_category_id')
      table.dropIndex('service_id')
      table.dropIndex('name')
      table.dropIndex('ref')
      table.dropIndex('status')
      table.dropIndex('created_at')
      table.dropIndex('price')

      // Remove composite index
      table.dropIndex(['active', 'featured'])
    })
  }
} 