import { DateTime } from 'luxon'
import Logger from '@ioc:Adonis/Core/Logger'
import NotificationUsage from 'App/Models/NotificationUsage'
import NotificationBillingTier from 'App/Models/NotificationBillingTier'
import Vendor from 'App/Models/Vendor'
import Database from '@ioc:Adonis/Lucid/Database'

export default class NotificationBillingService {
  /**
   * Generate monthly bills for all vendors
   */
  public static async generateMonthlyBills(billingPeriod: string = DateTime.now().toFormat('yyyy-MM')) {
    try {
      // Get all vendors with pending notification usage
      const vendors = await Vendor.query()
        .whereHas('notificationUsages', (query) => {
          query.where('billing_period', billingPeriod)
            .where('status', 'pending')
        })
        .preload('notificationUsages', (query) => {
          query.where('billing_period', billingPeriod)
            .where('status', 'pending')
        })

      for (const vendor of vendors) {
        await this.generateVendorBill(vendor, billingPeriod)
      }

      Logger.info('Monthly notification bills generated', { billingPeriod })
    } catch (error) {
      Logger.error('Failed to generate monthly notification bills', {
        error,
        billingPeriod,
      })
      throw error
    }
  }

  /**
   * Generate bill for a specific vendor
   */
  private static async generateVendorBill(vendor: Vendor, billingPeriod: string) {
    const trx = await Database.transaction()

    try {
      // Get vendor's current tier
      const tier = await NotificationBillingTier.query({ client: trx })
        .where('is_active', true)
        .first()

      if (!tier) {
        throw new Error(`No active billing tier found for vendor ${vendor.id}`)
      }

      // Get all pending usage records for the billing period
      const usages = await NotificationUsage.query({ client: trx })
        .where('vendor_id', vendor.id)
        .where('billing_period', billingPeriod)
        .where('status', 'pending')
        .orderBy('created_at', 'asc')

      if (usages.length === 0) {
        await trx.rollback()
        return
      }

      // Calculate total usage and costs
      const usageByChannel = this.calculateUsageByChannel(usages)
      const totalUsage = usages.length
      const baseCost = tier.base_price
      const overageCost = tier.calculateOverageCost(totalUsage)

      // Create bill record
      const [bill] = await Database.table('bills')
        .insert({
          vendor_id: vendor.id,
          type: 'notification',
          billing_period: billingPeriod,
          base_amount: baseCost,
          overage_amount: overageCost,
          total_amount: baseCost + overageCost,
          currency: tier.currency,
          status: 'pending',
          meta: {
            usage_by_channel: usageByChannel,
            total_usage: totalUsage,
            tier_id: tier.id,
            tier_name: tier.name,
          },
          created_at: new Date(),
          updated_at: new Date(),
        })
        .returning('*')

      // Mark all usage records as billed
      await NotificationUsage.query({ client: trx })
        .whereIn('id', usages.map(u => u.id))
        .update({
          status: 'billed',
          billed_at: new Date(),
          meta: Database.raw(`jsonb_set(COALESCE(meta, '{}'::jsonb), '{bill_id}', '"${bill.id}"'::jsonb)`),
        })

      await trx.commit()

      Logger.info('Vendor notification bill generated', {
        vendor_id: vendor.id,
        billing_period: billingPeriod,
        bill_id: bill.id,
        total_amount: baseCost + overageCost,
      })
    } catch (error) {
      await trx.rollback()
      Logger.error('Failed to generate vendor notification bill', {
        error,
        vendor_id: vendor.id,
        billing_period: billingPeriod,
      })
      throw error
    }
  }

  /**
   * Calculate usage statistics by channel
   */
  private static calculateUsageByChannel(usages: NotificationUsage[]): Record<string, any> {
    const stats = {
      sms: { count: 0, cost: 0 },
      email: { count: 0, cost: 0 },
      push: { count: 0, cost: 0 },
    }

    for (const usage of usages) {
      if (stats[usage.channel]) {
        stats[usage.channel].count++
        stats[usage.channel].cost += usage.cost
      }
    }

    return stats
  }

  /**
   * Get usage statistics for a vendor
   */
  public static async getVendorUsageStats(
    vendorId: string,
    startDate: DateTime,
    endDate: DateTime
  ) {
    const usages = await NotificationUsage.query()
      .where('vendor_id', vendorId)
      .whereBetween('created_at', [startDate.toSQL(), endDate.toSQL()])
      .orderBy('created_at', 'asc')

    return {
      totalUsage: usages.length,
      usageByChannel: this.calculateUsageByChannel(usages),
      totalCost: usages.reduce((sum, usage) => sum + usage.cost, 0),
      usages,
    }
  }
} 