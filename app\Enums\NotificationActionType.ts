/**
 * Notification Action Types
 * 
 * Defines semantic action types that the backend sends to frontend.
 * Frontend maps these to appropriate routes/screens based on platform.
 */
export class NotificationActionType {
  // Billing & Payment Actions
  static readonly PAY_BILL = 'PAY_BILL'
  static readonly PAY_INVOICE = 'PAY_INVOICE'
  static readonly VIEW_BILL_DETAILS = 'VIEW_BILL_DETAILS'
  static readonly VIEW_INVOICE = 'VIEW_INVOICE'
  static readonly VIEW_STATEMENT = 'VIEW_STATEMENT'
  static readonly DOWNLOAD_STATEMENT = 'DOWNLOAD_STATEMENT'
  static readonly DISPUTE_BILL = 'DISPUTE_BILL'
  static readonly RETRY_PAYMENT = 'RETRY_PAYMENT'
  static readonly VIEW_PAYMENT_HISTORY = 'VIEW_PAYMENT_HISTORY'

  // Order Actions
  static readonly VIEW_ORDER = 'VIEW_ORDER'
  static readonly TRACK_ORDER = 'TRACK_ORDER'
  static readonly REORDER = 'REORDER'
  static readonly CANCEL_ORDER = 'CANCEL_ORDER'
  static readonly CONTACT_SUPPORT = 'CONTACT_SUPPORT'
  static readonly RATE_ORDER = 'RATE_ORDER'
  static readonly VIEW_RECEIPT = 'VIEW_RECEIPT'

  // Commerce Actions
  static readonly BUY_PRODUCT = 'BUY_PRODUCT'
  static readonly VIEW_PRODUCT = 'VIEW_PRODUCT'
  static readonly ADD_TO_CART = 'ADD_TO_CART'
  static readonly VIEW_OFFER = 'VIEW_OFFER'
  static readonly BROWSE_CATEGORY = 'BROWSE_CATEGORY'
  static readonly COMPARE_PLANS = 'COMPARE_PLANS'
  static readonly VIEW_RATES = 'VIEW_RATES'
  static readonly SCHEDULE_DELIVERY = 'SCHEDULE_DELIVERY'

  // Account Actions
  static readonly VERIFY_ACCOUNT = 'VERIFY_ACCOUNT'
  static readonly COMPLETE_PROFILE = 'COMPLETE_PROFILE'
  static readonly UPDATE_SETTINGS = 'UPDATE_SETTINGS'
  static readonly VIEW_SUBSCRIPTION = 'VIEW_SUBSCRIPTION'
  static readonly RENEW_SUBSCRIPTION = 'RENEW_SUBSCRIPTION'
  static readonly UPGRADE_PLAN = 'UPGRADE_PLAN'
  static readonly CONTACT_SALES = 'CONTACT_SALES'

  // General Actions
  static readonly VIEW_DETAILS = 'VIEW_DETAILS'
  static readonly DISMISS = 'DISMISS'
  static readonly REMIND_LATER = 'REMIND_LATER'
  static readonly SHARE = 'SHARE'
  static readonly DOWNLOAD = 'DOWNLOAD'
  static readonly OPEN_EXTERNAL = 'OPEN_EXTERNAL'

  // Temp Order Actions
  static readonly VIEW_TEMP_ORDER = 'VIEW_TEMP_ORDER'
  static readonly APPROVE_TEMP_ORDER = 'APPROVE_TEMP_ORDER'
  static readonly REJECT_TEMP_ORDER = 'REJECT_TEMP_ORDER'

  /**
   * Get all billing-related action types
   */
  static getBillingActions(): string[] {
    return [
      this.PAY_BILL,
      this.PAY_INVOICE,
      this.VIEW_BILL_DETAILS,
      this.VIEW_INVOICE,
      this.VIEW_STATEMENT,
      this.DOWNLOAD_STATEMENT,
      this.DISPUTE_BILL,
      this.RETRY_PAYMENT,
      this.VIEW_PAYMENT_HISTORY
    ]
  }

  /**
   * Get all order-related action types
   */
  static getOrderActions(): string[] {
    return [
      this.VIEW_ORDER,
      this.TRACK_ORDER,
      this.REORDER,
      this.CANCEL_ORDER,
      this.CONTACT_SUPPORT,
      this.RATE_ORDER,
      this.VIEW_RECEIPT
    ]
  }

  /**
   * Get all commerce-related action types
   */
  static getCommerceActions(): string[] {
    return [
      this.BUY_PRODUCT,
      this.VIEW_PRODUCT,
      this.ADD_TO_CART,
      this.VIEW_OFFER,
      this.BROWSE_CATEGORY,
      this.COMPARE_PLANS,
      this.VIEW_RATES,
      this.SCHEDULE_DELIVERY
    ]
  }

  /**
   * Check if action type is valid
   */
  static isValid(actionType: string): boolean {
    const allActions = [
      ...this.getBillingActions(),
      ...this.getOrderActions(),
      ...this.getCommerceActions(),
      this.VERIFY_ACCOUNT,
      this.COMPLETE_PROFILE,
      this.UPDATE_SETTINGS,
      this.VIEW_SUBSCRIPTION,
      this.RENEW_SUBSCRIPTION,
      this.UPGRADE_PLAN,
      this.CONTACT_SALES,
      this.VIEW_DETAILS,
      this.DISMISS,
      this.REMIND_LATER,
      this.SHARE,
      this.DOWNLOAD,
      this.OPEN_EXTERNAL,
      this.VIEW_TEMP_ORDER,
      this.APPROVE_TEMP_ORDER,
      this.REJECT_TEMP_ORDER
    ]
    
    return allActions.includes(actionType)
  }
}
