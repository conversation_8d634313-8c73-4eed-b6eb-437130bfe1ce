import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import CustomerMailer from 'App/Mailers/CustomerMailer'
import Booking from 'App/Models/Booking'
import User from 'App/Models/User'

export default class BookingConfirmationNotification implements NotificationContract {
  constructor(
    private booking: Booking,
    private confirmationType: 'created' | 'confirmed' = 'created'
  ) {}

  public via(_notifiable: User) {
    return ['database' as const, 'fcm' as const, 'mail' as const]
  }

  public toDatabase(notifiable: User) {
    const isConfirmed = this.confirmationType === 'confirmed'
    const title = isConfirmed ? 'Booking Confirmed' : 'Booking Request Received'
    const message = isConfirmed 
      ? `Your booking for ${this.booking.product?.name} has been confirmed for ${this.booking.appointmentStart.toFormat('MMM dd, yyyy')} at ${this.booking.appointmentStart.toFormat('h:mm a')}.`
      : `Your booking request for ${this.booking.product?.name} has been received. Confirmation code: ${this.booking.confirmationCode}`

    return NotificationHelper.createDatabaseNotification({
      category: NotificationCategory.BOOKING,
      type: NotificationType.BOOKING_CONFIRMATION,
      priority: NotificationPriority.HIGH,
      title,
      message,
      actionType: NotificationActionType.VIEW_BOOKING,
      actionUrl: `/bookings/${this.booking.id}`,
      actionLabel: 'View Booking',
      data: {
        booking_id: this.booking.id,
        confirmation_code: this.booking.confirmationCode,
        appointment_date: this.booking.appointmentStart.toFormat('yyyy-MM-dd'),
        appointment_time: this.booking.appointmentStart.toFormat('HH:mm'),
        service_name: this.booking.product?.name,
        vendor_name: this.booking.vendor?.name,
        branch_name: this.booking.branch?.name,
        total_price: this.booking.totalPrice,
        duration_minutes: this.booking.durationMinutes,
        status: this.booking.status
      }
    })
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const isConfirmed = this.confirmationType === 'confirmed'
    const title = isConfirmed ? 'Booking Confirmed' : 'Booking Request Received'
    
    let body: string
    if (isConfirmed) {
      body = `Your booking for ${this.booking.product?.name} is confirmed for ${this.booking.appointmentStart.toFormat('MMM dd')} at ${this.booking.appointmentStart.toFormat('h:mm a')}.`
    } else {
      body = `Your booking request for ${this.booking.product?.name} has been received. Confirmation code: ${this.booking.confirmationCode}`
    }

    // Replace placeholders with actual user data
    body = body.replace('{firstName}', notifiable.firstName || notifiable.name)
    body = body.replace('{lastName}', notifiable.lastName || '')

    return {
      title,
      body,
      url: `aiauser://bookings/${this.booking.id}`,
      icon: 'https://cdn.verful.com/icons/verful-512x512.png',
      data: {
        booking_id: this.booking.id,
        confirmation_code: this.booking.confirmationCode || '',
        type: 'booking_confirmation'
      }
    }
  }

  public toMail(notifiable: User) {
    const isConfirmed = this.confirmationType === 'confirmed'
    const subject = isConfirmed ? 'Booking Confirmed' : 'Booking Request Received'
    
    const appointmentDate = this.booking.appointmentStart.toFormat('EEEE, MMMM dd, yyyy')
    const appointmentTime = this.booking.appointmentStart.toFormat('h:mm a')
    
    let greeting: string
    let intro: string
    
    if (isConfirmed) {
      greeting = `Hello ${notifiable.firstName || notifiable.name}!`
      intro = `Great news! Your booking has been confirmed. Here are the details:`
    } else {
      greeting = `Hello ${notifiable.firstName || notifiable.name}!`
      intro = `Thank you for your booking request. We have received your request and will confirm it shortly. Here are the details:`
    }

    const emailData = {
      greeting,
      subject,
      intro,
      booking_details: {
        confirmation_code: this.booking.confirmationCode,
        service_name: this.booking.product?.name,
        appointment_date: appointmentDate,
        appointment_time: appointmentTime,
        duration: `${this.booking.durationMinutes} minutes`,
        total_price: `$${this.booking.totalPrice.toFixed(2)}`,
        vendor_name: this.booking.vendor?.name,
        branch_name: this.booking.branch?.name,
        branch_location: this.booking.branch?.location?.name || 'Location details will be provided',
        status: this.booking.status
      },
      selected_options: this.booking.selectedServiceOptions || [],
      staff_assignments: this.booking.staffAssignments || [],
      booking_notes: this.booking.bookingNotes,
      action_url: `/bookings/${this.booking.id}`,
      action_label: 'View Booking Details',
      footer_text: isConfirmed 
        ? 'We look forward to serving you! If you need to make any changes, please contact us as soon as possible.'
        : 'You will receive a confirmation email once your booking is approved. If you have any questions, please contact us.'
    }

    return new CustomerMailer(notifiable, 'mails/booking/confirmation', emailData)
  }

  public toSms(notifiable: User) {
    if (!notifiable.phone) {
      throw new Error('Cannot send SMS to user without phone number')
    }

    const isConfirmed = this.confirmationType === 'confirmed'
    const serviceName = this.booking.product?.name || 'service'
    const date = this.booking.appointmentStart.toFormat('MMM dd')
    const time = this.booking.appointmentStart.toFormat('h:mm a')
    const confirmationCode = this.booking.confirmationCode

    let text: string
    if (isConfirmed) {
      text = `Hi ${notifiable.firstName || notifiable.name}, your booking for ${serviceName} is CONFIRMED for ${date} at ${time}. Confirmation: ${confirmationCode}`
    } else {
      text = `Hi ${notifiable.firstName || notifiable.name}, your booking request for ${serviceName} has been received. Confirmation code: ${confirmationCode}. You'll be notified once confirmed.`
    }

    return {
      text,
      phone: notifiable.phone.includes('+')
        ? notifiable.phone.replace(/^\+/, '')
        : notifiable.phone,
    }
  }
}
