import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Invoice from 'App/Models/Invoice'
import { bind } from '@adonisjs/route-model-binding'

export default class InvoicesController {
  /**
   * @index
   * @summary List all Invoices
   * @description List all Invoices, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 15, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const invoiceQuery = Invoice.filter(filters)

    return await invoiceQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a Invoice
   * @description Create a Invoice with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Invoice>
   */
  public async store({ request, response }: HttpContextContract) {
    const { amount, orderId } = request.all()

    const invoice = await Invoice.create({
      amount,
      orderId,
    })

    return response.json(invoice)
  }

  @bind()
  /**
   * @show
   * @summary Show a single Invoice
   * @description Show a Invoice with their details (name and details)
   * @paramPath id required number - Invoice ID
   * @responseBody 200 - <Invoice>
   * @response 404 - Invoice not found
   */
  public async show({ response }: HttpContextContract, invoice: Invoice) {
    return response.json(invoice)
  }

  @bind()
  /**
   * @update
   * @summary Update a Invoice
   * @description Update a Invoice with their details (name and details)
   * @paramPath id required number - Invoice ID
   * @requestBody <Invoice>
   * @responseBody 200 - <Invoice>
   * @response 404 - Invoice not found
   */
  public async update({ request, response }: HttpContextContract, invoice: Invoice) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await invoice.merge(input).save()

    return response.json(invoice)
  }

  @bind()
  /**
   * @destroy
   * @summary delete a Invoice
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, invoice: Invoice) {
    return await invoice.delete()
  }
}
