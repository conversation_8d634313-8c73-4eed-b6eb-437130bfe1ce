import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { NotificationActionType } from 'App/Enums/NotificationActionType'

interface FailedPayment {
  id: string | number
  amount: number
  currency: string
  method: string
  reference: string
  failureReason: string
  billId?: string | number
  invoiceId?: string | number
  attemptedAt: Date
}

export default class CustomerPaymentFailure implements NotificationContract {
  constructor(
    private payment: FailedPayment,

  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    const title = 'Payment Failed'
    const body = `Your payment of ${this.payment.currency} ${this.payment.amount} could not be processed. Reason: ${this.payment.failureReason}. Please try again.`

    // Create retry and alternative actions
    const actions = [
      NotificationHelper.createAction(
        NotificationActionType.CONTACT_SUPPORT,
        'Contact Support',
        {
          issue: 'payment_failure',
          paymentId: this.payment.id,
          reference: this.payment.reference
        }
      )
    ]

    // Add specific retry actions based on payment type
    if (this.payment.billId) {
      actions.unshift(
        NotificationHelper.createAction(
          NotificationActionType.RETRY_PAYMENT,
          'Retry Payment',
          { billId: this.payment.billId }
        ),
        NotificationHelper.createAction(
          NotificationActionType.VIEW_BILL_DETAILS,
          'View Bill',
          { billId: this.payment.billId }
        )
      )
    }

    if (this.payment.invoiceId) {
      actions.unshift(
        NotificationHelper.createAction(
          NotificationActionType.PAY_INVOICE,
          'Retry Payment',
          { invoiceId: this.payment.invoiceId }
        ),
        NotificationHelper.createAction(
          NotificationActionType.VIEW_INVOICE,
          'View Invoice',
          { invoiceId: this.payment.invoiceId }
        )
      )
    }

    return NotificationHelper.createNotificationData(
      title,
      body,
      actions,
      {
        category: NotificationCategory.BILLING,
        notificationType: NotificationType.PAYMENT_FAILED,
        priority: NotificationPriority.HIGH,
        entityId: this.payment.id,
        entityType: 'payment',
        amount: this.payment.amount,
        currency: this.payment.currency,
        paymentMethod: this.payment.method,
        paymentReference: this.payment.reference,
        failureReason: this.payment.failureReason,
        billId: this.payment.billId,
        invoiceId: this.payment.invoiceId,
        attemptedAt: this.payment.attemptedAt.toISOString()
      },
      'https://cdn.verful.com/icons/payment-failed-icon.png'
    )
  }
}
