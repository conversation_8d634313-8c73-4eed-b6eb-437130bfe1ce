import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import MessageTemplate from '../MessageTemplate'

export default class MessageTemplateFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof MessageTemplate, MessageTemplate>

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }
}
