import OrderItem from 'App/Models/OrderItem'
import Factory from '@ioc:Adonis/Lucid/Factory'
import { DateTime } from 'luxon'
import { ulid } from 'ulidx'

export default Factory.define(OrderItem, ({ faker }) => {
  const status = faker.helpers.arrayElement(['pending', 'preparing', 'ready', 'served', 'cancelled', 'on_hold', 'delayed'])
  const quantity = faker.number.int({ min: 1, max: 5 })
  const price = faker.number.float({ min: 5.0, max: 50.0, fractionDigits: 2 })
  const estimatedTime = faker.number.int({ min: 5, max: 30 })
  
  // Generate timing data based on status
  let preparationStartedAt = null
  let preparationCompletedAt = null
  let servedAt = null
  let actualPreparationTime = null
  
  if (['preparing', 'ready', 'served'].includes(status)) {
    preparationStartedAt = DateTime.now().minus({ minutes: faker.number.int({ min: 1, max: estimatedTime }) })
  }
  
  if (['ready', 'served'].includes(status)) {
    const prepTime = faker.number.int({ min: estimatedTime * 0.8, max: estimatedTime * 1.2 })
    preparationCompletedAt = preparationStartedAt?.plus({ minutes: prepTime })
    actualPreparationTime = prepTime
  }
  
  if (status === 'served') {
    servedAt = preparationCompletedAt?.plus({ minutes: faker.number.int({ min: 1, max: 10 }) })
  }

  return {
    orderId: ulid().toLowerCase(),
    productId: ulid().toLowerCase(),
    quantity,
    price,
    meta: {
      productName: faker.commerce.productName(),
      category: faker.helpers.arrayElement(['Main Course', 'Appetizer', 'Dessert', 'Beverage', 'Side Dish']),
      allergens: faker.helpers.arrayElements(['Nuts', 'Dairy', 'Gluten', 'Shellfish'], { min: 0, max: 2 })
    },
    status,
    departmentId: faker.datatype.boolean({ probability: 0.8 }) ? ulid().toLowerCase() : null,
    assignedStaffId: ['preparing', 'ready', 'served'].includes(status) ? ulid().toLowerCase() : null,
    estimatedPreparationTime: estimatedTime,
    preparationStartedAt,
    preparationCompletedAt,
    servedAt,
    priorityLevel: faker.number.int({ min: 1, max: 5 }),
    requiresSpecialAttention: faker.datatype.boolean({ probability: 0.2 }),
    specialInstructions: faker.datatype.boolean({ probability: 0.3 }) ? faker.lorem.sentence() : null,
    preparationNotes: ['preparing', 'ready', 'served'].includes(status) ? {
      notes: faker.lorem.sentences(2),
      timestamp: DateTime.now().toISO(),
      staffId: ulid().toLowerCase()
    } : null,
    statusHistory: {
      [`${status}_${DateTime.now().toISO()}`]: {
        status,
        timestamp: DateTime.now().toISO(),
        staffId: ulid().toLowerCase()
      }
    },
    qualityCheckStatus: faker.helpers.arrayElement(['pending', 'passed', 'failed', 'not_required']),
    qualityCheckedBy: faker.datatype.boolean({ probability: 0.4 }) ? ulid().toLowerCase() : null,
    customerModifications: faker.datatype.boolean({ probability: 0.2 }) ? {
      modifications: [faker.lorem.words(3)],
      requestedBy: ulid().toLowerCase(),
      timestamp: DateTime.now().toISO()
    } : null,
    cancellationReason: status === 'cancelled' ? faker.lorem.sentence() : null,
    actualPreparationTime,
    preparationAttempts: faker.number.int({ min: 1, max: 3 })
  }
})
.state('pending', (item) => ({
  status: 'pending',
  departmentId: null,
  assignedStaffId: null,
  preparationStartedAt: null,
  preparationCompletedAt: null,
  servedAt: null,
  actualPreparationTime: null,
  preparationNotes: null
}))
.state('preparing', (item) => ({
  status: 'preparing',
  departmentId: ulid().toLowerCase(),
  assignedStaffId: ulid().toLowerCase(),
  preparationStartedAt: DateTime.now().minus({ minutes: faker.number.int({ min: 1, max: 15 }) }),
  preparationCompletedAt: null,
  servedAt: null,
  actualPreparationTime: null,
  preparationNotes: {
    notes: 'Item preparation in progress',
    timestamp: DateTime.now().toISO(),
    staffId: ulid().toLowerCase()
  }
}))
.state('ready', (item) => {
  const startTime = DateTime.now().minus({ minutes: faker.number.int({ min: 10, max: 25 }) })
  const prepTime = faker.number.int({ min: 8, max: 20 })
  const completedTime = startTime.plus({ minutes: prepTime })
  
  return {
    status: 'ready',
    departmentId: ulid().toLowerCase(),
    assignedStaffId: ulid().toLowerCase(),
    preparationStartedAt: startTime,
    preparationCompletedAt: completedTime,
    servedAt: null,
    actualPreparationTime: prepTime,
    qualityCheckStatus: 'passed',
    qualityCheckedBy: ulid().toLowerCase(),
    preparationNotes: {
      notes: 'Item ready for serving',
      timestamp: completedTime.toISO(),
      staffId: ulid().toLowerCase()
    }
  }
})
.state('served', (item) => {
  const startTime = DateTime.now().minus({ minutes: faker.number.int({ min: 15, max: 35 }) })
  const prepTime = faker.number.int({ min: 8, max: 20 })
  const completedTime = startTime.plus({ minutes: prepTime })
  const servedTime = completedTime.plus({ minutes: faker.number.int({ min: 1, max: 5 }) })
  
  return {
    status: 'served',
    departmentId: ulid().toLowerCase(),
    assignedStaffId: ulid().toLowerCase(),
    preparationStartedAt: startTime,
    preparationCompletedAt: completedTime,
    servedAt: servedTime,
    actualPreparationTime: prepTime,
    qualityCheckStatus: 'passed',
    qualityCheckedBy: ulid().toLowerCase()
  }
})
.state('cancelled', (item) => ({
  status: 'cancelled',
  cancellationReason: faker.helpers.arrayElement([
    'Customer request',
    'Out of stock',
    'Kitchen error',
    'Order modification'
  ]),
  preparationStartedAt: null,
  preparationCompletedAt: null,
  servedAt: null,
  actualPreparationTime: null
}))
.state('overdue', (item) => ({
  status: 'preparing',
  departmentId: ulid().toLowerCase(),
  assignedStaffId: ulid().toLowerCase(),
  preparationStartedAt: DateTime.now().minus({ minutes: faker.number.int({ min: 30, max: 60 }) }),
  estimatedPreparationTime: faker.number.int({ min: 10, max: 20 }),
  requiresSpecialAttention: true,
  preparationNotes: {
    notes: 'Item is overdue - requires immediate attention',
    timestamp: DateTime.now().toISO(),
    staffId: ulid().toLowerCase()
  }
}))
.state('highPriority', (item) => ({
  priorityLevel: 1,
  requiresSpecialAttention: true,
  specialInstructions: 'High priority order - expedite preparation',
  estimatedPreparationTime: faker.number.int({ min: 5, max: 15 })
}))
.state('withModifications', (item) => ({
  customerModifications: {
    modifications: [
      'No onions',
      'Extra cheese',
      'Medium rare'
    ],
    requestedBy: ulid().toLowerCase(),
    timestamp: DateTime.now().toISO(),
    notes: 'Customer has specific dietary requirements'
  },
  requiresSpecialAttention: true
}))
.build()
