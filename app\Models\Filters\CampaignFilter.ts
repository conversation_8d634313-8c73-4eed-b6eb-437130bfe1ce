import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Campaign from '../Campaign'

export default class CampaignFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Campaign, Campaign>

  public active(value: boolean): void {
    this.$query.where('active', value)
  }

  public s(name: string) {
    this.$query.whereILike('name', `%${name}%`)
  }
}
