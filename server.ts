/*
|--------------------------------------------------------------------------
| AdonisJs Server
|--------------------------------------------------------------------------
|
| The contents in this file is meant to bootstrap the AdonisJs application
| and start the HTTP server to accept incoming connections. You must avoid
| making this file dirty and instead make use of `lifecycle hooks` provided
| by AdonisJs service providers for custom code.
|
*/

import 'reflect-metadata'
import sourceMapSupport from 'source-map-support'
import { Ignitor } from '@adonisjs/core/build/standalone'

sourceMapSupport.install({ handleUncaughtExceptions: false })

// Start the HTTP server and initialize WebSocket
new Ignitor(__dirname)
  .httpServer()
  .start()
  .then(async (httpServer) => {
    // Import WebSocket services after <PERSON><PERSON><PERSON> is fully initialized
    const { default: WebSocketManager } = await import('./app/Services/WebSocketManager')
    const { default: RealTimeNotificationService } = await import(
      './app/Services/RealTimeNotificationService'
    )

    // Initialize WebSocket server with the HTTP server
    WebSocketManager.initialize(httpServer)
    console.log('🚀 WebSocket server initialized and ready for connections')

    // Initialize notification service
    RealTimeNotificationService.initialize()
    console.log('📢 Real-time notification service initialized')

    // Handle graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      console.log(`📡 Received ${signal}. Starting graceful shutdown...`)

      try {
        // Close WebSocket connections
        await WebSocketManager.close()
        console.log('🔌 WebSocket connections closed')

        console.log('✅ Graceful shutdown completed')
        process.exit(0)
      } catch (error) {
        console.error('❌ Error during graceful shutdown:', error)
        process.exit(1)
      }
    }

    // Listen for shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => gracefulShutdown('SIGINT'))

    // Emit ready event for PM2
    if (process.send) {
      process.send('ready')
    }
  })
  .catch((error) => {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  })
