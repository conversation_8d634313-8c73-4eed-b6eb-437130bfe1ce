import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'

/**
 * @name Address management
 * @version 1.0.0
 * @description Address management for the application
 */
export default class UserAddressController {
  /**
   * @index
   * @summary List all address
   * @description List all address, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, user: User) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const addressQuery = user.related('addresses').query().filter(filters)

    return await addressQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a address
   * @description Create a address with their details (name and details)
   * @requestBody {"name": "", "details": "", "location": {"name": "", "address": "", "regions": {"country": ""}, "coordinates": {"lat": 0, "lng": 0}, "place_id": ""}, "phone": ""}
   * @responseBody 200 - <Address>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, user: User) {
    const { name, details, userId, location, phone } = request.all()

    const address = await user
      .related('addresses')
      .create({ name, details, userId, location, phone })

    return response.json(address)
  }
}
