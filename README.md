# AppInApp API - January 2025 Codebase

This README provides instructions for setting up and running the AppInApp API, extracted from the codebase snapshot of January 5, 2025.

## Table of Contents

1.  [Prerequisites](#prerequisites)
2.  [Installation](#installation)
3.  [Configuration](#configuration)
4.  [Database Setup](#database-setup)
5.  [Running the API](#running-the-api)
6.  [Telegram Bot](#telegram-bot)
7. [Running Queue Worker](#running-queue-worker)
8.  [Code Linting and Formatting](#code-linting-and-formatting)
9.  [Generating API Docs](#generating-api-docs)
10. [Testing](#testing)
11. [Deployment](#deployment)

## Prerequisites

Before you begin, ensure you have the following installed:

*   **Node.js:** Version >= 21.0.0 (check your version with `node -v`). It's recommended to use nvm to manage your node.js versions
*   **npm or yarn:** Package manager (npm is included with Node.js).
*   **PostgreSQL:** Database (check if it's running on your system)
*   **MySQL:** Database (check if it's running on your system)
*   **Redis:** (check if it's running on your system) Used for Queueing
*   **AdonisJS CLI:** Install globally using `npm i -g @adonisjs/core`
*   **Telegram Bot Token:** You will need a bot token from Telegram to run telegram bot functionalities

## Installation

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/EpicAp-AppinApp/aia-api.git
    cd aia-api
    ```

2.  **Install dependencies:**

    ```bash
    npm install # Or yarn install
    ```

## Configuration

1.  **Copy `.env.example` to `.env`:**

    ```bash
    cp .env.example .env
    ```

2.  **Update `.env`:**

    *   Set the `PORT`, `HOST`, `APP_KEY`, and `APP_URL` (This should be the public URL of your deployed application).
    *   Configure your database connection (`DB_CONNECTION`, `PG_HOST`, `PG_PORT`, `PG_USER`, `PG_PASSWORD`, `PG_DB_NAME` and `MYSQL_*`) to match your local PostgreSQL and MySQL setup.
    *   Configure SMTP settings for email sending (`SMTP_HOST`, `SMTP_PORT`, `SMTP_USERNAME`, `SMTP_PASSWORD`, `MAIL_DOMAIN`).
    *   Configure social login credentials, such as `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`, etc.
    *   Setup SMS provider keys
    *   Configure MPESA credentials
    *   Setup your telegram bot keys and create a telegram bot
    *   Set queue values (QUEUE_REDIS_HOST, QUEUE_REDIS_PORT, QUEUE_REDIS_PASSWORD)

    ```
    PORT=3333
    HOST=0.0.0.0
    NODE_ENV=development

    APP_KEY=YOUR_APP_KEY

    APP_URL='https://your-deployed-app.com'

    APP_VERSION=1

    DRIVE_DISK=local
    # DRIVE_DISK=s3

    DB_CONNECTION=pg

    PG_HOST=localhost
    PG_PORT=5432
    PG_USER=YOUR_PG_USER
    PG_PASSWORD=YOUR_PG_PASSWORD
    PG_DB_NAME=aia

    MYSQL_HOST=localhost
    MYSQL_PORT=3306
    MYSQL_USER=YOUR_MYSQL_USER
    MYSQL_PASSWORD=YOUR_MYSQL_PASSWORD
    MYSQL_DATABASE=YOUR_MYSQL_DATABASE

    SMTP_HOST='mail.example.com'
    SMTP_PORT=465
    SMTP_USERNAME='<EMAIL>'
    SMTP_PASSWORD='YOUR_SMTP_PASSWORD'
    MAIL_DOMAIN='<EMAIL>'

    GOOGLE_CLIENT_ID=YOUR_GOOGLE_CLIENT_ID
    GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_CLIENT_SECRET

    FACEBOOK_CLIENT_ID=YOUR_FACEBOOK_CLIENT_ID
    FACEBOOK_CLIENT_SECRET=YOUR_FACEBOOK_CLIENT_SECRET

    CACHE_VIEWS=false

    FCM_API_KEY=YOUR_FCM_API_KEY

    REDIS_CONNECTION=local
    REDIS_HOST=127.0.0.1
    REDIS_PORT=6379
    REDIS_PASSWORD=YOUR_REDIS_PASSWORD

    S3_KEY=YOUR_S3_KEY
    S3_SECRET=YOUR_S3_SECRET
    S3_BUCKET=YOUR_S3_BUCKET
    S3_REGION=YOUR_S3_REGION
    S3_ENDPOINT='https://s3.amazonaws.com'

    APPLE_APP_ID='com.example.app'
    APPLE_TEAM_ID='teamId'
    APPLE_CLIENT_ID='clientId'
    APPLE_CLIENT_SECRET='clientSecret'

    SMS_PARTNER_ID='YOUR_SMS_PARTNER_ID'
    SMS_API_KEY='YOUR_SMS_API_KEY'
    SMS_SHORT_CODE='YOUR_SMS_SHORT_CODE'

    MPESA_CONSUMER_KEY='YOUR_MPESA_CONSUMER_KEY'
    MPESA_CONSUMER_SECRET='YOUR_MPESA_CONSUMER_SECRET'
    MPESA_ENVIRONMENT='live'
    MPESA_STORE=YOUR_MPESA_STORE_NUMBER
    MPESA_SHORTCODE=YOUR_MPESA_SHORTCODE
    MPESA_PASSKEY='YOUR_MPESA_PASSKEY'
    MPESA_USERNAME=''
    MPESA_PASSWORD=''

    QUEUE_REDIS_HOST=localhost
    QUEUE_REDIS_PORT=6379
    QUEUE_REDIS_PASSWORD=YOUR_QUEUE_REDIS_PASSWORD
    TELEGRAM_TOKEN=YOUR_TELEGRAM_BOT_TOKEN
    ```

## Database Setup

1.  **Run database migrations:** This will create the necessary tables in your PostgreSQL database.

    ```bash
    node ace migration:run
    ```

    If you wish to start with a fresh database, use :
    ```bash
    node ace migration:fresh --seed
    ```
2.  **Seed the database** *(optional)*: This populates the database with initial data (roles and tasks).

    ```bash
    node ace db:seed
    ```

## Running the API

1.  **Start the Adonis.js server:**

    ```bash
    npm run dev # or node ace serve --watch
    ```

    This starts the development server, watches for file changes, and automatically restarts the server.

2.  **Access the API:**  The API will be accessible at the URL you configured in your `.env` file (e.g., `http://localhost:3333`).

## Telegram Bot

The codebase includes a Telegram bot implementation. To enable it:

1. Obtain Telegram bot token from BotFather in Telegram
2.  **Set the `TELEGRAM_TOKEN` environment variable:** Add the Telegram Bot Token you have generated earlier in your .env file
    ```env
    TELEGRAM_TOKEN=your_telegram_bot_token
    ```

3. Restart Adonis.js Application

## Running Queue Worker

This codebase makes use of a queue worker for a few tasks:

1. To start the queue

```shell
node ace queue:listen