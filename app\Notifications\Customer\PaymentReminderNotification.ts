import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'
import { DateTime } from 'luxon'

interface PaymentReminder {
  billId: string | number
  amount: number
  currency: string
  dueDate: Date
  description?: string
  reminderType: 'first' | 'second' | 'final'
}

export default class PaymentReminderNotification implements NotificationContract {
  constructor(
    private reminder: PaymentReminder,

  ) {}

  public via(_notifiable) {
    return 'database' as const
  }

  public toDatabase() {
    const dueDate = DateTime.fromJSDate(this.reminder.dueDate)
    const now = DateTime.now()
    const daysUntilDue = Math.ceil(dueDate.diff(now, 'days').days)
    
    // Determine urgency based on reminder type and days until due
    let priority = NotificationPriority.MEDIUM
    let urgencyText = ''
    
    if (this.reminder.reminderType === 'final' || daysUntilDue <= 1) {
      priority = NotificationPriority.HIGH
      urgencyText = daysUntilDue <= 0 ? ' - OVERDUE' : ' - Due tomorrow'
    } else if (this.reminder.reminderType === 'second' || daysUntilDue <= 3) {
      priority = NotificationPriority.HIGH
      urgencyText = ` - Due in ${daysUntilDue} days`
    } else {
      urgencyText = ` - Due in ${daysUntilDue} days`
    }

    const reminderTypeText = {
      'first': 'Payment Reminder',
      'second': 'Payment Due Soon',
      'final': 'Final Payment Notice'
    }

    const title = `${reminderTypeText[this.reminder.reminderType]}${urgencyText}`
    const body = `${this.reminder.reminderType === 'final' ? 'FINAL NOTICE: ' : ''}Your payment of ${this.reminder.currency} ${this.reminder.amount} is due ${daysUntilDue <= 1 ? 'tomorrow' : `in ${daysUntilDue} days`}. ${this.reminder.description || ''}`

    return NotificationHelper.createNotificationData(
      title,
      body,
      NotificationHelper.createBillingActions(this.reminder.billId, 'bill'),
      {
        category: NotificationCategory.BILLING,
        notificationType: NotificationType.PAYMENT_REMINDER,
        priority,
        entityId: this.reminder.billId,
        entityType: 'bill',
        amount: this.reminder.amount,
        currency: this.reminder.currency,
        dueDate: this.reminder.dueDate.toISOString(),
        daysUntilDue,
        reminderType: this.reminder.reminderType,
        isUrgent: priority === NotificationPriority.HIGH
      },
      'https://cdn.verful.com/icons/payment-reminder-icon.png'
    )
  }
}
