import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'modifier_options'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('vendor_id').references('id').inTable('vendors').onDelete('SET NULL').nullable().index()
      table.string('name').notNullable()
      table.enum('type', ['preparation', 'condiment', 'extra']).notNullable().index()
      table.text('description').nullable()
      table.decimal('default_price_adjustment', 10, 2).defaultTo(0.00)
      table.boolean('active').defaultTo(true)
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 