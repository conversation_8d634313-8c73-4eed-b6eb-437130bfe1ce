import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import ChargeConfiguration from 'App/Models/ChargeConfiguration'
import { schema, rules } from '@ioc:Adonis/Core/Validator'

export default class ChargeConfigurationsController {
  /**
   * List all charge configurations with filtering
   */
  public async index({ request, response }: HttpContextContract) {
    const query = ChargeConfiguration.query().preload('vendor').preload('branch').preload('service')

    // Apply filters
    if (request.input('vendor_id')) {
      query.where('vendor_id', request.input('vendor_id'))
    }
    if (request.input('branch_id')) {
      query.where('branch_id', request.input('branch_id'))
    }
    if (request.input('service_id')) {
      query.where('service_id', request.input('service_id'))
    }
    if (request.input('is_active') !== undefined) {
      query.where('is_active', request.input('is_active'))
    }
    if (request.input('type')) {
      query.where('type', request.input('type'))
    }

    // Sort by priority and sort_order
    query.orderBy('priority', 'desc')
    query.orderBy('sort_order', 'asc')

    const configurations = await query.exec()
    return response.ok(configurations)
  }

  /**
   * Create a new charge configuration
   */
  public async store({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      name: schema.string({ trim: true }, [rules.required()]),
      code: schema.string({ trim: true }, [
        rules.required(),
        rules.unique({ table: 'charge_configurations', column: 'code' }),
      ]),
      description: schema.string.optional({ trim: true }),
      type: schema.enum(['fixed', 'percentage'] as const),
      amount: schema.number.optional(),
      percentageRate: schema.number.optional(),
      vendorId: schema.string.optional(),
      branchId: schema.string.optional(),
      serviceId: schema.string.optional(),
      conditions: schema.object.optional().anyMembers(),
      isTax: schema.boolean.optional(),
      isMandatory: schema.boolean.optional(),
      isActive: schema.boolean.optional(),
      priority: schema.number.optional(),
      sortOrder: schema.number.optional(),
      currency: schema.string.optional(),
      meta: schema.object.optional().anyMembers(),
    })

    const data = await request.validate({ schema: validationSchema })
    const configuration = await ChargeConfiguration.create(data)
    await configuration.load('vendor')
    await configuration.load('branch')
    await configuration.load('service')

    return response.created(configuration)
  }

  /**
   * Show a specific charge configuration
   */
  public async show({ params, response }: HttpContextContract) {
    const configuration = await ChargeConfiguration.findOrFail(params.id)
    await configuration.load('vendor')
    await configuration.load('branch')
    await configuration.load('service')

    return response.ok(configuration)
  }

  /**
   * Update a charge configuration
   */
  public async update({ params, request, response }: HttpContextContract) {
    const configuration = await ChargeConfiguration.findOrFail(params.id)

    const validationSchema = schema.create({
      name: schema.string.optional({ trim: true }),
      code: schema.string.optional({ trim: true }, [
        rules.unique({
          table: 'charge_configurations',
          column: 'code',
          whereNot: { id: params.id },
        }),
      ]),
      description: schema.string.optional({ trim: true }),
      type: schema.enum.optional(['fixed', 'percentage'] as const),
      amount: schema.number.optional(),
      percentageRate: schema.number.optional(),
      vendorId: schema.string.optional(),
      branchId: schema.string.optional(),
      serviceId: schema.string.optional(),
      conditions: schema.object.optional().anyMembers(),
      isTax: schema.boolean.optional(),
      isMandatory: schema.boolean.optional(),
      isActive: schema.boolean.optional(),
      priority: schema.number.optional(),
      sortOrder: schema.number.optional(),
      currency: schema.string.optional(),
      meta: schema.object.optional().anyMembers(),
    })

    const data = await request.validate({ schema: validationSchema })
    configuration.merge(data)
    await configuration.save()

    await configuration.load('vendor')
    await configuration.load('branch')
    await configuration.load('service')

    return response.ok(configuration)
  }

  /**
   * Delete a charge configuration
   */
  public async destroy({ params, response }: HttpContextContract) {
    const configuration = await ChargeConfiguration.findOrFail(params.id)
    await configuration.delete()

    return response.noContent()
  }

  /**
   * Apply charges to an order
   */
  public async applyToOrder({ request, response }: HttpContextContract) {
    const { orderId, baseAmount } = request.only(['orderId', 'baseAmount'])

    // Ensure baseAmount is numeric
    const numericBaseAmount = Number(baseAmount) || 0

    // Get all applicable charge configurations
    const configurations = await ChargeConfiguration.query()
      .where('is_active', true)
      .orderBy('priority', 'desc')
      .orderBy('sort_order', 'asc')
      .exec()

    const charges: Array<{
      configurationId: number
      name: string
      type: string
      amount: number
      isTax: boolean
      isMandatory: boolean
    }> = []
    let totalCharges = 0

    // Apply each charge configuration
    for (const config of configurations) {
      const isApplicable = await config.isApplicable({ id: orderId }, numericBaseAmount)
      if (isApplicable) {
        const chargeAmount = config.calculateCharge(numericBaseAmount)
        const numericChargeAmount = Number(chargeAmount) || 0

        charges.push({
          configurationId: config.id,
          name: config.name,
          type: config.type,
          amount: numericChargeAmount,
          isTax: config.isTax,
          isMandatory: config.isMandatory,
        })
        totalCharges += numericChargeAmount
      }
    }

    // Ensure all values are properly numeric
    const numericTotalCharges = Number(totalCharges.toFixed(2))
    const numericFinalAmount = Number((numericBaseAmount + numericTotalCharges).toFixed(2))

    return response.ok({
      charges,
      totalCharges: numericTotalCharges,
      finalAmount: numericFinalAmount,
    })
  }
}
