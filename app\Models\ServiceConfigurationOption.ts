import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate, belongsTo, BelongsTo, computed } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'
import ServiceConfiguration from './ServiceConfiguration'
import Duration from './Duration'

export enum ServiceOptionType {
  DURATION = 'duration',
  LOCATION = 'location',
  PERSONNEL = 'personnel',
  EQUIPMENT = 'equipment',
  DELIVERY_METHOD = 'delivery_method',
  EXPERTISE_LEVEL = 'expertise_level',
  ADD_ON = 'add_on',
  SCHEDULING = 'scheduling',
  CUSTOM = 'custom'
}

export default class ServiceConfigurationOption extends BaseModel {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public serviceConfigurationId: string

  @column()
  public name: string

  @column()
  public type: ServiceOptionType

  @column()
  public description: string | null

  @column()
  public priceAdjustment: number

  @column()
  public durationId: string | null

  @column()
  public isDefault: boolean

  @column()
  public sortOrder: number

  @column()
  public constraints: Record<string, any>

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(option: ServiceConfigurationOption) {
    option.id = ulid().toLowerCase()
  }

  @belongsTo(() => ServiceConfiguration)
  public serviceConfiguration: BelongsTo<typeof ServiceConfiguration>

  @belongsTo(() => Duration)
  public duration: BelongsTo<typeof Duration>

  @computed()
  public get isDurationOption(): boolean {
    return this.type === ServiceOptionType.DURATION
  }

  @computed()
  public get hasCalendarIntegration(): boolean {
    return this.isDurationOption && !!this.durationId
  }

  @computed()
  public get displayPrice(): string {
    if (this.priceAdjustment === 0) {
      return 'Included'
    } else if (this.priceAdjustment > 0) {
      return `+$${this.priceAdjustment.toFixed(2)}`
    } else {
      return `-$${Math.abs(this.priceAdjustment).toFixed(2)}`
    }
  }

  /**
   * Get the calendar block time if this is a duration option
   */
  public async getCalendarBlockMinutes(): Promise<number | null> {
    if (!this.isDurationOption || !this.durationId) {
      return null
    }

    if (!this.duration) {
      await this.load('duration')
    }

    return this.duration?.calendarBlockMinutes || null
  }

  /**
   * Check if this option can be combined with another option
   */
  public canCombineWith(otherOption: ServiceConfigurationOption): boolean {
    // Same type options generally can't be combined (except add-ons)
    if (this.type === otherOption.type && this.type !== ServiceOptionType.ADD_ON) {
      return false
    }

    // Check constraints
    if (this.constraints.excludes && Array.isArray(this.constraints.excludes)) {
      if (this.constraints.excludes.includes(otherOption.id) || 
          this.constraints.excludes.includes(otherOption.type)) {
        return false
      }
    }

    if (otherOption.constraints.excludes && Array.isArray(otherOption.constraints.excludes)) {
      if (otherOption.constraints.excludes.includes(this.id) || 
          otherOption.constraints.excludes.includes(this.type)) {
        return false
      }
    }

    return true
  }

  /**
   * Check if this option requires other options
   */
  public getRequiredOptions(): string[] {
    return this.constraints.requires || []
  }

  /**
   * Check if this option has dependencies that are satisfied
   */
  public async checkDependencies(selectedOptionIds: string[]): Promise<{ satisfied: boolean; missing: string[] }> {
    const required = this.getRequiredOptions()
    const missing = required.filter(reqId => !selectedOptionIds.includes(reqId))

    return {
      satisfied: missing.length === 0,
      missing
    }
  }

  /**
   * Validate the option configuration
   */
  public async validateConfiguration(): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Validate duration reference for duration options
    if (this.isDurationOption) {
      if (!this.durationId) {
        errors.push('Duration options must have a valid duration ID')
      } else {
        // Check if duration exists and is active
        const duration = await Duration.find(this.durationId)
        if (!duration) {
          errors.push(`Referenced duration ID '${this.durationId}' does not exist`)
        } else if (!duration.active) {
          errors.push(`Referenced duration '${duration.name}' is not active`)
        }
      }
    } else {
      // Non-duration options should not have duration ID
      if (this.durationId) {
        errors.push('Non-duration options should not have a duration ID')
      }
    }

    // Validate constraints
    if (this.constraints) {
      if (this.constraints.requires && !Array.isArray(this.constraints.requires)) {
        errors.push('Constraint "requires" must be an array')
      }
      if (this.constraints.excludes && !Array.isArray(this.constraints.excludes)) {
        errors.push('Constraint "excludes" must be an array')
      }
    }

    // Validate sort order
    if (this.sortOrder < 0) {
      errors.push('Sort order must be non-negative')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Create option templates for common option types
   */
  public static createTemplate(
    name: string,
    type: ServiceOptionType,
    serviceConfigurationId: string,
    options: Partial<ServiceConfigurationOption> = {}
  ): Partial<ServiceConfigurationOption> {
    const defaultsByType = {
      [ServiceOptionType.DURATION]: {
        description: 'Service duration option',
        priceAdjustment: 0,
        isDefault: true,
        sortOrder: 1,
        constraints: {}
      },
      [ServiceOptionType.PERSONNEL]: {
        description: 'Personnel/staff option',
        priceAdjustment: 0,
        isDefault: true,
        sortOrder: 10,
        constraints: {}
      },
      [ServiceOptionType.EQUIPMENT]: {
        description: 'Equipment option',
        priceAdjustment: 0,
        isDefault: false,
        sortOrder: 20,
        constraints: {}
      },
      [ServiceOptionType.LOCATION]: {
        description: 'Location/delivery option',
        priceAdjustment: 0,
        isDefault: false,
        sortOrder: 30,
        constraints: {}
      },
      [ServiceOptionType.ADD_ON]: {
        description: 'Additional service option',
        priceAdjustment: 25,
        isDefault: false,
        sortOrder: 40,
        constraints: {}
      },
      [ServiceOptionType.DELIVERY_METHOD]: {
        description: 'Service delivery method',
        priceAdjustment: 0,
        isDefault: false,
        sortOrder: 15,
        constraints: {}
      },
      [ServiceOptionType.EXPERTISE_LEVEL]: {
        description: 'Expertise level option',
        priceAdjustment: 0,
        isDefault: false,
        sortOrder: 5,
        constraints: {}
      },
      [ServiceOptionType.SCHEDULING]: {
        description: 'Scheduling preference option',
        priceAdjustment: 0,
        isDefault: false,
        sortOrder: 35,
        constraints: {}
      },
      [ServiceOptionType.CUSTOM]: {
        description: 'Custom service option',
        priceAdjustment: 0,
        isDefault: false,
        sortOrder: 50,
        constraints: {}
      }
    }

    return {
      name,
      type,
      serviceConfigurationId,
      durationId: type === ServiceOptionType.DURATION ? null : undefined,
      active: true,
      ...defaultsByType[type],
      ...options
    }
  }

  /**
   * Get options by type for a configuration
   */
  public static async getByType(serviceConfigurationId: string, type: ServiceOptionType) {
    return await ServiceConfigurationOption.query()
      .where('serviceConfigurationId', serviceConfigurationId)
      .where('type', type)
      .where('active', true)
      .orderBy('sortOrder')
  }

  /**
   * Get default options for a configuration
   */
  public static async getDefaults(serviceConfigurationId: string) {
    return await ServiceConfigurationOption.query()
      .where('serviceConfigurationId', serviceConfigurationId)
      .where('isDefault', true)
      .where('active', true)
      .orderBy('sortOrder')
  }
}
