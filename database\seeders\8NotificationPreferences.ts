import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Database from '@ioc:Adonis/Lucid/Database'

export default class NotificationPreferencesSeeder extends BaseSeeder {
  public async run() {
    // Default notification preferences for different types
    const defaultPreferences = [
      {
        user_id: null, // Global default for all users
        vendor_id: null, // Global default for all vendors
        type: 'delivery',
        channels: JSON.stringify(['email', 'sms', 'push']),
        preferences: JSON.stringify({
          email: true,
          sms: true,
          push: true,
          order_assigned: true,
          order_picked_up: true,
          order_in_transit: true,
          order_delivered: true,
          order_failed: true,
          order_cancelled: true,
        }),
        meta: JSON.stringify({
          description: 'Default delivery notification preferences',
          is_global_default: true,
        }),
      },
      {
        user_id: null,
        vendor_id: null,
        type: 'system',
        channels: JSON.stringify(['email', 'push']),
        preferences: JSON.stringify({
          email: true,
          sms: false,
          push: true,
          account_updates: true,
          security_alerts: true,
          feature_announcements: false,
          maintenance_notices: true,
          billing_notifications: true,
        }),
        meta: JSON.stringify({
          description: 'Default system notification preferences',
          is_global_default: true,
        }),
      },
      {
        user_id: null,
        vendor_id: null,
        type: 'marketing',
        channels: JSON.stringify(['email']),
        preferences: JSON.stringify({
          email: false,
          sms: false,
          push: false,
          promotional_offers: false,
          newsletter: false,
          product_updates: false,
          vendor_recommendations: false,
        }),
        meta: JSON.stringify({
          description: 'Default marketing notification preferences (opt-out by default)',
          is_global_default: true,
        }),
      },
      {
        user_id: null,
        vendor_id: null,
        type: 'order',
        channels: JSON.stringify(['email', 'sms', 'push']),
        preferences: JSON.stringify({
          email: true,
          sms: true,
          push: true,
          order_confirmation: true,
          payment_confirmation: true,
          order_status_updates: true,
          order_ready: true,
          order_completed: true,
          refund_processed: true,
        }),
        meta: JSON.stringify({
          description: 'Default order notification preferences',
          is_global_default: true,
        }),
      },
      {
        user_id: null,
        vendor_id: null,
        type: 'vendor',
        channels: JSON.stringify(['email', 'push']),
        preferences: JSON.stringify({
          email: true,
          sms: false,
          push: true,
          new_orders: true,
          payment_received: true,
          customer_reviews: true,
          inventory_alerts: true,
          performance_reports: false,
          subscription_updates: true,
        }),
        meta: JSON.stringify({
          description: 'Default vendor notification preferences',
          is_global_default: true,
        }),
      },
    ]

    // Insert preferences in a transaction
    const trx = await Database.transaction()

    try {
      for (const preference of defaultPreferences) {
        const exists = await trx
          .from('notification_preferences')
          .where('user_id', preference.user_id || '')
          .where('vendor_id', preference.vendor_id || '')
          .where('type', preference.type)
          .first()

        if (!exists) {
          await trx.table('notification_preferences').insert({
            ...preference,
            created_at: new Date(),
            updated_at: new Date(),
          })
        }
      }

      await trx.commit()
      console.log('✅ Default notification preferences seeded successfully')
    } catch (error) {
      await trx.rollback()
      console.error('❌ Error seeding notification preferences:', error)
      throw error
    }
  }
}
