import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { DateTime } from 'luxon'
import Booking, { BookingStatus } from 'App/Models/Booking'
import Branch from 'App/Models/Branch'

export default class BookingCalendarController {
  /**
   * Get branch calendar view with all bookings
   */
  public async getBranchCalendar({ params, request, response }: HttpContextContract) {
    try {
      const { branchId } = params
      const {
        startDate,
        endDate,
        view = 'month', // month, week, day
        status,
        customerId,
        staffId
      } = request.qs()

      // Validate branch exists
      await Branch.findOrFail(branchId)

      // Parse dates or use defaults
      let parsedStartDate: DateTime
      let parsedEndDate: DateTime

      if (startDate && endDate) {
        parsedStartDate = DateTime.fromISO(startDate)
        parsedEndDate = DateTime.fromISO(endDate)
      } else {
        // Default to current month
        const now = DateTime.now()
        parsedStartDate = now.startOf('month')
        parsedEndDate = now.endOf('month')
      }

      if (!parsedStartDate.isValid || !parsedEndDate.isValid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid date format. Use ISO format (YYYY-MM-DD)'
        })
      }

      // Build query
      const query = Booking.query()
        .where('branchId', branchId)
        .whereBetween('appointmentStart', [parsedStartDate.toISO(), parsedEndDate.toISO()])
        .preload('customer')
        .preload('product')
        .orderBy('appointmentStart')

      // Apply filters
      if (status) {
        query.where('status', status)
      }

      if (customerId) {
        query.where('customerId', customerId)
      }

      if (staffId) {
        query.whereRaw("JSON_CONTAINS(staff_assignments, JSON_OBJECT('userId', ?))", [staffId])
      }

      const bookings = await query

      // Format bookings for calendar display
      const calendarEvents = bookings.map(booking => ({
        id: booking.id,
        title: `${booking.product.name} - ${booking.customer.name}`,
        start: booking.appointmentStart.toISO(),
        end: booking.appointmentEnd.toISO(),
        status: booking.status,
        customer: {
          id: booking.customer.id,
          name: booking.customer.name,
          email: booking.customer.email,
          phone: booking.customer.phone
        },
        service: {
          id: booking.product.id,
          name: booking.product.name
        },
        duration: booking.durationMinutes,
        totalPrice: booking.totalPrice,
        confirmationCode: booking.confirmationCode,
        staffAssignments: booking.staffAssignments,
        equipmentReservations: booking.equipmentReservations,
        bookingNotes: booking.bookingNotes
      }))

      // Generate calendar statistics
      const stats = {
        totalBookings: bookings.length,
        statusBreakdown: this.getStatusBreakdown(bookings),
        dailyBreakdown: this.getDailyBreakdown(bookings, parsedStartDate, parsedEndDate),
        revenue: bookings.reduce((total, booking) => total + booking.totalPrice, 0)
      }

      return response.json({
        success: true,
        data: {
          calendar: {
            view,
            startDate: parsedStartDate.toFormat('yyyy-MM-dd'),
            endDate: parsedEndDate.toFormat('yyyy-MM-dd'),
            events: calendarEvents
          },
          statistics: stats
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get branch calendar',
        error: error.message
      })
    }
  }

  /**
   * Get customer's booking calendar
   */
  public async getCustomerCalendar({ params, request, response }: HttpContextContract) {
    try {
      const { customerId } = params
      const {
        startDate,
        endDate,
        status,
        branchId
      } = request.qs()

      // Parse dates or use defaults (next 3 months)
      let parsedStartDate: DateTime
      let parsedEndDate: DateTime

      if (startDate && endDate) {
        parsedStartDate = DateTime.fromISO(startDate)
        parsedEndDate = DateTime.fromISO(endDate)
      } else {
        const now = DateTime.now()
        parsedStartDate = now.startOf('day')
        parsedEndDate = now.plus({ months: 3 }).endOf('day')
      }

      if (!parsedStartDate.isValid || !parsedEndDate.isValid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid date format. Use ISO format (YYYY-MM-DD)'
        })
      }

      // Build query
      const query = Booking.query()
        .where('customerId', customerId)
        .whereBetween('appointmentStart', [parsedStartDate.toISO(), parsedEndDate.toISO()])
        .preload('branch')
        .preload('vendor')
        .preload('product')
        .orderBy('appointmentStart')

      // Apply filters
      if (status) {
        query.where('status', status)
      }

      if (branchId) {
        query.where('branchId', branchId)
      }

      const bookings = await query

      // Format bookings for customer view
      const customerBookings = bookings.map(booking => ({
        id: booking.id,
        appointmentStart: booking.appointmentStart.toISO(),
        appointmentEnd: booking.appointmentEnd.toISO(),
        status: booking.status,
        confirmationCode: booking.confirmationCode,
        service: {
          id: booking.product.id,
          name: booking.product.name
        },
        vendor: {
          id: booking.vendor.id,
          name: booking.vendor.name
        },
        branch: {
          id: booking.branch.id,
          name: booking.branch.name,
          location: booking.branch.location
        },
        duration: booking.durationMinutes,
        totalPrice: booking.totalPrice,
        selectedServiceOptions: booking.selectedServiceOptions,
        bookingNotes: booking.bookingNotes,
        canCancel: booking.canBeCancelled(),
        canModify: booking.canBeModified()
      }))

      return response.json({
        success: true,
        data: {
          customerId,
          dateRange: {
            startDate: parsedStartDate.toFormat('yyyy-MM-dd'),
            endDate: parsedEndDate.toFormat('yyyy-MM-dd')
          },
          bookings: customerBookings,
          summary: {
            totalBookings: bookings.length,
            upcomingBookings: bookings.filter(b => b.isUpcoming).length,
            completedBookings: bookings.filter(b => b.status === BookingStatus.COMPLETED).length,
            totalSpent: bookings.filter(b => b.status === BookingStatus.COMPLETED)
              .reduce((total, booking) => total + booking.totalPrice, 0)
          }
        }
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get customer calendar',
        error: error.message
      })
    }
  }

  /**
   * Get calendar statistics for a date range
   */
  public async getCalendarStats({ params, request, response }: HttpContextContract) {
    try {
      const { branchId } = params
      const {
        startDate,
        endDate,
        groupBy = 'day' // day, week, month
      } = request.qs()

      // Validate branch exists
      await Branch.findOrFail(branchId)

      // Parse dates
      const parsedStartDate = DateTime.fromISO(startDate)
      const parsedEndDate = DateTime.fromISO(endDate)

      if (!parsedStartDate.isValid || !parsedEndDate.isValid) {
        return response.status(400).json({
          success: false,
          message: 'Invalid date format. Use ISO format (YYYY-MM-DD)'
        })
      }

      // Get bookings for the period
      const bookings = await Booking.query()
        .where('branchId', branchId)
        .whereBetween('appointmentStart', [parsedStartDate.toISO(), parsedEndDate.toISO()])

      // Calculate statistics
      const stats = {
        overview: {
          totalBookings: bookings.length,
          totalRevenue: bookings.reduce((sum, booking) => sum + booking.totalPrice, 0),
          averageBookingValue: bookings.length > 0 
            ? bookings.reduce((sum, booking) => sum + booking.totalPrice, 0) / bookings.length 
            : 0,
          statusBreakdown: this.getStatusBreakdown(bookings)
        },
        timeline: this.getTimelineStats(bookings, parsedStartDate, parsedEndDate, groupBy),
        busyHours: this.getBusyHoursStats(bookings),
        popularServices: this.getPopularServicesStats(bookings)
      }

      return response.json({
        success: true,
        data: stats
      })

    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to get calendar statistics',
        error: error.message
      })
    }
  }

  // Helper methods
  private getStatusBreakdown(bookings: Booking[]) {
    const breakdown = {}
    Object.values(BookingStatus).forEach(status => {
      breakdown[status] = bookings.filter(booking => booking.status === status).length
    })
    return breakdown
  }

  private getDailyBreakdown(bookings: Booking[], startDate: DateTime, endDate: DateTime) {
    const breakdown = {}
    let currentDate = startDate

    while (currentDate <= endDate) {
      const dateKey = currentDate.toFormat('yyyy-MM-dd')
      breakdown[dateKey] = bookings.filter(booking => 
        booking.appointmentStart.toFormat('yyyy-MM-dd') === dateKey
      ).length
      currentDate = currentDate.plus({ days: 1 })
    }

    return breakdown
  }

  private getTimelineStats(bookings: Booking[], startDate: DateTime, endDate: DateTime, groupBy: string) {
    // Implementation for timeline statistics based on groupBy parameter
    // This would group bookings by day/week/month and return counts/revenue
    return {}
  }

  private getBusyHoursStats(bookings: Booking[]) {
    const hourCounts = {}
    
    bookings.forEach(booking => {
      const hour = booking.appointmentStart.hour
      hourCounts[hour] = (hourCounts[hour] || 0) + 1
    })

    return Object.entries(hourCounts)
      .map(([hour, count]) => ({ hour: parseInt(hour), bookings: count }))
      .sort((a, b) => b.bookings - a.bookings)
  }

  private getPopularServicesStats(bookings: Booking[]) {
    const serviceCounts = {}
    
    bookings.forEach(booking => {
      const productId = booking.productId
      serviceCounts[productId] = (serviceCounts[productId] || 0) + 1
    })

    return Object.entries(serviceCounts)
      .map(([productId, count]) => ({ productId, bookings: count }))
      .sort((a, b) => b.bookings - a.bookings)
      .slice(0, 10) // Top 10
  }
}
