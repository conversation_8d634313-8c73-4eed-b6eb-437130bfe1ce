import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import StaffRating from '../../Models/StaffRating'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name StaffRating management
 * @version 1.0.0
 * @description StaffRating management for the application
 */
export default class StaffRatingsController {
  /**
   * @index
   * @summary List all ratings
   * @description List all ratings, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const ratingQuery = StaffRating.filter(filters)

    return await ratingQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a rating
   * @description Create a rating with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <StaffRating>
   */
  public async store({ request, response }: HttpContextContract) {
    const { points, comment, customerId } = request.all()
    const rating = StaffRating.create({ points, comment, customerId })

    return response.json(rating)
  }

  @bind()
  /**
   * @show
   * @summary Show a single rating
   * @description Show a rating with their details (name and details)
   * @paramPath id required number - StaffRating ID
   * @responseBody 200 - <StaffRating>
   * @response 404 - StaffRating not found
   */
  public async show({ response }: HttpContextContract, rating: StaffRating) {
    return response.json(rating)
  }

  @bind()
  /**
   * @update
   * @summary Update a rating
   * @description Update a rating with their details (name and details)
   * @paramPath id required number - StaffRating ID
   * @requestBody <StaffRating>
   * @responseBody 200 - <StaffRating>
   * @response 404 - StaffRating not found
   */
  public async update({ request, response }: HttpContextContract, rating: StaffRating) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await rating.merge(input).save()

    return response.json(rating)
  }

  @bind()

  /**
   * @destroy
   * @responsebody 204 - Not found
   */
  public async destroy(_: HttpContextContract, rating: StaffRating) {
    return await rating.delete()
  }
}
