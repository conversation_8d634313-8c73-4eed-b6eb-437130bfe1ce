import { NotificationHelper } from './NotificationHelper'
import { NotificationActionType } from 'App/Enums/NotificationActionType'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

/**
 * Helper class for migrating legacy notifications to contract-compliant format
 */
export class NotificationMigrationHelper {
  
  /**
   * Convert legacy action format to contract-compliant format
   */
  static convertLegacyAction(legacyAction: {
    screen: string
    label: string
    args: Record<string, any>
  }) {
    const { screen, label, args } = legacyAction
    
    // Map legacy screens to semantic action types
    const screenToActionMap: Record<string, string> = {
      '/orders/[orderId]': NotificationActionType.VIEW_ORDER,
      'OrderDetails': NotificationActionType.VIEW_ORDER,
      '/temp-orders/[orderId]': NotificationActionType.VIEW_TEMP_ORDER,
      'temp-orders': NotificationActionType.VIEW_TEMP_ORDER,
      '/billing/[billId]': NotificationActionType.VIEW_BILL_DETAILS,
      '/invoices/[invoiceId]': NotificationActionType.VIEW_INVOICE,
      '/payments/[paymentId]': NotificationActionType.VIEW_PAYMENT_HISTORY,
      'Home': NotificationActionType.VIEW_DETAILS,
      '/subscriptions': NotificationActionType.VIEW_SUBSCRIPTION,
      '/notifications': NotificationActionType.VIEW_DETAILS
    }
    
    // Try to find exact match first
    let actionType = screenToActionMap[screen]
    
    // If no exact match, try pattern matching
    if (!actionType) {
      if (screen.includes('order')) {
        actionType = NotificationActionType.VIEW_ORDER
      } else if (screen.includes('bill')) {
        actionType = NotificationActionType.VIEW_BILL_DETAILS
      } else if (screen.includes('invoice')) {
        actionType = NotificationActionType.VIEW_INVOICE
      } else if (screen.includes('payment')) {
        actionType = NotificationActionType.VIEW_PAYMENT_HISTORY
      } else if (screen.includes('subscription')) {
        actionType = NotificationActionType.VIEW_SUBSCRIPTION
      } else {
        actionType = NotificationActionType.VIEW_DETAILS
      }
    }
    
    return NotificationHelper.createAction(actionType, label, args)
  }
  
  /**
   * Convert legacy notification data to contract-compliant format
   */
  static convertLegacyNotification(legacyData: {
    title: string
    body: string
    actions?: Array<{screen: string, label: string, args: Record<string, any>}>
    meta?: Record<string, any>
    icon?: string
    url?: string
  }, notificationClass: string) {
    
    const { title, body, actions = [], meta = {}, icon, url } = legacyData
    
    // Convert legacy actions
    const convertedActions = actions.map(action => this.convertLegacyAction(action))
    
    // Determine category and type based on notification class
    const { category, notificationType, priority } = this.inferNotificationMetadata(notificationClass, title, body)
    
    // Extract entity information from meta
    const entityId = meta.orderId || meta.billId || meta.invoiceId || meta.subscriptionId
    const entityType = this.inferEntityType(meta)
    
    return NotificationHelper.createNotificationData(
      title,
      body,
      convertedActions,
      {
        category,
        notificationType,
        priority,
        entityId,
        entityType,
        ...meta,
        migrated: true,
        originalClass: notificationClass
      },
      icon,
      url
    )
  }
  
  /**
   * Infer notification metadata from class name and content
   */
  private static inferNotificationMetadata(notificationClass: string, title: string, body: string) {
    const className = notificationClass.toLowerCase()
    const titleLower = title.toLowerCase()
    const bodyLower = body.toLowerCase()
    
    // Determine category
    let category = NotificationCategory.SYSTEM
    let notificationType = NotificationType.BROADCAST_ANNOUNCEMENT
    let priority = NotificationPriority.MEDIUM
    
    // Order-related
    if (className.includes('order') || titleLower.includes('order') || bodyLower.includes('order')) {
      category = NotificationCategory.ORDER
      
      if (className.includes('new') || titleLower.includes('new')) {
        notificationType = NotificationType.ORDER_CREATED
      } else if (className.includes('status') || titleLower.includes('status')) {
        notificationType = NotificationType.ORDER_STATUS_CHANGE
      } else if (className.includes('temp')) {
        notificationType = NotificationType.TEMP_ORDER_CREATED
      } else {
        notificationType = NotificationType.ORDER_CREATED
      }
      
      priority = NotificationPriority.MEDIUM
    }
    
    // Payment-related
    else if (className.includes('payment') || titleLower.includes('payment') || bodyLower.includes('payment')) {
      category = NotificationCategory.BILLING
      
      if (className.includes('received') || titleLower.includes('received') || bodyLower.includes('received')) {
        notificationType = NotificationType.PAYMENT_RECEIVED
        priority = NotificationPriority.MEDIUM
      } else if (className.includes('failure') || className.includes('failed') || titleLower.includes('failed')) {
        notificationType = NotificationType.PAYMENT_FAILED
        priority = NotificationPriority.HIGH
      } else {
        notificationType = NotificationType.PAYMENT_RECEIVED
        priority = NotificationPriority.MEDIUM
      }
    }
    
    // Billing-related
    else if (className.includes('invoice') || titleLower.includes('invoice') || bodyLower.includes('invoice')) {
      category = NotificationCategory.BILLING
      notificationType = NotificationType.INVOICE_GENERATED
      priority = NotificationPriority.MEDIUM
    }
    
    // Broadcast-related
    else if (className.includes('broadcast') || titleLower.includes('broadcast')) {
      category = NotificationCategory.PROMOTION
      notificationType = NotificationType.BROADCAST_ANNOUNCEMENT
      priority = NotificationPriority.LOW
    }
    
    // Subscription-related
    else if (className.includes('subscription') || titleLower.includes('subscription')) {
      category = NotificationCategory.ACCOUNT
      notificationType = NotificationType.SUBSCRIPTION_CREATED
      priority = NotificationPriority.MEDIUM
    }
    
    // Welcome-related
    else if (className.includes('welcome') || titleLower.includes('welcome')) {
      category = NotificationCategory.ACCOUNT
      notificationType = NotificationType.WELCOME
      priority = NotificationPriority.MEDIUM
    }
    
    // OTP-related
    else if (className.includes('otp') || titleLower.includes('otp')) {
      category = NotificationCategory.ACCOUNT
      notificationType = NotificationType.OTP_VERIFICATION
      priority = NotificationPriority.HIGH
    }
    
    return { category, notificationType, priority }
  }
  
  /**
   * Infer entity type from meta data
   */
  private static inferEntityType(meta: Record<string, any>): string | undefined {
    if (meta.orderId) return 'order'
    if (meta.billId) return 'bill'
    if (meta.invoiceId) return 'invoice'
    if (meta.subscriptionId) return 'subscription'
    if (meta.paymentId) return 'payment'
    if (meta.userId) return 'user'
    
    return undefined
  }
  
  /**
   * Validate that a notification is contract-compliant
   */
  static validateContractCompliance(notificationData: any): {
    isCompliant: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []
    
    // Check required fields
    if (!notificationData.title) errors.push('Missing required field: title')
    if (!notificationData.body) errors.push('Missing required field: body')
    if (!notificationData.actions) errors.push('Missing required field: actions')
    if (!notificationData.meta) errors.push('Missing required field: meta')
    
    // Check meta structure
    if (notificationData.meta) {
      if (!notificationData.meta.category) errors.push('Missing required meta field: category')
      if (!notificationData.meta.notificationType) errors.push('Missing required meta field: notificationType')
      if (!notificationData.meta.priority) errors.push('Missing required meta field: priority')
      
      // Validate enum values
      if (notificationData.meta.category && !NotificationCategory.isValid(notificationData.meta.category)) {
        errors.push(`Invalid category: ${notificationData.meta.category}`)
      }
      
      if (notificationData.meta.notificationType && !NotificationType.isValid(notificationData.meta.notificationType)) {
        errors.push(`Invalid notification type: ${notificationData.meta.notificationType}`)
      }
      
      if (notificationData.meta.priority && !NotificationPriority.isValid(notificationData.meta.priority)) {
        errors.push(`Invalid priority: ${notificationData.meta.priority}`)
      }
    }
    
    // Check actions structure
    if (notificationData.actions && Array.isArray(notificationData.actions)) {
      notificationData.actions.forEach((action, index) => {
        if (!action.type && !action.screen) {
          errors.push(`Action ${index}: Missing both 'type' and 'screen' fields`)
        }
        
        if (action.type && !NotificationActionType.isValid(action.type)) {
          errors.push(`Action ${index}: Invalid action type: ${action.type}`)
        }
        
        if (!action.label) {
          errors.push(`Action ${index}: Missing required field: label`)
        }
        
        // Warn about legacy format
        if (action.screen && !action.type) {
          warnings.push(`Action ${index}: Using legacy 'screen' format, consider migrating to 'type'`)
        }
      })
    }
    
    return {
      isCompliant: errors.length === 0,
      errors,
      warnings
    }
  }
}
