import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'

// interface StaffFilters {
//   per?: number
//   page?: number
//   order?: string
//   sort?: string
//   [key: string]: any
// }

interface StaffResponse {
  id: number
  user_id: number
  online: boolean
  identifier: string
  first_name: string
  last_name: string
  phone: string
  email: string
  idpass: string
  title: string
  gender: string
  status: string
  branch: {
    id: number
    name: string
    email: string
    phone: string
    details: string
    location: string
  }
  vendor: {
    id: number
    name: string
    slug: string
    details: string
    email: string
    phone: string
    reg: string
    kra: string
    cover: string
    logo: string
    active: boolean
    featured: boolean
  }
  roles: string[]
}

interface PaginationMeta {
  total: number
  perPage: number
  currentPage: number
  lastPage: number
  firstPage: number
  firstPageUrl: string
  lastPageUrl: string
  nextPageUrl: string | null
  previousPageUrl: string | null
}

export default class StaffController {
  private buildBaseQuery() {
    return Database.from('staff')
      .leftJoin('users', 'staff.user_id', 'users.id')
      .leftJoin('branches', 'staff.branch_id', 'branches.id')
      .leftJoin('vendors', 'staff.vendor_id', 'vendors.id')
      .leftJoin('user_roles', 'staff.user_id', 'user_roles.model_id')
      .leftJoin('roles', 'user_roles.role_id', 'roles.id')
      .select(
        'staff.id',
        'staff.user_id',
        'staff.branch_id',
        'staff.vendor_id',
        'staff.identifier',
        'staff.online',
        'staff.created_at',
        'staff.updated_at',
        'roles.name as user_role_name',
        'users.first_name as user_first_name',
        'users.last_name as user_last_name',
        'users.phone as user_phone',
        'users.email as user_email',
        'users.idpass as user_idpass',
        'users.title as user_title',
        'users.gender as user_gender',
        'users.status as user_status',
        'branches.name as branch_name',
        'branches.details as branch_details',
        'branches.email as branch_email',
        'branches.phone as branch_phone',
        'branches.location as branch_location',
        'vendors.name as vendor_name',
        'vendors.slug as vendor_slug',
        'vendors.details as vendor_details',
        'vendors.email as vendor_email',
        'vendors.phone as vendor_phone',
        'vendors.reg as vendor_reg',
        'vendors.kra as vendor_kra',
        'vendors.cover as vendor_cover',
        'vendors.logo as vendor_logo',
        'vendors.active as vendor_active',
        'vendors.featured as vendor_featured'
      )
  }

  private applyFilters(query: any, filters: Record<string, any>) {
    for (const [key, value] of Object.entries(filters)) {
      if (key && value) {
        if (key.includes('created_at') || key.includes('updated_at')) {
          query.where(key, '>=', value)
        } else {
          query.where(key, value)
        }
      }
    }
    return query
  }

  private mapStaffData(staff: any): StaffResponse {
    return {
      id: staff.id,
      user_id: staff.user_id,
      online: staff.online,
      identifier: staff.identifier,
      first_name: staff.user_first_name,
      last_name: staff.user_last_name,
      phone: staff.user_phone,
      email: staff.user_email,
      idpass: staff.user_idpass,
      title: staff.user_title,
      gender: staff.user_gender,
      status: staff.user_status,
      branch: {
        id: staff.branch_id,
        name: staff.branch_name,
        email: staff.branch_email,
        phone: staff.branch_phone,
        details: staff.branch_details,
        location: staff.branch_location,
      },
      vendor: {
        id: staff.vendor_id,
        name: staff.vendor_name,
        slug: staff.vendor_slug,
        details: staff.vendor_details,
        email: staff.vendor_email,
        phone: staff.vendor_phone,
        reg: staff.vendor_reg,
        kra: staff.vendor_kra,
        cover: staff.vendor_cover,
        logo: staff.vendor_logo,
        active: staff.vendor_active,
        featured: staff.vendor_featured,
      },
      roles: staff.user_role_name ? [staff.user_role_name] : [],
    }
  }

  private groupStaffByRoles(staffList: StaffResponse[]): StaffResponse[] {
    const usersMap: Record<string, StaffResponse> = {}

    staffList.forEach((staff) => {
      const id = staff.id.toString()
      if (!usersMap[id]) {
        usersMap[id] = { ...staff, roles: staff.roles || [] }
      } else {
        staff.roles.forEach((role) => {
          if (role && !usersMap[id].roles.includes(role)) {
            usersMap[id].roles.push(role)
          }
        })
      }
    })

    return Object.values(usersMap)
  }

  private buildPaginationMeta(total: number, per: number, page: number): PaginationMeta {
    const lastPage = Math.ceil(total / per)
    const firstPage = 1
    const nextPage = page < lastPage ? page + 1 : null
    const previousPage = page > firstPage ? page - 1 : null

    return {
      total,
      perPage: per,
      currentPage: page,
      lastPage,
      firstPage,
      firstPageUrl: `/?page=${firstPage}`,
      lastPageUrl: `/?page=${lastPage}`,
      nextPageUrl: nextPage ? `/?page=${nextPage}` : null,
      previousPageUrl: previousPage ? `/?page=${previousPage}` : null,
    }
  }

  public async index({ request, response }: HttpContextContract) {
    try {
      const { per = 15, page = 1, order = 'staff.created_at', sort = 'desc', ...filters } = request.qs()

      // Build and execute query
      let query = this.buildBaseQuery()
      query = this.applyFilters(query, filters)
      query.orderBy(order, sort)

      const result = await query.paginate(page, per)

      // Process and format data
      const mappedData = result.map(this.mapStaffData)
      const groupedData = this.groupStaffByRoles(mappedData)

      // Use original pagination metadata but add grouped data info
      const originalMeta = result.getMeta()
      const meta = {
        ...originalMeta,
        actualDataCount: groupedData.length,
        originalDataCount: mappedData.length,
        note: 'Data has been grouped by roles, actualDataCount reflects grouped results'
      }

      return response.status(200).json({
        meta,
        data: groupedData,
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Internal Server Error',
        message: error.message,
      })
    }
  }
}
