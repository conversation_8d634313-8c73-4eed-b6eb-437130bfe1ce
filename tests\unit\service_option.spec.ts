import test from 'japa'
import Database from '@ioc:Adonis/Lucid/Database'
import ServiceOption from 'App/Models/ServiceOption'
import ProductServiceOption from 'App/Models/ProductServiceOption'
import Duration from 'App/Models/Duration'
import { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'

test.group('Service Option Model', (group) => {
  group.beforeEach(async () => {
    await Database.beginGlobalTransaction()
  })

  group.afterEach(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('should create a service option', async (assert) => {
    const optionData = {
      vendorId: null, // Global option
      name: 'Premium Equipment',
      type: ServiceOptionType.EQUIPMENT,
      description: 'High-end professional equipment',
      defaultPriceAdjustment: 50,
      durationId: null,
      constraints: {},
      active: true
    }

    const option = await ServiceOption.create(optionData)

    assert.exists(option.id)
    assert.equal(option.name, optionData.name)
    assert.equal(option.type, optionData.type)
    assert.equal(option.defaultPriceAdjustment, optionData.defaultPriceAdjustment)
    assert.isFalse(option.isDurationOption)
    assert.isFalse(option.hasCalendarIntegration)
  })

  test('should create duration option with calendar integration', async (assert) => {
    // Create a duration first
    const duration = await Duration.create(
      Duration.createTemplate('Test Duration', 180, 'long')
    )

    const durationOption = await ServiceOption.create({
      vendorId: null,
      name: 'Extended Service (3 hours)',
      type: ServiceOptionType.DURATION,
      description: '3-hour extended service',
      defaultPriceAdjustment: 75,
      durationId: duration.id,
      constraints: {},
      active: true
    })

    assert.isTrue(durationOption.isDurationOption)
    assert.isTrue(durationOption.hasCalendarIntegration)
    assert.equal(durationOption.durationId, duration.id)

    const calendarMinutes = await durationOption.getCalendarBlockMinutes()
    assert.equal(calendarMinutes, duration.calendarBlockMinutes)
  })

  test('should validate option configuration correctly', async (assert) => {
    // Valid option
    const validOption = await ServiceOption.create({
      vendorId: null,
      name: 'Valid Option',
      type: ServiceOptionType.PERSONNEL,
      description: 'Valid personnel option',
      defaultPriceAdjustment: 25,
      durationId: null,
      constraints: {
        requires: ['option-1'],
        excludes: ['option-2']
      },
      active: true
    })

    const validValidation = await validOption.validateConfiguration()
    assert.isTrue(validValidation.valid)
    assert.equal(validValidation.errors.length, 0)

    // Invalid option (duration option without duration ID)
    const invalidOption = new ServiceOption()
    invalidOption.fill({
      name: 'Invalid Duration Option',
      type: ServiceOptionType.DURATION,
      defaultPriceAdjustment: 0,
      durationId: null, // Invalid for duration option
      constraints: {}
    })

    const invalidValidation = await invalidOption.validateConfiguration()
    assert.isFalse(invalidValidation.valid)
    assert.isAbove(invalidValidation.errors.length, 0)
  })

  test('should check option compatibility correctly', async (assert) => {
    const option1 = await ServiceOption.create({
      vendorId: null,
      name: 'Option 1',
      type: ServiceOptionType.PERSONNEL,
      description: 'Personnel option 1',
      defaultPriceAdjustment: 0,
      constraints: {
        excludes: ['option-2-id']
      },
      active: true
    })

    const option2 = await ServiceOption.create({
      vendorId: null,
      name: 'Option 2',
      type: ServiceOptionType.PERSONNEL,
      description: 'Personnel option 2',
      defaultPriceAdjustment: 25,
      constraints: {},
      active: true
    })

    const option3 = await ServiceOption.create({
      vendorId: null,
      name: 'Add-on Option',
      type: ServiceOptionType.ADD_ON,
      description: 'Additional service',
      defaultPriceAdjustment: 15,
      constraints: {},
      active: true
    })

    // Same type options (except add-ons) can't be combined
    assert.isFalse(option1.canCombineWith(option2))

    // Add-ons can be combined with other types
    assert.isTrue(option1.canCombineWith(option3))
    assert.isTrue(option2.canCombineWith(option3))

    // Test constraint exclusion
    option1.constraints.excludes = [option2.id]
    assert.isFalse(option1.canCombineWith(option2))
  })

  test('should create option templates correctly', async (assert) => {
    const durationTemplate = ServiceOption.createTemplate(
      'Standard Duration',
      ServiceOptionType.DURATION,
      'vendor-123'
    )

    assert.equal(durationTemplate.name, 'Standard Duration')
    assert.equal(durationTemplate.type, ServiceOptionType.DURATION)
    assert.equal(durationTemplate.vendorId, 'vendor-123')
    assert.equal(durationTemplate.defaultPriceAdjustment, 0)
    assert.isTrue(durationTemplate.active)

    const addOnTemplate = ServiceOption.createTemplate(
      'Premium Add-on',
      ServiceOptionType.ADD_ON,
      null,
      { defaultPriceAdjustment: 50 }
    )

    assert.equal(addOnTemplate.defaultPriceAdjustment, 50)
    assert.isNull(addOnTemplate.vendorId)
  })

  test('should get options by type correctly', async (assert) => {
    await ServiceOption.createMany([
      ServiceOption.createTemplate('Duration 1', ServiceOptionType.DURATION, null),
      ServiceOption.createTemplate('Duration 2', ServiceOptionType.DURATION, 'vendor-123'),
      ServiceOption.createTemplate('Personnel 1', ServiceOptionType.PERSONNEL, null)
    ])

    const durationOptions = await ServiceOption.getByType(ServiceOptionType.DURATION)
    assert.equal(durationOptions.length, 2)

    const vendorDurationOptions = await ServiceOption.getByType(ServiceOptionType.DURATION, 'vendor-123')
    assert.equal(vendorDurationOptions.length, 2) // Global + vendor-specific

    const personnelOptions = await ServiceOption.getByType(ServiceOptionType.PERSONNEL)
    assert.equal(personnelOptions.length, 1)
  })

  test('should search options correctly', async (assert) => {
    await ServiceOption.createMany([
      ServiceOption.createTemplate('Premium Equipment', ServiceOptionType.EQUIPMENT, null),
      ServiceOption.createTemplate('Basic Equipment', ServiceOptionType.EQUIPMENT, null),
      ServiceOption.createTemplate('Premium Service', ServiceOptionType.ADD_ON, null)
    ])

    const premiumOptions = await ServiceOption.search('Premium')
    assert.equal(premiumOptions.length, 2)

    const equipmentOptions = await ServiceOption.search('Equipment')
    assert.equal(equipmentOptions.length, 2)
  })
})
