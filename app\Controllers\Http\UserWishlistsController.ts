import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Wishlist from '../../Models/Wishlist'
import { bind } from '@adonisjs/route-model-binding'
import User from 'App/Models/User'

/**
 * @name Wishlist management
 * @version 1.0.0
 * @description Wishlist management for the application
 */
export default class UserWishlistController {
  /**
   * @index
   * @summary List all wishlist
   * @description List all wishlist, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, user: User) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const wishlistQuery = user.related('wishlist').query().filter(filters)

    return await wishlistQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a wishlist
   * @description Create a wishlist with their details (name and details)
   * @requestBody {"name": "", "details": "", "location": {"name": "", "wishlist": "", "regions": {"country": ""}, "coordinates": {"lat": 0, "lng": 0}, "place_id": ""}, "phone": ""}
   * @responseBody 200 - <Wishlist>
   */
  public async store({ request, response }: HttpContextContract) {
    const { userId, productId } = request.all()

    const wishlist = await Wishlist.create({ userId, productId })

    return response.json(wishlist)
  }

  @bind()
  /**
   * @show
   * @summary Show a single wishlist
   * @description Show a wishlist with their details (name and details)
   * @paramPath id required number - Wishlist ID
   * @responseBody 200 - <Wishlist>
   * @response 404 - Wishlist not found
   */
  public async show({ response }: HttpContextContract, wishlist: Wishlist) {
    return response.json(wishlist)
  }

  @bind()
  /**
   * @update
   * @summary Update a wishlist
   * @description Update a wishlist with their details (name and details)
   * @paramPath id required number - Wishlist ID
   * @requestBody <Wishlist>
   * @responseBody 200 - <Wishlist>
   * @response 404 - Wishlist not found
   */
  public async update({ request, response }: HttpContextContract, wishlist: Wishlist) {
    const input = request.all()

    await wishlist.merge(input).save()

    return response.json(wishlist)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a wishlist
   * @responseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, wishlist: Wishlist) {
    return await wishlist.delete()
  }
}
