import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'durations'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('name').notNullable()
      table.text('description').nullable()
      
      // Core duration fields
      table.integer('minutes').notNullable()
      table.integer('buffer_minutes').notNullable().defaultTo(0)
      table.enum('category', ['short', 'medium', 'long', 'full-day']).notNullable().index()
      
      // Scheduling constraints
      table.integer('max_concurrent').notNullable().defaultTo(1)
      table.boolean('allows_back_to_back').notNullable().defaultTo(true)
      table.integer('required_break_after').notNullable().defaultTo(0)
      
      // Scheduling rules (JSON)
      table.json('scheduling_rules').notNullable().defaultTo(JSON.stringify({
        minAdvanceHours: 2,
        maxPerDay: 5,
        timeSlots: [],
        blackoutDays: ['sunday']
      }))
      
      // Branch constraints (JSON)
      table.json('branch_constraints').notNullable().defaultTo(JSON.stringify({
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: []
      }))
      
      table.boolean('active').notNullable().defaultTo(true).index()
      
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
      
      // Indexes for performance
      table.index(['category', 'active'])
      table.index(['minutes', 'active'])
      table.index(['max_concurrent', 'active'])
      table.index(['allows_back_to_back', 'active'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
