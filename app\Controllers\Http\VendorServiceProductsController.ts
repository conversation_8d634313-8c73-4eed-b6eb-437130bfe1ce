import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import Service from 'App/Models/Service'
import Product from 'App/Models/Product'

/**
 * @name Vendor Service Products management
 * @version 1.0.0
 * @description Product management for vendor services
 */
export default class VendorServiceProductsController {
  /**
   * @index
   * @summary List all products for a vendor service
   * @description List all products for a specific vendor and service combination, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery category - Filter by product category ID
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor, service: Service) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', category, ...filters } = request.qs()
    const productQuery = Product.filter(filters)
      .preload('vendor')
      .preload('category')
      .where('vendorId', vendor.id)
      .where('serviceId', service.id)

    if (category) {
      productQuery.where('productCategoryId', category)
    }

    return await productQuery.orderBy(order, sort).paginate(page, per)
  }
} 