import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'products'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('ref').nullable()
      table.string('sku').nullable()
      table.string('name').notNullable()
      table.text('details').nullable()
      table.float('price').nullable()
      table.float('discounted').nullable()
      table.integer('stock').nullable()
      table.boolean('active').defaultTo(1)
      table.boolean('featured').defaultTo(0)
      table.enum('type', ['Physical', 'Digital', 'Service']).defaultTo('Digital')
      table.enum('condition', ['New', 'Used', 'Refurbished']).defaultTo('New')
      table
        .enum('status', ['Draft', 'Pending', 'Published', 'Unpublished', 'Archived'])
        .defaultTo('Published')
      table.enum('availability', ['In Stock', 'Out of Stock', 'Pre Order']).defaultTo('In Stock')
      table.enum('shipping', ['Free', 'Paid', 'Pickup']).defaultTo('Paid')
      table
        .enum('unit', [
          'kg',
          'g',
          'lb',
          'oz',
          'l',
          'ml',
          'pt',
          'gal',
          'unit',
          'pack',
          'box',
          'set',
          'pair',
          'dozen',
          'bundle',
          'roll',
          'bag',
          'case',
          'each',
          'other',
          'mm',
          'cm',
          'm',
          'in',
          'ft',
          'yd',
          'sqm',
          'sqft',
          'sqyd',
          'acre',
          'ha',
          'sqmm',
          'sqcm',
          'pc',
        ])
        .defaultTo('unit')
      table.enum('mode', ['Single', 'Variable']).defaultTo('Single')
      table.enum('payment', ['Free', 'Prepaid', 'Postpaid', 'Concurrent']).defaultTo('Prepaid')
      table.enum('visibility', ['Private', 'Public', 'Restricted']).defaultTo('Public')
      table
        .string('product_category_id')
        .references('id')
        .inTable('product_categories')
        .onDelete('CASCADE')
      table.string('user_id').references('id').inTable('users').onDelete('CASCADE')
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE')
      table.string('branch_id').references('id').inTable('branches').onDelete('CASCADE')
      table.string('service_id').references('id').inTable('services').onDelete('CASCADE').nullable()
      table.json('image').nullable()
      table.json('meta').nullable()
      table.json('extra').nullable()

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('expires_at', { useTz: true })
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
