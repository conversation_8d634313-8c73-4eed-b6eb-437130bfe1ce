// Example: app/Middleware/Acl.ts
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { AuthenticationException } from '@adonisjs/auth/build/standalone'

export default class AclMiddleware {
  public async handle(
    { auth }: HttpContextContract,
    next: () => Promise<void>,
    allowedRoles: string[] // Roles passed from route definition (e.g., ['admin'])
  ) {
    const user = auth.user! // Assumes auth middleware ran first

    // Load roles if not already loaded (optional, depends on usage)
    // await user.load('roles')

    const hasPermission = await user.hasAnyRoles(...allowedRoles)

    if (!hasPermission) {
      throw new AuthenticationException(
        'Unauthorized access', // Message
        'E_UNAUTHORIZED_ACCESS', // Code
        undefined, // Guard name (optional)
        '/login' // Redirect URL (optional)
      )
      // Or return response.forbidden() if redirect isn't desired
    }

    await next()
  }
}