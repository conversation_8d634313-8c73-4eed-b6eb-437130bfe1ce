import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from '../../Models/Order'
import { bind } from '@adonisjs/route-model-binding' // Keep bind as it's used in store/index
import CustomerNewOrder from 'App/Notifications/Customer/CustomerNewOrder'
import User from 'App/Models/User'
import StaffNewOrder from 'App/Notifications/Staff/StaffNewOrder' // Keep StaffNewOrder import
import Lot from 'App/Models/Lot' // Keep Lot import
import Branch from 'App/Models/Branch'
import OrderPricingService from 'App/Services/OrderPricingService'

/**
 * @name Order management
 * @version 1.0.0
 * @description Order management for the application
 */
export default class BranchOrdersController {
  /**
   * @index
   * @summary List all Orders for a specific branch
   * @description List all Orders for a specific branch, paginated. Uses Route Model Binding for the branch.
   * @paramPath branch - The ID of the branch.
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind() // Keep @bind() for the index method as it uses the bound branch
  public async index({ request }: HttpContextContract, branch: Branch) {
    // Keep branch parameter here
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const OrderQuery = Order.filter(filters)
      .where('branchId', branch.id) // Use the ID from the bound branch object
      .preload('customer')
      .preload('lot')
      .preload('items')
      .preload('staff')
      .preload('section')
      .preload('invoices')

    return await OrderQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @ordersbystaffid
   * @summary List all Orders filtered by staffId, status, branchId (via query string)
   * @description List all Orders, paginated, filtered by query string parameters.
   * @path /v1/getorders/bystaffid - Example path (based on swagger)
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery staffId - Staff ID to filter by
   * @paramQuery branchId - Branch ID to filter by
   * @paramQuery status - Status to filter by
   */
  // @bind() // <-- REMOVED @bind decorator for ordersbystaffid
  public async ordersbystaffid(
    { request }: HttpContextContract /* <--- REMOVED , branch: Branch */
  ) {
    // Extract filters, including branchId, from the query string
    const {
      per = 10,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      staffId,
      branchId,
      status,
      ...filters
    } = request.qs()
    const OrderQuery = Order.filter(filters)
      .where('branchId', branchId) // Use branchId from query string
      .where('staffId', staffId) // Use staffId from query string
      .where('status', status) // Use status from query string
      .preload('customer')
      .preload('lot')
      .preload('items', (itemQuery) => {
        itemQuery.preload('product')
        itemQuery.preload('modifiers')
      })
      .preload('staff')
      .preload('section')
      .preload('invoices')

    const orders = await OrderQuery.orderBy(order, sort).paginate(page, per)

    // Handle temp orders (Pending status) - populate items from meta.temp_items
    // Process each order in the paginated result
    for (const order of orders) {
      if (order.status === 'Pending' && order.meta?.temp_items) {
        // This is a temp order - populate items from meta.temp_items
        const tempItems = order.meta.temp_items
        const productIds = Object.keys(tempItems)

        if (productIds.length > 0) {
          // Import Product model dynamically to avoid circular dependency
          const { default: Product } = await import('../../Models/Product')
          const products = await Product.query()
            .whereIn('id', productIds)
            .preload('category')
            .preload('gallery')
            .preload('forms')
            .preload('branch')
            .preload('vendor')
            .exec()

          // Create items array and assign to order
          const items = products.map((product) => {
            const quantity = tempItems[product.id]?.quantity || 0
            const productPrice = product.price || 0
            const totalItemCost = productPrice * quantity

            return {
              id: null, // No order_items record yet
              orderId: order.id,
              productId: product.id,
              quantity: quantity,
              meta: null,
              createdAt: order.createdAt,
              updatedAt: order.updatedAt,
              price: productPrice, // ✅ FIX: Set actual product price
              status: 'pending',
              departmentId: null,
              assignedStaffId: null,
              estimatedPreparationTime: null,
              preparationStartedAt: null,
              preparationCompletedAt: null,
              servedAt: null,
              priorityLevel: 1,
              requiresSpecialAttention: false,
              specialInstructions: null,
              preparationNotes: null,
              statusHistory: null,
              qualityCheckStatus: 'not_required',
              qualityCheckedBy: null,
              customerModifications: null,
              cancellationReason: null,
              actualPreparationTime: null,
              preparationAttempts: 1,
              modifiers: [],
              product: product.serialize(), // ✅ FIX: Use serialize() instead of toJSON()
              actualPreparationTimeMinutes: null,
              isOverdue: false,
              preparationProgress: 0,
              statusDisplayName: 'Pending',
              canBeStarted: true,
              canBeCompleted: false,
              canBeServed: false,
              requiresAttention: false,
              estimatedCompletionTime: null,
              allModifiersCompleted: true,
              totalItemCost: totalItemCost, // ✅ FIX: Calculate actual cost
            }
          })

          // Assign items to the order instance
          order.$setRelated('items', items)
        }
      }
    }

    // Add pricing information using standardized service
    const ordersWithPricing = OrderPricingService.addPricingToPaginatedOrders(orders)

    return ordersWithPricing
  }

  /**
   * @store
   * @summary Create an Order within a specific branch
   * @description Create an Order with details for a branch identified by Route Model Binding.
   * @paramPath branch - The ID of the branch.
   * @requestBody {"staffId": "string", "vendorId": "string", "lotId": "string", "sectionId": "string", "action": "string", "type": "string", "delivery": "string", "status": "string", "meta": {}, "userId": "string", "items": [{"productId": "quantity"}]} - Order details
   * @responseBody 200 - <Order>
   */
  @bind() // Keep @bind for store, assuming route is /branches/{branch}/orders
  public async store({ request, response, auth }: HttpContextContract, branch: Branch) {
    // Keep bound branch parameter
    try {
      const {
        staffId,
        vendorId,
        // branchId, // Removed as 'branch.id' from binding should be used now
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
        userId = null,
        items = [],
      } = request.all()

      // Validate that order has items
      if (!items || items.length === 0) {
        return response.badRequest({
          error: 'Order must contain at least one item',
          details: 'Cannot create an order without items',
        })
      }

      // Create the order using the relationship from the bound 'branch' object
      // branchId is implicitly set here
      const order = await branch.related('orders').create({
        userId: userId ? userId : auth.user?.id,
        staffId,
        vendorId, // Assuming vendorId still comes from payload, verify if needed
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
      })

      // Attach items to the order
      // Use Promise.all for better handling of multiple async operations
      await Promise.all(
        items.map(async (item: Record<string, number>) => {
          const productId = Object.keys(item)[0] // Assuming one product per item object key
          if (productId && typeof item[productId] === 'number') {
            await order.related('items').create({
              productId,
              quantity: item[productId],
            })
          }
        })
      )

      // Reload items to calculate amount correctly, including pivot data
      await order.load('items')

      // --- Calculate Amount ---
      let amount =
        order.items?.reduce((acc, item) => {
          // Get price from the related product
          const price = item.product?.price ?? 0
          const quantity = item.quantity
          return acc + price * quantity
        }, 0) ?? 0 // Default to 0 if reduce results in undefined/null

      // Add charges from meta if they exist
      if (order.meta && order.meta.charges && typeof order.meta.charges === 'object') {
        amount +=
          Object.values(order.meta.charges as Record<string, number>)?.reduce(
            (acc, charge) => acc + (typeof charge === 'number' ? charge : 0), // Ensure charge is a number
            0
          ) ?? 0 // Default to 0 if reduce is null/undefined
      }
      // --- End Amount Calculation ---

      // Create the related invoice
      await order.related('invoices').create({
        amount, // Use the calculated amount
        status: 'Pending',
      })

      // Reload necessary relations for the response and notifications
      await order.load('customer')
      await order.load('vendor')
      await order.load('branch') // Branch should already be loaded via relationship creation, but explicit load is safe
      await order.load('invoices', (iq) => iq.preload('order', (oq) => oq.preload('payments')))
      // Items already loaded above, no need to reload unless modified

      // Notify Customer if userId is provided
      if (userId) {
        const customer = await User.findOrFail(userId)
        await customer.notify(new CustomerNewOrder(order))

        // Sync customer relationship to this specific branch using the bound branch's ID
        // The 'false' argument prevents detaching other existing branch relationships
        await customer.related('branches').sync(
          {
            [branch.id]: {
              // Use ID from the bound branch object
              active: true,
              vendor_id: vendorId, // Verify if this vendorId or branch.vendorId is correct
              branch_id: branch.id, // Use bound branch ID
            },
          },
          false
        )
      }

      // --- Staff Notification Logic ---
      if (lotId) {
        // If lotId is provided, notify staff associated with that specific Lot
        const lot = await Lot.findOrFail(lotId) // Use imported Lot model
        // Find the most recently added staff member to the lot (adjust query if needed)
        const staffMember = await lot.related('staff').query().orderBy('created_at', 'desc').first()
        if (staffMember) {
          await staffMember.notify(new StaffNewOrder(order)) // Use imported StaffNewOrder
        } else {
          // Optional: Log a warning if no staff is found for the specified lot
          console.warn(
            `[BranchOrdersController] No staff found for Lot ID: ${lotId} during order creation.`
          )
        }
      } else {
        // If no lotId, notify all staff members associated with the Branch
        // Query staff related to the *bound* branch object
        const staffMembers = await User.query().whereHas('employers', (q) => {
          q.where('branch_id', branch.id) // Filter by the bound branch's ID
        })

        // Notify all found staff members
        await Promise.all(
          staffMembers.map(async (u) => await u.notify(new StaffNewOrder(order))) // Use imported StaffNewOrder
        )
      }
      // --- End Staff Notification Logic ---

      // Return the created order object with loaded relations
      return response.json(order)
    } catch (error) {
      console.error('Error in BranchOrdersController.store:', error) // Log the specific error
      // Return a user-friendly error response
      return response.badRequest({
        message: 'Failed to create the order. Please check the provided data.',
        error: error.message,
      })
    }
  }
}
