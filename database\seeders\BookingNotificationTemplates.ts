import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Database from '@ioc:Adonis/Lucid/Database'

export default class BookingNotificationTemplatesSeeder extends BaseSeeder {
  public async run() {
    const templates = [
      // Booking Created Templates
      {
        type: 'booking',
        name: 'booking_created',
        subject: 'Booking Request Received - {{confirmation_code}}',
        body: 'Hello {{customer_name}}, your booking request for {{service_name}} on {{appointment_date}} at {{appointment_time}} has been received. Confirmation code: {{confirmation_code}}. You will be notified once confirmed.',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_date', 'appointment_time', 
          'confirmation_code', 'vendor_name', 'branch_name', 'total_price', 'duration_minutes'
        ]),
        channels: JSON.stringify(['email', 'sms', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent when a new booking request is created',
          priority: 'high',
          immediate: true
        })
      },

      // Booking Confirmed Templates
      {
        type: 'booking',
        name: 'booking_confirmed',
        subject: 'Booking Confirmed - {{confirmation_code}}',
        body: 'Great news {{customer_name}}! Your booking for {{service_name}} on {{appointment_date}} at {{appointment_time}} has been confirmed. Confirmation code: {{confirmation_code}}. We look forward to serving you!',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_date', 'appointment_time', 
          'confirmation_code', 'vendor_name', 'branch_name', 'total_price', 'duration_minutes',
          'branch_location', 'selected_options'
        ]),
        channels: JSON.stringify(['email', 'sms', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent when a booking is confirmed by the vendor',
          priority: 'high',
          immediate: true
        })
      },

      // 24-Hour Reminder Templates
      {
        type: 'booking',
        name: 'booking_reminder_24h',
        subject: 'Appointment Reminder - Tomorrow at {{appointment_time}}',
        body: 'Hi {{customer_name}}, this is a friendly reminder that you have an appointment for {{service_name}} tomorrow ({{appointment_date}}) at {{appointment_time}}. Confirmation: {{confirmation_code}}. Please arrive 10 minutes early.',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_date', 'appointment_time', 
          'confirmation_code', 'vendor_name', 'branch_name', 'duration_minutes',
          'branch_location', 'preparation_notes'
        ]),
        channels: JSON.stringify(['email', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: '24-hour appointment reminder',
          priority: 'medium',
          scheduled: true,
          schedule_offset_hours: -24
        })
      },

      // 2-Hour Reminder Templates
      {
        type: 'booking',
        name: 'booking_reminder_2h',
        subject: 'Appointment Reminder - In 2 Hours',
        body: 'Hi {{customer_name}}, your appointment for {{service_name}} is in 2 hours at {{appointment_time}}. Confirmation: {{confirmation_code}}. Please ensure you have the confirmation code ready.',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_time', 
          'confirmation_code', 'branch_name', 'branch_location'
        ]),
        channels: JSON.stringify(['sms', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: '2-hour appointment reminder',
          priority: 'high',
          scheduled: true,
          schedule_offset_hours: -2
        })
      },

      // 30-Minute Reminder Templates
      {
        type: 'booking',
        name: 'booking_reminder_30min',
        subject: 'URGENT: Appointment in 30 Minutes',
        body: 'URGENT: Hi {{customer_name}}, your appointment for {{service_name}} is in 30 minutes at {{appointment_time}}. Confirmation: {{confirmation_code}}. Please head to {{branch_name}} now.',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_time', 
          'confirmation_code', 'branch_name', 'branch_location'
        ]),
        channels: JSON.stringify(['sms', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: '30-minute urgent appointment reminder',
          priority: 'critical',
          scheduled: true,
          schedule_offset_minutes: -30
        })
      },

      // Booking Cancelled Templates
      {
        type: 'booking',
        name: 'booking_cancelled',
        subject: 'Booking Cancellation Confirmed - {{confirmation_code}}',
        body: 'Hello {{customer_name}}, your booking for {{service_name}} on {{appointment_date}} at {{appointment_time}} has been cancelled. Confirmation: {{confirmation_code}}. {{cancellation_reason}}',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_date', 'appointment_time', 
          'confirmation_code', 'cancellation_reason', 'cancelled_by', 'refund_amount',
          'refund_processing_time'
        ]),
        channels: JSON.stringify(['email', 'sms', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent when a booking is cancelled',
          priority: 'high',
          immediate: true
        })
      },

      // Booking Modified Templates
      {
        type: 'booking',
        name: 'booking_modified',
        subject: 'Booking Updated - {{confirmation_code}}',
        body: 'Hello {{customer_name}}, your booking for {{service_name}} has been updated. New appointment time: {{appointment_date}} at {{appointment_time}}. Confirmation: {{confirmation_code}}.',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_date', 'appointment_time', 
          'confirmation_code', 'previous_appointment_date', 'previous_appointment_time',
          'modification_reason', 'total_price'
        ]),
        channels: JSON.stringify(['email', 'sms', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent when booking details are modified',
          priority: 'high',
          immediate: true
        })
      },

      // Booking Completed Templates
      {
        type: 'booking',
        name: 'booking_completed',
        subject: 'Service Completed - Thank You!',
        body: 'Thank you {{customer_name}}! Your {{service_name}} service has been completed. We hope you enjoyed your experience. Please consider leaving a review.',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'completion_time', 
          'vendor_name', 'staff_names', 'review_url', 'rebooking_url'
        ]),
        channels: JSON.stringify(['email', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent when service is marked as completed',
          priority: 'low',
          immediate: true
        })
      },

      // No-Show Templates
      {
        type: 'booking',
        name: 'booking_no_show',
        subject: 'Missed Appointment - {{confirmation_code}}',
        body: 'Hello {{customer_name}}, we noticed you missed your appointment for {{service_name}} today at {{appointment_time}}. Confirmation: {{confirmation_code}}. Please contact us to reschedule.',
        variables: JSON.stringify([
          'customer_name', 'service_name', 'appointment_time', 
          'confirmation_code', 'vendor_name', 'rebooking_url', 'contact_phone'
        ]),
        channels: JSON.stringify(['email', 'sms', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent when customer is marked as no-show',
          priority: 'medium',
          immediate: true
        })
      },

      // Vendor New Booking Alert Templates
      {
        type: 'booking',
        name: 'vendor_new_booking',
        subject: 'New Booking Request - {{service_name}}',
        body: 'New booking request from {{customer_name}} for {{service_name}} on {{appointment_date}} at {{appointment_time}}. Total: ${{total_price}}. Confirmation: {{confirmation_code}}. Please review and confirm.',
        variables: JSON.stringify([
          'customer_name', 'customer_phone', 'customer_email', 'service_name', 
          'appointment_date', 'appointment_time', 'confirmation_code', 'total_price',
          'duration_minutes', 'selected_options', 'customer_notes'
        ]),
        channels: JSON.stringify(['email', 'sms', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent to vendor when new booking request is received',
          priority: 'high',
          immediate: true,
          target_roles: ['vendor', 'manager', 'staff']
        })
      },

      // Vendor Booking Confirmed Alert Templates
      {
        type: 'booking',
        name: 'vendor_booking_confirmed',
        subject: 'Booking Confirmed - {{service_name}}',
        body: 'Booking confirmed for {{customer_name}} - {{service_name}} on {{appointment_date}} at {{appointment_time}}. Confirmation: {{confirmation_code}}. Added to your schedule.',
        variables: JSON.stringify([
          'customer_name', 'customer_phone', 'service_name', 
          'appointment_date', 'appointment_time', 'confirmation_code',
          'staff_assignments', 'equipment_reservations'
        ]),
        channels: JSON.stringify(['email', 'push', 'database']),
        is_active: true,
        meta: JSON.stringify({
          description: 'Sent to vendor when booking is confirmed',
          priority: 'medium',
          immediate: true,
          target_roles: ['vendor', 'manager', 'staff']
        })
      }
    ]

    // Insert templates, handling duplicates
    for (const template of templates) {
      await Database.table('notification_templates')
        .insert(template)
        .onConflict(['type', 'name'])
        .merge([
          'subject',
          'body', 
          'variables',
          'channels',
          'is_active',
          'meta',
          'updated_at'
        ])
    }

    console.log('✅ Booking notification templates seeded successfully')
  }
}
