import Route from '@ioc:Adonis/Core/Route'

/**
 * WebSocket management and monitoring routes
 */
Route.group(() => {
  // WebSocket server status and health
  Route.get('/status', 'WebSocketController.status')
  Route.get('/health', 'WebSocketController.health')

  // Admin-only testing and management routes
  Route.group(() => {
    Route.post('/broadcast/test', 'WebSocketController.broadcastTest')
    Route.post('/send/test', 'WebSocketController.sendTestToUser')
    Route.post('/trigger/order-status', 'WebSocketController.triggerOrderStatusBroadcast')
    Route.post('/trigger/item-status', 'WebSocketController.triggerItemStatusBroadcast')
    Route.post('/trigger/overdue-alert', 'WebSocketController.triggerOverdueAlert')
  }).middleware(['auth', 'websocket_auth'])

}).prefix('/api/v1/websocket')

/**
 * WebSocket subscription management routes
 */
Route.group(() => {
  // Subscription management
  Route.post('/subscribe', 'WebSocketSubscriptionController.subscribe')
  Route.post('/unsubscribe', 'WebSocketSubscriptionController.unsubscribe')
  Route.get('/subscriptions', 'WebSocketSubscriptionController.getUserSubscriptions')
  Route.post('/validate', 'WebSocketSubscriptionController.validateSubscription')

  // Admin-only subscription management
  Route.group(() => {
    Route.get('/channel/subscriptions', 'WebSocketSubscriptionController.getChannelSubscriptions')
    Route.get('/statistics', 'WebSocketSubscriptionController.getStatistics')
    Route.post('/cleanup', 'WebSocketSubscriptionController.cleanupStaleSubscriptions')
  }).middleware(['auth', 'websocket_auth'])

}).prefix('/api/v1/websocket/subscriptions').middleware(['auth'])

/**
 * Real-time notification management routes
 */
Route.group(() => {
  // User notification management
  Route.get('/notifications', 'NotificationController.getUserNotifications')
  Route.post('/notifications/:id/acknowledge', 'NotificationController.acknowledgeNotification')

  // Admin/Manager notification management
  Route.group(() => {
    Route.post('/notifications/send', 'NotificationController.sendCustomNotification')
    Route.get('/notifications/statistics', 'NotificationController.getStatistics')
    Route.post('/notifications/test', 'NotificationController.testNotification')
    Route.get('/notifications/templates', 'NotificationController.getTemplates')
  }).middleware(['auth', 'websocket_auth'])

}).prefix('/api/v1').middleware(['auth'])

/**
 * Order completion management routes with WebSocket integration
 */
Route.group(() => {
  // Order completion endpoints
  Route.get('/orders/:id/completion/check', 'OrderCompletionController.checkCompletion')
  Route.post('/orders/completion/bulk-check', 'OrderCompletionController.bulkCheck')
  Route.get('/orders/completion/ready', 'OrderCompletionController.getReadyOrders')
  Route.post('/orders/completion/scheduled-check', 'OrderCompletionController.scheduledCheck')
  Route.get('/orders/completion/statistics', 'OrderCompletionController.getStatistics')
  Route.post('/orders/:id/completion/force', 'OrderCompletionController.forceCompletion')

}).prefix('/api/v1/fulfillment').middleware(['auth'])

/**
 * Enhanced order item status routes with WebSocket integration
 */
Route.group(() => {
  // Order completion status endpoints
  Route.get('/orders/:order_id/completion/status', 'OrderItemStatusController.checkOrderCompletionStatus')
  Route.post('/orders/completion/bulk-check', 'OrderItemStatusController.bulkCheckOrderCompletion')

}).prefix('/api/v1/order-items').middleware(['auth'])
