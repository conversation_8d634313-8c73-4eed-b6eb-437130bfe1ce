import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import VendorCategory from '../VendorCategory'

export default class VendorCategoryFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof VendorCategory, VendorCategory>

  public s(name: string) {
    this.$query.where((builder) => {
      builder.whereILike('name', `%${name}%`)
    })
  }

  public type(type: string) {
    this.$query.where((builder) => {
      builder.where('vendor_type_id', 'LIKE', `%${type}%`)
    })
  }

  public with(relations: string) {
    relations.split(',').map((relation: 'vendors') => {
      this.$query.preload(relation)
    })
  }

  public withTrashed() {
    this.$query.withTrashed()
  }

  public onlyTrashed() {
    this.$query.onlyTrashed()
  }
}
