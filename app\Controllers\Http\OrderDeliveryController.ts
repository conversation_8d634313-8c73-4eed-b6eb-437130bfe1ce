import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from 'App/Models/Order'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import { bind } from '@adonisjs/route-model-binding'

import Database from '@ioc:Adonis/Lucid/Database'
import { DateTime } from 'luxon'

export default class OrderDeliveryController {
  /**
   * Assign a delivery vendor to an order
   */
  @bind()
  public async assignVendor({ request, response }: HttpContextContract, order: Order) {
    const deliverySchema = schema.create({
      deliveryVendorId: schema.string({}, [rules.exists({ table: 'vendors', column: 'id' })]),
      deliveryBranchId: schema.string.optional({}, [rules.exists({ table: 'branches', column: 'id' })]),
      estimatedDeliveryTime: schema.number.optional(),
      deliveryFee: schema.number.optional(),
      deliveryNotes: schema.object().anyMembers(),
    })

    const payload = await request.validate({ schema: deliverySchema })

    // Start a transaction
    const trx = await Database.transaction()

    try {
      // Update order with delivery details
      order.useTransaction(trx)
      await order.merge({
        deliveryVendorId: payload.deliveryVendorId,
        deliveryBranchId: payload.deliveryBranchId,
        deliveryStatus: 'assigned' as const,
        estimatedDeliveryTime: payload.estimatedDeliveryTime,
        deliveryFee: payload.deliveryFee,
        deliveryNotes: payload.deliveryNotes,
      }).save()

      // Update order status if needed
      if (order.status === 'Ready') {
        await order.merge({ status: 'Delivering' }).save()
      }

      await trx.commit()

      // Load relationships
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')

      return response.ok(order)
    } catch (error) {
      await trx.rollback()
      return response.badRequest({ message: 'Failed to assign delivery vendor', error })
    }
  }

  /**
   * Update delivery status
   */
  @bind()
  public async updateStatus({ request, response }: HttpContextContract, order: Order) {
    const statusSchema = schema.create({
      status: schema.enum(['picked_up', 'in_transit', 'delivered', 'failed', 'cancelled']),
      trackingData: schema.object().anyMembers(),
      notes: schema.string.optional(),
    })

    const payload = await request.validate({ schema: statusSchema })

    // Start a transaction
    const trx = await Database.transaction()

    try {
      order.useTransaction(trx)

      // Update delivery status
      await order.merge({
        deliveryStatus: payload.status as 'picked_up' | 'in_transit' | 'delivered' | 'failed' | 'cancelled',
        trackingData: payload.trackingData,
        deliveryNotes: { ...order.deliveryNotes, statusUpdate: payload.notes },
      }).save()

      // Update timestamps based on status
      switch (payload.status) {
        case 'picked_up':
          await order.merge({ pickupTime: DateTime.now() }).save()
          break
        case 'delivered':
          await order.merge({ 
            actualDeliveryTime: DateTime.now(),
            status: 'Delivered'
          }).save()
          break
        case 'failed':
        case 'cancelled':
          await order.merge({ status: 'Cancelled' }).save()
          break
      }

      await trx.commit()

      // Load relationships
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')

      return response.ok(order)
    } catch (error) {
      await trx.rollback()
      return response.badRequest({ message: 'Failed to update delivery status', error })
    }
  }

  /**
   * Update tracking information
   */
  @bind()
  public async updateTracking({ request, response }: HttpContextContract, order: Order) {
    const trackingSchema = schema.create({
      trackingData: schema.object().anyMembers(),
      location: schema.object().anyMembers(),
      estimatedDeliveryTime: schema.number.optional(),
    })

    const payload = await request.validate({ schema: trackingSchema })

    try {
      await order.merge({
        trackingData: payload.trackingData,
        estimatedDeliveryTime: payload.estimatedDeliveryTime,
      }).save()

      // Load relationships
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')

      return response.ok(order)
    } catch (error) {
      return response.badRequest({ message: 'Failed to update tracking information', error })
    }
  }

  /**
   * Record delivery signature
   */
  @bind()
  public async recordSignature({ request, response }: HttpContextContract, order: Order) {
    const signatureSchema = schema.create({
      signatureImage: schema.string(),
      notes: schema.string.optional(),
    })

    const payload = await request.validate({ schema: signatureSchema })

    try {
      await order.merge({
        signatureImage: payload.signatureImage,
        deliveryNotes: { ...order.deliveryNotes, signatureNotes: payload.notes },
      }).save()

      return response.ok(order)
    } catch (error) {
      return response.badRequest({ message: 'Failed to record signature', error })
    }
  }

  /**
   * Get delivery tracking information
   */
  @bind()
  public async getTracking({ response }: HttpContextContract, order: Order) {
    try {
      await order.load('deliveryVendor')
      await order.load('deliveryBranch')

      return response.ok({
        orderId: order.id,
        trackingCode: order.trackingCode,
        status: order.deliveryStatus,
        trackingData: order.trackingData,
        estimatedDeliveryTime: order.estimatedDeliveryTime,
        actualDeliveryTime: order.actualDeliveryTime,
        pickupTime: order.pickupTime,
        deliveryVendor: order.deliveryVendor,
        deliveryBranch: order.deliveryBranch,
      })
    } catch (error) {
      return response.badRequest({ message: 'Failed to get tracking information', error })
    }
  }
} 