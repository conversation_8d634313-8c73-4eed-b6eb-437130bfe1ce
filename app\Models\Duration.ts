import { DateTime } from 'luxon'
import { BaseModel, column, beforeCreate, computed } from '@ioc:Adonis/Lucid/Orm'
import { ulid } from 'ulidx'

export default class Duration extends BaseModel {
  public static selfAssignPrimaryKey = true

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public description: string | null

  @column()
  public minutes: number

  @column()
  public bufferMinutes: number

  @column()
  public category: 'short' | 'medium' | 'long' | 'full-day'

  @column()
  public maxConcurrent: number

  @column()
  public allowsBackToBack: boolean

  @column()
  public requiredBreakAfter: number

  @column()
  public schedulingRules: {
    minAdvanceHours: number
    maxPerDay: number
    timeSlots: string[]
    blackoutDays: string[]
  }

  @column()
  public branchConstraints: {
    respectBranchHours: boolean
    staffRequired: number
    equipmentRequired: string[]
  }

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateId(duration: Duration) {
    duration.id = ulid().toLowerCase()
  }

  @computed()
  public get calendarBlockMinutes(): number {
    return this.minutes + this.bufferMinutes
  }

  @computed()
  public get totalHours(): number {
    return Math.round((this.calendarBlockMinutes / 60) * 100) / 100
  }

  @computed()
  public get isShortDuration(): boolean {
    return this.category === 'short'
  }

  @computed()
  public get isMediumDuration(): boolean {
    return this.category === 'medium'
  }

  @computed()
  public get isLongDuration(): boolean {
    return this.category === 'long'
  }

  @computed()
  public get isFullDay(): boolean {
    return this.category === 'full-day'
  }

  /**
   * Check if this duration can be scheduled back-to-back with another service
   */
  public canScheduleBackToBack(): boolean {
    return this.allowsBackToBack
  }

  /**
   * Get the minimum break time required after this duration
   */
  public getRequiredBreakMinutes(): number {
    return this.requiredBreakAfter
  }

  /**
   * Check if this duration requires advance booking
   */
  public requiresAdvanceBooking(hoursInAdvance: number): boolean {
    return hoursInAdvance >= this.schedulingRules.minAdvanceHours
  }

  /**
   * Check if this duration can be scheduled on a specific day
   */
  public canScheduleOnDay(dayOfWeek: string): boolean {
    return !this.schedulingRules.blackoutDays.includes(dayOfWeek.toLowerCase())
  }

  /**
   * Check if this duration can be scheduled in a specific time slot
   */
  public canScheduleInTimeSlot(timeSlot: string): boolean {
    if (this.schedulingRules.timeSlots.length === 0) {
      return true // No restrictions
    }
    return this.schedulingRules.timeSlots.includes(timeSlot.toLowerCase())
  }

  /**
   * Get the maximum number of this duration that can be scheduled per day
   */
  public getMaxPerDay(): number {
    return this.schedulingRules.maxPerDay
  }

  /**
   * Check if branch hours should be respected for this duration
   */
  public shouldRespectBranchHours(): boolean {
    return this.branchConstraints.respectBranchHours
  }

  /**
   * Get the minimum staff required for this duration
   */
  public getRequiredStaff(): number {
    return this.branchConstraints.staffRequired
  }

  /**
   * Get the equipment required for this duration
   */
  public getRequiredEquipment(): string[] {
    return this.branchConstraints.equipmentRequired
  }

  /**
   * Validate if the duration configuration is valid
   */
  public validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (this.minutes <= 0) {
      errors.push('Duration minutes must be greater than 0')
    }

    if (this.bufferMinutes < 0) {
      errors.push('Buffer minutes cannot be negative')
    }

    if (this.maxConcurrent <= 0) {
      errors.push('Max concurrent must be greater than 0')
    }

    if (this.requiredBreakAfter < 0) {
      errors.push('Required break after cannot be negative')
    }

    if (this.schedulingRules.minAdvanceHours < 0) {
      errors.push('Minimum advance hours cannot be negative')
    }

    if (this.schedulingRules.maxPerDay <= 0) {
      errors.push('Max per day must be greater than 0')
    }

    if (this.branchConstraints.staffRequired < 0) {
      errors.push('Required staff cannot be negative')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Create a duration template for common service durations
   */
  public static createTemplate(
    name: string,
    minutes: number,
    category: 'short' | 'medium' | 'long' | 'full-day',
    options: Partial<Duration> = {}
  ): Partial<Duration> {
    const defaultBufferMinutes = {
      'short': 15,
      'medium': 30,
      'long': 60,
      'full-day': 90
    }

    const defaultMaxConcurrent = {
      'short': 8,
      'medium': 5,
      'long': 3,
      'full-day': 1
    }

    return {
      name,
      minutes,
      category,
      bufferMinutes: options.bufferMinutes ?? defaultBufferMinutes[category],
      maxConcurrent: options.maxConcurrent ?? defaultMaxConcurrent[category],
      allowsBackToBack: options.allowsBackToBack ?? (category === 'short' || category === 'medium'),
      requiredBreakAfter: options.requiredBreakAfter ?? (category === 'long' || category === 'full-day' ? 60 : 0),
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: defaultMaxConcurrent[category],
        timeSlots: [],
        blackoutDays: ['sunday'],
        ...options.schedulingRules
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: [],
        ...options.branchConstraints
      },
      active: true,
      ...options
    }
  }
}
