import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import MessageAction from 'App/Models/MessageAction'
import Message from 'App/Models/Message'
import CreateMessageActionValidator from 'App/Validators/CreateMessageActionValidator'
import UpdateMessageActionValidator from 'App/Validators/UpdateMessageActionValidator'

/**
 * @swagger
 * components:
 *   schemas:
 *     MessageAction:
 *       type: object
 *       properties:
 *         id:
 *           type: number
 *         message_id:
 *           type: number
 *         type_id:
 *           type: number
 *         config:
 *           type: object
 *         status:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */
export default class MessageActionsController {
  /**
   * @swagger
   * /api/v1/messages/{id}/actions:
   *   get:
   *     tags:
   *       - Message Actions
   *     summary: Get all actions for a message
   *     description: Returns a list of all actions associated with a message
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       200:
   *         description: List of message actions
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 actions:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/MessageAction'
   *       404:
   *         description: Message not found
   */
  @bind()
  public async index({ response }: HttpContextContract, message: Message) {
    const actions = await message.related('actions').query()
    return response.ok({ actions })
  }

  /**
   * @swagger
   * /api/v1/messages/{id}/actions:
   *   post:
   *     tags:
   *       - Message Actions
   *     summary: Create a new action for a message
   *     description: Creates a new action associated with a message
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - type_id
   *               - config
   *             properties:
   *               type_id:
   *                 type: number
   *               config:
   *                 type: object
   *               status:
   *                 type: string
   *                 enum: [pending, active, completed, cancelled]
   *     responses:
   *       201:
   *         description: Action created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 action:
   *                   $ref: '#/components/schemas/MessageAction'
   *       400:
   *         description: Invalid input
   *       404:
   *         description: Message not found
   */
  @bind()
  public async store({ request, response }: HttpContextContract, message: Message) {
    const data = await request.validate(CreateMessageActionValidator)
    const action = await message.related('actions').create(data)
    return response.created({ action })
  }

  /**
   * @swagger
   * /api/v1/messages/{id}/actions/{actionId}:
   *   get:
   *     tags:
   *       - Message Actions
   *     summary: Get a specific action
   *     description: Returns details of a specific action
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: actionId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       200:
   *         description: Action details
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 action:
   *                   $ref: '#/components/schemas/MessageAction'
   *       404:
   *         description: Message or action not found
   */
  @bind()
  public async show({ response }: HttpContextContract, _message: Message, action: MessageAction) {
    return response.ok({ action })
  }

  /**
   * @swagger
   * /api/v1/messages/{id}/actions/{actionId}:
   *   put:
   *     tags:
   *       - Message Actions
   *     summary: Update an action
   *     description: Updates an existing action
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: actionId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               type_id:
   *                 type: number
   *               config:
   *                 type: object
   *               status:
   *                 type: string
   *                 enum: [pending, active, completed, cancelled]
   *     responses:
   *       200:
   *         description: Action updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 action:
   *                   $ref: '#/components/schemas/MessageAction'
   *       400:
   *         description: Invalid input
   *       404:
   *         description: Message or action not found
   */
  @bind()
  public async update({ request, response }: HttpContextContract, _message: Message, action: MessageAction) {
    const data = await request.validate(UpdateMessageActionValidator)
    await action.merge(data).save()
    return response.ok({ action })
  }

  /**
   * @swagger
   * /api/v1/messages/{id}/actions/{actionId}:
   *   delete:
   *     tags:
   *       - Message Actions
   *     summary: Delete an action
   *     description: Deletes an existing action
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *       - name: actionId
   *         in: path
   *         required: true
   *         schema:
   *           type: number
   *     responses:
   *       204:
   *         description: Action deleted successfully
   *       404:
   *         description: Message or action not found
   */
  @bind()
  public async destroy({ response }: HttpContextContract, _message: Message, action: MessageAction) {
    await action.delete()
    return response.noContent()
  }
} 