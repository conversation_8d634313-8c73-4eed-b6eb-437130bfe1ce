# Temp Orders Testing Guide

## Overview
This guide provides comprehensive testing procedures for the unified temp orders system, including API endpoint testing, response format verification, and backward compatibility validation.

## Test Environment Setup

### Prerequisites
1. **Server Running**: Ensure AdonisJS server is running
   ```bash
   node ace serve --watch
   ```

2. **Test Credentials**: Use the following test credentials
   - Email: `<EMAIL>`
   - Password: `54722332233`
   - Phone: `************`

3. **Test Data**: Ensure test products and vendors exist in database

### Running Tests

#### Automated Testing
```bash
# Make script executable
chmod +x tests/temp-orders-api-tests.sh

# Run comprehensive test suite
./tests/temp-orders-api-tests.sh
```

#### Manual Testing
Use the curl commands provided in each test section below.

## Test Scenarios

### 1. Authentication Test
**Purpose**: Verify authentication system works for API access

```bash
curl -X POST "http://localhost:3080/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "54722332233"
  }'
```

**Expected Response**:
```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>"
  }
}
```

### 2. Create Temp Order Test
**Purpose**: Verify temp order creation with unified system

```bash
curl -X POST "http://localhost:3080/v1/temp-orders" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "vendorId": "01j5r8zqhm5kx8qp6j9y8x7w5v",
    "branchId": "01j5r8zqhm5kx8qp6j9y8x7w5w",
    "sectionId": "01j5r8zqhm5kx8qp6j9y8x7w5x",
    "action": "Purchase",
    "type": "Instant",
    "delivery": "Dinein",
    "items": {
      "product_1": 2,
      "product_2": 1
    },
    "meta": {
      "charges": {
        "service": 50,
        "tax": 25
      },
      "customerName": "Test Customer",
      "customerPhone": "+************"
    }
  }'
```

**Expected Response**:
```json
{
  "id": "temp_order_id",
  "vendorId": "01j5r8zqhm5kx8qp6j9y8x7w5v",
  "branchId": "01j5r8zqhm5kx8qp6j9y8x7w5w",
  "status": "Pending",
  "total": 175,
  "items": [
    {
      "id": "product_1",
      "name": "Product 1",
      "price": 50,
      "quantity": 2
    },
    {
      "id": "product_2", 
      "name": "Product 2",
      "price": 25,
      "quantity": 1
    }
  ],
  "meta": {
    "charges": {
      "service": 50,
      "tax": 25
    },
    "temp_items": {
      "product_1": {"quantity": 2},
      "product_2": {"quantity": 1}
    }
  },
  "customer": {...},
  "vendor": {...},
  "branch": {...}
}
```

### 3. List Temp Orders Test
**Purpose**: Verify temp order listing with pagination

```bash
curl -X GET "http://localhost:3080/v1/temp-orders?per=10&page=1" \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response**:
```json
{
  "meta": {
    "total": 5,
    "per_page": 10,
    "current_page": 1,
    "last_page": 1
  },
  "data": [
    {
      "id": "temp_order_1",
      "status": "Pending",
      "total": 175,
      "items": [...],
      "customer": {...}
    }
  ]
}
```

### 4. Get Single Temp Order Test
**Purpose**: Verify individual temp order retrieval

```bash
curl -X GET "http://localhost:3080/v1/temp-orders/{temp_order_id}" \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response**: Same format as create response with all relationships loaded.

### 5. Update Temp Order Test
**Purpose**: Verify temp order modification

```bash
curl -X PUT "http://localhost:3080/v1/temp-orders/{temp_order_id}" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "items": {
      "product_1": 3,
      "product_2": 2
    },
    "meta": {
      "charges": {
        "service": 60,
        "tax": 30
      },
      "notes": "Updated order"
    }
  }'
```

**Expected Response**: Updated temp order with new items and meta data.

### 6. Place Order Test
**Purpose**: Verify temp order to placed order conversion

```bash
curl -X POST "http://localhost:3080/v1/temp-orders/{temp_order_id}/place-order" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "ref": "payment_ref_123"
  }'
```

**Expected Response**:
```json
{
  "id": "same_order_id",
  "status": "Placed",
  "orderNumber": "ORD-1234567890-123",
  "ref": "payment_ref_123",
  "items": [
    {
      "id": "order_item_1",
      "productId": "product_1",
      "quantity": 2,
      "product": {...}
    }
  ],
  "invoices": [
    {
      "id": "invoice_1",
      "amount": 175,
      "status": "Pending"
    }
  ],
  "meta": {
    "charges": {...}
    // Note: temp_items should be removed
  }
}
```

### 7. Delete Temp Order Test
**Purpose**: Verify temp order deletion

```bash
curl -X DELETE "http://localhost:3080/v1/temp-orders/{temp_order_id}" \
  -H "Authorization: Bearer $TOKEN"
```

**Expected Response**: HTTP 204 No Content

## Error Handling Tests

### 1. Unauthorized Access
```bash
curl -X GET "http://localhost:3080/v1/temp-orders"
# Expected: 401 Unauthorized
```

### 2. Invalid Data
```bash
curl -X POST "http://localhost:3080/v1/temp-orders" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"vendorId": "", "items": {}}'
# Expected: 400 Bad Request with validation errors
```

### 3. Non-existent Resource
```bash
curl -X GET "http://localhost:3080/v1/temp-orders/invalid-id" \
  -H "Authorization: Bearer $TOKEN"
# Expected: 404 Not Found
```

### 4. Permission Denied
```bash
# Try to place order with different staff token
curl -X POST "http://localhost:3080/v1/temp-orders/{temp_order_id}/place-order" \
  -H "Authorization: Bearer $WRONG_STAFF_TOKEN"
# Expected: 403 Forbidden
```

## Backward Compatibility Verification

### Response Format Validation
Verify that all responses match the original temp order format:

1. **Field Mapping**: All original fields present
2. **Data Types**: Consistent data types (strings, numbers, objects)
3. **Relationships**: Customer, vendor, branch, section objects included
4. **Items Structure**: Items array with embedded quantity
5. **Total Calculation**: Correct total from items + charges

### API Behavior Validation
1. **Endpoint URLs**: Same URLs as original system
2. **HTTP Methods**: Same methods (GET, POST, PUT, DELETE)
3. **Request Formats**: Same request body structures
4. **Status Codes**: Same HTTP status codes for success/error cases
5. **Pagination**: Same pagination structure for list endpoints

## Performance Testing

### Load Testing
```bash
# Test concurrent temp order creation
for i in {1..10}; do
  curl -X POST "http://localhost:3080/v1/temp-orders" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{...}' &
done
wait
```

### Database Impact
Monitor database performance during testing:
- Query execution times
- Connection pool usage
- Memory consumption
- Index utilization

## Integration Testing

### End-to-End Workflow
1. **Create** temp order
2. **List** temp orders (verify it appears)
3. **Get** specific temp order (verify data)
4. **Update** temp order (verify changes)
5. **Place** order (verify conversion)
6. **Verify** placed order in orders table
7. **Check** invoice creation
8. **Validate** order items creation

### Cross-System Integration
1. **Queue System**: Verify notifications work
2. **Payment System**: Test with payment references
3. **Inventory System**: Check product availability
4. **Staff System**: Verify permission controls

## Test Data Validation

### Database State Verification
```sql
-- Check temp orders are in orders table with Pending status
SELECT COUNT(*) FROM orders WHERE status = 'Pending';

-- Verify temp_items structure in meta
SELECT id, meta->'temp_items' FROM orders WHERE status = 'Pending' LIMIT 5;

-- Check placed orders have order_items
SELECT o.id, COUNT(oi.id) as item_count 
FROM orders o 
LEFT JOIN order_items oi ON o.id = oi.order_id 
WHERE o.status = 'Placed' 
GROUP BY o.id;

-- Verify invoices are created for placed orders
SELECT o.id, COUNT(i.id) as invoice_count 
FROM orders o 
LEFT JOIN invoices i ON o.id = i.order_id 
WHERE o.status = 'Placed' 
GROUP BY o.id;
```

## Troubleshooting

### Common Issues
1. **Authentication Failures**: Check token validity and user permissions
2. **404 Errors**: Verify order IDs and database state
3. **Validation Errors**: Check required fields and data formats
4. **Permission Errors**: Verify staff roles and branch assignments

### Debug Commands
```bash
# Check server logs
tail -f logs/app.log

# Verify database connections
node ace db:seed --files=TestDataSeeder

# Check route registration
node ace list:routes | grep temp-orders
```

## Success Criteria

### Functional Requirements
- ✅ All CRUD operations work correctly
- ✅ Order placement converts temp orders properly
- ✅ Staff permissions are enforced
- ✅ Response formats match original system
- ✅ Error handling is consistent

### Performance Requirements
- ✅ Response times under 500ms for single operations
- ✅ List operations handle pagination efficiently
- ✅ Concurrent operations don't cause conflicts
- ✅ Database queries are optimized

### Security Requirements
- ✅ Authentication required for all operations
- ✅ Staff permissions enforced for order placement
- ✅ Data validation prevents injection attacks
- ✅ Sensitive data is properly protected
