import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import FeatureFlag from 'App/Models/FeatureFlag'

export default class FeatureFlagSeeder extends BaseSeeder {
  public async run() {
    // Main billing system flag
    await FeatureFlag.create({
      name: 'Billing System',
      code: 'billing_system',
      description: 'Controls the entire billing system functionality',
      isEnabled: false,
      scope: 'global',
      conditions: {},
      meta: {
        disabledReason: 'Billing system is disabled by default. Enable when ready to start billing customers.'
      }
    })

    // Subscription billing
    await FeatureFlag.create({
      name: 'Subscription Billing',
      code: 'subscription_billing',
      description: 'Controls subscription-based billing functionality',
      isEnabled: false,
      scope: 'global',
      conditions: {},
      meta: {
        disabledReason: 'Subscription billing is disabled by default'
      }
    })

    // Usage-based billing
    await FeatureFlag.create({
      name: 'Usage Billing',
      code: 'usage_billing',
      description: 'Controls usage-based billing functionality',
      isEnabled: false,
      scope: 'global',
      conditions: {},
      meta: {
        disabledReason: 'Usage-based billing is disabled by default'
      }
    })

    // Campaign billing
    await FeatureFlag.create({
      name: 'Campaign Billing',
      code: 'campaign_billing',
      description: 'Controls campaign-related billing functionality',
      isEnabled: false,
      scope: 'global',
      conditions: {},
      meta: {
        disabledReason: 'Campaign billing is disabled by default'
      }
    })

    // Notification billing
    await FeatureFlag.create({
      name: 'Notification Billing',
      code: 'notification_billing',
      description: 'Controls notification-related billing functionality',
      isEnabled: false,
      scope: 'global',
      conditions: {},
      meta: {
        disabledReason: 'Notification billing is disabled by default'
      }
    })

    // Vendor billing
    await FeatureFlag.create({
      name: 'Vendor Billing',
      code: 'vendor_billing',
      description: 'Controls vendor-related billing functionality',
      isEnabled: false,
      scope: 'global',
      conditions: {},
      meta: {
        disabledReason: 'Vendor billing is disabled by default'
      }
    })
  }
}