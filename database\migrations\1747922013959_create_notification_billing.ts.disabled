import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'notification_billing_rules'

  public async up() {
    // Table for notification billing rules
    await this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('type').notNullable() // 'delivery', 'system', etc.
      table.string('name').notNullable() // 'order_assigned', 'order_delivered', etc.
      table.string('channel').notNullable() // 'email', 'sms', 'push'
      table.decimal('base_price', 10, 2).notNullable()
      table.integer('volume_threshold').nullable() // For volume discounts
      table.decimal('volume_discount', 5, 2).nullable() // Percentage discount
      table.boolean('is_active').defaultTo(true)
      table.json('meta').nullable()

      // Add indexes
      table.index('type')
      table.index('name')
      table.index('channel')
      table.unique(['type', 'name', 'channel'])

      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })




  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 