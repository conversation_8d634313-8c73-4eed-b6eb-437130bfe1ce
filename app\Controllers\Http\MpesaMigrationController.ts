import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Payment from 'App/Models/Payment'
import KeMpesa from 'App/Services/Mpesa' // Current implementation
import OfficialMpesaService from 'App/Services/OfficialMpesa' // New official implementation
import MpesaConfigValidator from 'App/Services/MpesaConfigValidator'

/**
 * Interface for M-Pesa implementation configuration
 */
interface MpesaImplementationConfig {
  library: string;
  status: string;
  [key: string]: any;
}

/**
 * M-Pesa Migration Controller
 *
 * This controller helps with testing and migrating from the current
 * M-Pesa implementation to the official Safaricom library.
 */
export default class MpesaMigrationController {
  
  /**
   * Compare both M-Pesa implementations
   */
  public async compare({ response }: HttpContextContract) {
    try {
      console.log('=== MPESA IMPLEMENTATION COMPARISON ===')
      
      // Create dummy payment for testing
      const dummyPayment = new Payment()
      dummyPayment.id = 'test-payment-id'
      dummyPayment.amount = 100
      
      // Test current implementation
      let currentConfig: MpesaImplementationConfig | null = null
      let currentError: string | null = null
      try {
        new KeMpesa(dummyPayment) // Test initialization only
        currentConfig = {
          library: 'osenco-mpesa',
          status: 'initialized',
          // Add any config summary if available
        }
      } catch (error) {
        currentError = error.message
      }

      // Test official implementation
      let officialConfig: any = null
      let officialError: string | null = null
      try {
        const officialMpesa = new OfficialMpesaService(dummyPayment)
        officialConfig = officialMpesa.getConfigSummary()
      } catch (error) {
        officialError = error.message
      }
      
      // Get configuration validation
      const validation = MpesaConfigValidator.validate()
      
      const comparison = {
        timestamp: new Date().toISOString(),
        configurationValidation: validation,
        implementations: {
          current: {
            library: '@osenco/mpesa',
            status: currentError ? 'error' : 'working',
            config: currentConfig,
            error: currentError
          },
          official: {
            library: 'mpesa-node (official)',
            status: officialError ? 'error' : 'working',
            config: officialConfig,
            error: officialError
          }
        },
        recommendation: {
          migrate: !officialError,
          reason: officialError 
            ? 'Official library has configuration issues that need to be resolved'
            : 'Official library is properly configured and ready for migration'
        }
      }
      
      console.log('Comparison Result:', JSON.stringify(comparison, null, 2))
      
      return response.json(comparison)
      
    } catch (error) {
      console.error('=== COMPARISON ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to compare implementations',
        message: error.message
      })
    }
  }
  
  /**
   * Test STK Push with official library
   */
  public async testOfficialSTKPush({ request, response }: HttpContextContract) {
    try {
      const { phone, amount, reference } = request.only(['phone', 'amount', 'reference'])
      
      if (!phone || !amount || !reference) {
        return response.badRequest({
          error: 'Missing required parameters',
          required: ['phone', 'amount', 'reference']
        })
      }
      
      console.log('=== TESTING OFFICIAL STK PUSH ===')
      
      const officialMpesa = new OfficialMpesaService()
      const result = await officialMpesa.stkPush(phone, amount, reference, 'Test Payment')
      
      console.log('Official STK Push Result:', JSON.stringify(result, null, 2))
      
      return response.json({
        library: 'official-mpesa-node',
        result,
        timestamp: new Date().toISOString()
      })
      
    } catch (error) {
      console.error('=== OFFICIAL STK PUSH TEST ERROR ===', error)
      return response.status(500).json({
        error: 'Official STK Push test failed',
        message: error.message
      })
    }
  }
  
  /**
   * Test current implementation STK Push
   */
  public async testCurrentSTKPush({ request, response }: HttpContextContract) {
    try {
      const { phone, amount, reference, orderId } = request.only(['phone', 'amount', 'reference', 'orderId'])
      
      if (!phone || !amount || !reference) {
        return response.badRequest({
          error: 'Missing required parameters',
          required: ['phone', 'amount', 'reference', 'orderId']
        })
      }
      
      console.log('=== TESTING CURRENT STK PUSH ===')
      
      // Create dummy payment
      const dummyPayment = new Payment()
      dummyPayment.id = 'test-payment-current'
      dummyPayment.amount = parseInt(amount)
      
      const currentMpesa = new KeMpesa(dummyPayment)
      const result = await currentMpesa.stkPush(phone, amount, reference, orderId || 'test-order')
      
      console.log('Current STK Push Result:', JSON.stringify(result, null, 2))
      
      return response.json({
        library: 'osenco-mpesa',
        result,
        timestamp: new Date().toISOString()
      })
      
    } catch (error) {
      console.error('=== CURRENT STK PUSH TEST ERROR ===', error)
      return response.status(500).json({
        error: 'Current STK Push test failed',
        message: error.message
      })
    }
  }
  
  /**
   * Register C2B URLs with official library
   */
  public async registerC2BUrls({ response }: HttpContextContract) {
    try {
      console.log('=== REGISTERING C2B URLS ===')
      
      const officialMpesa = new OfficialMpesaService()
      const result = await officialMpesa.registerC2BUrls()
      
      console.log('C2B Registration Result:', JSON.stringify(result, null, 2))
      
      return response.json({
        action: 'c2b-registration',
        result,
        timestamp: new Date().toISOString()
      })
      
    } catch (error) {
      console.error('=== C2B REGISTRATION ERROR ===', error)
      return response.status(500).json({
        error: 'C2B registration failed',
        message: error.message
      })
    }
  }
  
  /**
   * Check account balance using official library
   */
  public async checkBalance({ response }: HttpContextContract) {
    try {
      console.log('=== CHECKING ACCOUNT BALANCE ===')
      
      const officialMpesa = new OfficialMpesaService()
      const result = await officialMpesa.checkAccountBalance()
      
      console.log('Balance Check Result:', JSON.stringify(result, null, 2))
      
      return response.json({
        action: 'balance-check',
        result,
        timestamp: new Date().toISOString()
      })
      
    } catch (error) {
      console.error('=== BALANCE CHECK ERROR ===', error)
      return response.status(500).json({
        error: 'Balance check failed',
        message: error.message
      })
    }
  }
  
  /**
   * Migration status and recommendations
   */
  public async migrationStatus({ response }: HttpContextContract) {
    try {
      const validation = MpesaConfigValidator.validate()
      
      // Check if official library can be initialized
      let canMigrate = false
      let migrationBlockers: string[] = []

      try {
        new OfficialMpesaService()
        canMigrate = true
      } catch (error) {
        migrationBlockers.push(error.message)
      }
      
      const status = {
        timestamp: new Date().toISOString(),
        configurationValid: validation.isValid,
        configurationErrors: validation.errors,
        configurationWarnings: validation.warnings,
        canMigrate,
        migrationBlockers,
        nextSteps: canMigrate 
          ? [
              'Test STK Push with official library',
              'Register C2B URLs',
              'Update payment controllers to use official service',
              'Deploy to staging for testing',
              'Monitor callback success rates',
              'Deploy to production'
            ]
          : [
              'Fix configuration errors',
              'Install mpesa-node library: npm install mpesa-node',
              'Set required environment variables',
              'Test configuration again'
            ],
        recommendedActions: [
          'Install official library: npm install mpesa-node',
          'Update environment variables for official library',
          'Test both implementations side by side',
          'Gradually migrate endpoints',
          'Monitor callback success rates'
        ]
      }
      
      return response.json(status)
      
    } catch (error) {
      console.error('=== MIGRATION STATUS ERROR ===', error)
      return response.status(500).json({
        error: 'Failed to get migration status',
        message: error.message
      })
    }
  }
}
