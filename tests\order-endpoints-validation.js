/**
 * Comprehensive Order Endpoints Validation Script
 * Tests all order endpoints to ensure proper JSON responses with pricing information
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://uat-api.appinapp.ke';
const TEST_CONFIG = {
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Helper function to log test results
function logTest(testName, passed, details = '') {
  if (passed) {
    console.log(`✅ ${testName}`);
    testResults.passed++;
  } else {
    console.log(`❌ ${testName}: ${details}`);
    testResults.failed++;
    testResults.errors.push({ test: testName, error: details });
  }
}

// Helper function to validate pricing structure
function validatePricingStructure(pricing) {
  const requiredFields = ['subtotal', 'modifiersTotal', 'chargesTotal', 'total', 'invoiceAmount', 'currency'];
  
  if (!pricing) {
    return { valid: false, error: 'Pricing object is missing' };
  }

  for (const field of requiredFields) {
    if (pricing[field] === undefined || pricing[field] === null) {
      return { valid: false, error: `Missing pricing field: ${field}` };
    }
    
    if (field !== 'currency' && typeof pricing[field] !== 'number') {
      return { valid: false, error: `Invalid pricing field type for ${field}: expected number, got ${typeof pricing[field]}` };
    }
  }

  if (pricing.currency !== 'KES') {
    return { valid: false, error: `Invalid currency: expected KES, got ${pricing.currency}` };
  }

  // Validate pricing logic
  const calculatedTotal = pricing.subtotal + pricing.modifiersTotal + pricing.chargesTotal;
  const tolerance = 0.01;
  
  if (Math.abs(calculatedTotal - pricing.total) > tolerance) {
    return { 
      valid: false, 
      error: `Pricing calculation mismatch: subtotal(${pricing.subtotal}) + modifiersTotal(${pricing.modifiersTotal}) + chargesTotal(${pricing.chargesTotal}) = ${calculatedTotal}, but total is ${pricing.total}` 
    };
  }

  return { valid: true };
}

// Helper function to validate order structure
function validateOrderStructure(order) {
  const requiredFields = ['id', 'status', 'pricing'];
  
  for (const field of requiredFields) {
    if (!order[field]) {
      return { valid: false, error: `Missing order field: ${field}` };
    }
  }

  // Validate pricing
  const pricingValidation = validatePricingStructure(order.pricing);
  if (!pricingValidation.valid) {
    return pricingValidation;
  }

  // Check for Lucid model internals (should not be present)
  const lucidInternals = ['$attributes', '$columns', '$original', '$preloaded', 'modelOptions'];
  for (const internal of lucidInternals) {
    if (order[internal]) {
      return { valid: false, error: `Lucid model internal found: ${internal}` };
    }
  }

  return { valid: true };
}

// Test function for paginated order endpoints
async function testPaginatedOrderEndpoint(endpointName, url, authToken = null) {
  try {
    const config = { ...TEST_CONFIG };
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    const response = await axios.get(url, config);
    
    // Check response status
    if (response.status !== 200) {
      logTest(`${endpointName} - Status Code`, false, `Expected 200, got ${response.status}`);
      return;
    }

    const data = response.data;

    // Check pagination structure
    if (!data.meta || !data.data) {
      logTest(`${endpointName} - Pagination Structure`, false, 'Missing meta or data fields');
      return;
    }

    logTest(`${endpointName} - Pagination Structure`, true);

    // Validate each order in the response
    if (data.data.length > 0) {
      let validOrders = 0;
      let invalidOrders = 0;

      for (const order of data.data) {
        const validation = validateOrderStructure(order);
        if (validation.valid) {
          validOrders++;
        } else {
          invalidOrders++;
          console.log(`   ⚠️  Order ${order.id || 'unknown'}: ${validation.error}`);
        }
      }

      logTest(`${endpointName} - Order Structure (${validOrders}/${data.data.length} valid)`, 
               invalidOrders === 0, 
               invalidOrders > 0 ? `${invalidOrders} orders have invalid structure` : '');
    } else {
      logTest(`${endpointName} - Order Structure`, true, 'No orders to validate');
    }

  } catch (error) {
    logTest(`${endpointName} - Request`, false, error.message);
  }
}

// Test function for single order endpoints
async function testSingleOrderEndpoint(endpointName, url, authToken = null) {
  try {
    const config = { ...TEST_CONFIG };
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    const response = await axios.get(url, config);
    
    // Check response status
    if (response.status !== 200) {
      logTest(`${endpointName} - Status Code`, false, `Expected 200, got ${response.status}`);
      return;
    }

    const order = response.data;

    // Validate order structure
    const validation = validateOrderStructure(order);
    logTest(`${endpointName} - Order Structure`, validation.valid, validation.error || '');

    // Check for QR code if this is a show endpoint
    if (endpointName.includes('Show') && !order.qrCode) {
      logTest(`${endpointName} - QR Code`, false, 'QR code is missing');
    } else if (endpointName.includes('Show')) {
      logTest(`${endpointName} - QR Code`, true);
    }

  } catch (error) {
    logTest(`${endpointName} - Request`, false, error.message);
  }
}

// Main test execution
async function runOrderEndpointTests() {
  console.log('🚀 Starting Order Endpoints Validation Tests\n');
  console.log('Testing against:', BASE_URL);
  console.log('=' * 60);

  // Test public endpoints (no auth required)
  console.log('\n📋 Testing Public Order Endpoints:');
  
  await testPaginatedOrderEndpoint(
    'Orders Index', 
    `${BASE_URL}/api/v1/orders?per=5&page=1`
  );

  // Test endpoints that might require authentication
  console.log('\n🔐 Testing Authenticated Order Endpoints:');
  
  // Note: In a real test, you would get a valid auth token
  // For now, we'll test without auth and expect appropriate responses
  
  await testPaginatedOrderEndpoint(
    'User Orders Index', 
    `${BASE_URL}/api/v1/user/orders?per=5&page=1`
  );

  await testPaginatedOrderEndpoint(
    'Auth Orders by Role', 
    `${BASE_URL}/api/v1/auth/orders?per=5&page=1`
  );

  // Test vendor-specific endpoints (would need vendor context)
  console.log('\n🏪 Testing Vendor Order Endpoints:');
  
  // These would typically require vendor authentication
  // await testPaginatedOrderEndpoint(
  //   'Vendor Orders Index', 
  //   `${BASE_URL}/api/v1/vendors/{vendorId}/orders?per=5&page=1`
  // );

  // Test branch-specific endpoints (would need branch context)
  console.log('\n🏢 Testing Branch Order Endpoints:');
  
  // These would typically require branch authentication
  // await testPaginatedOrderEndpoint(
  //   'Branch Orders Index', 
  //   `${BASE_URL}/api/v1/branches/{branchId}/orders?per=5&page=1`
  // );

  // Summary
  console.log('\n' + '=' * 60);
  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  if (testResults.errors.length > 0) {
    console.log('\n🔍 Detailed Error Report:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }

  console.log('\n✨ Order Endpoints Validation Complete!');
}

// Export for use in other test files
module.exports = {
  validatePricingStructure,
  validateOrderStructure,
  testPaginatedOrderEndpoint,
  testSingleOrderEndpoint,
  runOrderEndpointTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runOrderEndpointTests().catch(console.error);
}
