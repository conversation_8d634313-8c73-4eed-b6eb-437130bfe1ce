import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, computed } from '@ioc:Adonis/Lucid/Orm'
import Product from './Product'
import ServiceOption from './ServiceOption'

export default class ProductServiceOption extends BaseModel {
  public static table = 'product_service_options'

  @column({ isPrimary: true })
  public id: number

  @column()
  public productId: string

  @column()
  public serviceOptionId: string

  @column()
  public priceAdjustmentOverride: number | null

  @column()
  public isDefault: boolean

  @column()
  public sortOrder: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Product)
  public product: BelongsTo<typeof Product>

  @belongsTo(() => ServiceOption)
  public serviceOption: BelongsTo<typeof ServiceOption>

  @computed()
  public get effectivePriceAdjustment(): number {
    // Use override if provided, otherwise use the service option's default
    return this.priceAdjustmentOverride ?? this.serviceOption?.defaultPriceAdjustment ?? 0
  }

  @computed()
  public get displayPrice(): string {
    const price = this.effectivePriceAdjustment
    if (price === 0) {
      return 'Included'
    } else if (price > 0) {
      return `+$${price.toFixed(2)}`
    } else {
      return `-$${Math.abs(price).toFixed(2)}`
    }
  }

  /**
   * Check if this pivot has a price override
   */
  public hasPriceOverride(): boolean {
    return this.priceAdjustmentOverride !== null
  }

  /**
   * Get the calendar block time if this is a duration option
   */
  public async getCalendarBlockMinutes(): Promise<number | null> {
    if (!this.serviceOption) {
      await this.load('serviceOption', (query) => {
        query.preload('duration')
      })
    }

    return await this.serviceOption.getCalendarBlockMinutes()
  }

  /**
   * Validate the pivot configuration
   */
  public async validateConfiguration(): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Validate that both product and service option exist
    if (!this.productId) {
      errors.push('Product ID is required')
    }

    if (!this.serviceOptionId) {
      errors.push('Service option ID is required')
    }

    // Validate sort order
    if (this.sortOrder < 0) {
      errors.push('Sort order must be non-negative')
    }

    // Validate price adjustment override if provided
    if (this.priceAdjustmentOverride !== null && typeof this.priceAdjustmentOverride !== 'number') {
      errors.push('Price adjustment override must be a number')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Create a product service option with default values
   */
  public static async createWithDefaults(
    productId: string,
    serviceOptionId: string,
    options: Partial<ProductServiceOption> = {}
  ): Promise<ProductServiceOption> {
    // Get the next sort order for this product
    const lastOption = await ProductServiceOption.query()
      .where('productId', productId)
      .orderBy('sortOrder', 'desc')
      .first()

    const nextSortOrder = lastOption ? lastOption.sortOrder + 1 : 1

    return await ProductServiceOption.create({
      productId,
      serviceOptionId,
      priceAdjustmentOverride: null,
      isDefault: false,
      sortOrder: nextSortOrder,
      ...options
    })
  }

  /**
   * Bulk update sort orders for a product
   */
  public static async updateSortOrders(productId: string, optionOrders: { id: number; sortOrder: number }[]): Promise<void> {
    for (const { id, sortOrder } of optionOrders) {
      await ProductServiceOption.query()
        .where('id', id)
        .where('productId', productId)
        .update({ sortOrder })
    }
  }

  /**
   * Get default options for a product
   */
  public static async getDefaults(productId: string) {
    return await ProductServiceOption.query()
      .where('productId', productId)
      .where('isDefault', true)
      .preload('serviceOption', (query) => {
        query.preload('duration')
      })
      .orderBy('sortOrder')
  }

  /**
   * Get options by type for a product
   */
  public static async getByType(productId: string, type: string) {
    return await ProductServiceOption.query()
      .where('productId', productId)
      .whereHas('serviceOption', (query) => {
        query.where('type', type).where('active', true)
      })
      .preload('serviceOption', (query) => {
        query.preload('duration')
      })
      .orderBy('sortOrder')
  }

  /**
   * Check for conflicts when adding a new option
   */
  public static async checkConflicts(
    productId: string,
    serviceOptionId: string
  ): Promise<{ hasConflicts: boolean; conflicts: string[] }> {
    const conflicts: string[] = []

    // Get existing options for the product
    const existingOptions = await ProductServiceOption.query()
      .where('productId', productId)
      .preload('serviceOption')

    // Get the new service option
    const newOption = await ServiceOption.query()
      .where('id', serviceOptionId)
      .preload('duration')
      .first()

    if (!newOption) {
      return { hasConflicts: true, conflicts: ['Service option not found'] }
    }

    // Check for type conflicts (except add-ons)
    if (newOption.type !== 'add_on') {
      const sameTypeOptions = existingOptions.filter(
        pivot => pivot.serviceOption.type === newOption.type
      )
      
      if (sameTypeOptions.length > 0) {
        conflicts.push(`Product already has ${newOption.type} option: ${sameTypeOptions[0].serviceOption.name}`)
      }
    }

    // Check constraint conflicts
    for (const existingPivot of existingOptions) {
      if (!newOption.canCombineWith(existingPivot.serviceOption)) {
        conflicts.push(`Option '${newOption.name}' cannot be combined with '${existingPivot.serviceOption.name}'`)
      }
    }

    return {
      hasConflicts: conflicts.length > 0,
      conflicts
    }
  }

  /**
   * Remove option from product
   */
  public static async removeFromProduct(productId: string, serviceOptionId: string): Promise<boolean> {
    const pivot = await ProductServiceOption.query()
      .where('productId', productId)
      .where('serviceOptionId', serviceOptionId)
      .first()

    if (pivot) {
      await pivot.delete()
      return true
    }

    return false
  }

  /**
   * Get statistics for a product's service options
   */
  public static async getProductStatistics(productId: string): Promise<{
    totalOptions: number
    defaultOptions: number
    optionsByType: Record<string, number>
    totalPriceAdjustment: number
    hasCalendarIntegration: boolean
  }> {
    const options = await ProductServiceOption.query()
      .where('productId', productId)
      .preload('serviceOption', (query) => {
        query.preload('duration')
      })

    const optionsByType = options.reduce((counts, pivot) => {
      const type = pivot.serviceOption.type
      counts[type] = (counts[type] || 0) + 1
      return counts
    }, {} as Record<string, number>)

    const totalPriceAdjustment = options.reduce((total, pivot) => {
      return total + pivot.effectivePriceAdjustment
    }, 0)

    const hasCalendarIntegration = options.some(pivot => 
      pivot.serviceOption.hasCalendarIntegration
    )

    return {
      totalOptions: options.length,
      defaultOptions: options.filter(pivot => pivot.isDefault).length,
      optionsByType,
      totalPriceAdjustment,
      hasCalendarIntegration
    }
  }
}
