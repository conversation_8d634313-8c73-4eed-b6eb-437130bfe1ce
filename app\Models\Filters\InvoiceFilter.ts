import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Invoice from '../Invoice'

export default class InvoiceFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Invoice, Invoice>

  public status(value: string): void {
    this.$query.where('status', value)
  }

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }
}
