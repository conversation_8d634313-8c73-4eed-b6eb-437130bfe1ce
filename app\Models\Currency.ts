import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import CurrencyFilter from './Filters/CurrencyFilter'

export default class Currency extends compose(BaseModel, Filterable) {
  public static $filter = () => CurrencyFilter

  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public symbol: string

  @column()
  public code: string

  @column()
  public rate: number

  @column({ columnName: 'default' })
  public isDefault: boolean

  @column()
  public active: boolean

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
