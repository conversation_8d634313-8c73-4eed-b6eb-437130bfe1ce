import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'message_actions'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('message_id').unsigned().references('id').inTable('messages').onDelete('CASCADE')
      table.integer('type_id').unsigned().references('id').inTable('action_types')
      table.jsonb('config').notNullable().defaultTo('{}')
      table.string('status', 50).notNullable().defaultTo('pending')
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
} 