import { DateTime } from 'luxon'
import {
  column,
  beforeSave,
  BaseModel,
  computed,
  HasMany,
  hasMany,
  manyToMany,
  ManyToMany,
  belongsTo,
  BelongsTo,
  beforeCreate,
} from '@ioc:Adonis/Lucid/Orm'
import { compose } from '@ioc:Adonis/Core/Helpers'
import { attachment, AttachmentContract } from '@ioc:Adonis/Addons/AttachmentLite'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { SoftDeletes } from '@ioc:Adonis/Addons/LucidSoftDeletes'
import Env from '@ioc:Adonis/Core/Env'
import Database from '@ioc:Adonis/Lucid/Database'
import VendorFilter from './Filters/VendorFilter'
import Device from './Device'
import crypto from 'crypto'
import CamelCaseStrategy from '../Strategies/CamelCaseStrategy'
import Payment from './Payment'
import User from './User'
// import Attachment from './Attachment'
import Product from './Product'
import { ulid } from 'ulidx'
import Speciality from './Speciality'
import VendorCategory from './VendorCategory'
import Branch from './Branch'
import Service from './Service'
import Task from './Task'
import Group from './Group'
import NotificationUsage from './NotificationUsage'
import ServiceArea from './ServiceArea'

export default class Vendor extends compose(BaseModel, Filterable, SoftDeletes) {
  public static $filter = () => VendorFilter
  public static namingStrategy = new CamelCaseStrategy()

  @column({ isPrimary: true })
  public id: string

  @column()
  public userId: string

  @column()
  public serviceId: string

  @column()
  public name: string

  @column()
  public code: string

  @column()
  public slug: string

  @column()
  public email: string

  @column()
  public phone: string

  @column()
  public reg: string

  @column()
  public kra: string

  @column()
  public permit: string

  @column()
  public details: string | null

  @column()
  public active: boolean

  @column()
  public featured: boolean

  @attachment({ folder: 'logos', preComputeUrl: true })
  public logo: AttachmentContract | null

  @attachment({ folder: 'covers', preComputeUrl: true })
  public cover: AttachmentContract | null

  @column()
  public location: {
    name?: string
    address: string
    regions: {
      administrative_area_level_3?: string | null
      administrative_area_level_1?: string | null
      country: string
    }
    coordinates: {
      lat: number
      lng: number
    }
    place_id: string
  } | null

  @column()
  public geom?: any

  // Delivery-related properties
  @column()
  public deliveryPreferences: Record<string, any> | null

  @column()
  public availabilitySettings: Record<string, any> | null

  @column()
  public pricingStructure: Record<string, any> | null

  @column()
  public verificationStatus: 'pending' | 'verified' | 'rejected'

  @column()
  public verificationNotes: string | null

  @column.dateTime()
  public verifiedAt: DateTime | null

  @computed()
  public get logoUrl() {
    if (!this.logo) {
      const hash = crypto
        .createHash('md5')
        .update(this.email ?? '<EMAIL>')
        .digest('hex')

      return `https://www.gravatar.com/avatar/${hash}?s=200&d=mp`
    }
  }

  @beforeCreate()
  public static async generateUlid(vendor: Vendor) {
    vendor.id = ulid().toLowerCase()
  }

  @beforeSave()
  public static async setGeom(vendor: Vendor) {
    if (vendor.location?.coordinates) {
      if (Env.get('DB_CONNECTION') === 'pg') {
        vendor.geom = Database.st().geomFromText(
          `POINT(${vendor.location.coordinates.lng} ${vendor.location.coordinates.lat})`,
          4326
        )
      } else {
        vendor.geom = Database.raw(
          `GeomFromText('POINT(${vendor.location.coordinates.lng} ${vendor.location.coordinates.lat})', 4326)`
        )
      }
    }
  }

  @column()
  public rememberMeToken: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime()
  public deletedAt: DateTime

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @hasMany(() => Branch)
  public branches: HasMany<typeof Branch>

  @hasMany(() => Payment)
  public payments: HasMany<typeof Payment>

  @hasMany(() => Device)
  public devices: HasMany<typeof Device>

  @hasMany(() => Product)
  public products: HasMany<typeof Product>

  @hasMany(() => Group)
  public groups: HasMany<typeof Group>

  // @hasMany(() => Attachment, {
  //   foreignKey: 'attachableId',
  //   onQuery: (query) => query.where('attachableType', 'Vendor'),
  // })
  // public attachments: HasMany<typeof Attachment>

  @manyToMany(() => User, {
    pivotTable: 'staff',
    pivotTimestamps: true,
    pivotColumns: ['branch_id', 'identifier'],
  })
  public staff: ManyToMany<typeof User>

  @manyToMany(() => Service, {
    pivotTable: 'vendor_services',
    pivotTimestamps: true,
    pivotColumns: ['active'],
  })
  public services: ManyToMany<typeof Service>

  @manyToMany(() => Task, {
    pivotTable: 'vendor_tasks',
    pivotTimestamps: true,
    pivotColumns: ['active'],
  })
  public tasks: ManyToMany<typeof Task>

  @manyToMany(() => VendorCategory, {
    pivotTable: 'vendor_set_categories',
    pivotTimestamps: true,
  })
  public categories: ManyToMany<typeof VendorCategory>

  @manyToMany(() => Speciality, {
    pivotTable: 'vendor_specialities',
    pivotTimestamps: true,
  })
  public specialities: ManyToMany<typeof Speciality>

  @manyToMany(() => User, {
    pivotTable: 'customers',
    pivotTimestamps: true,
    pivotColumns: ['active', 'branch_id'],
    onQuery(query) {
      query.pivotColumns(['active', 'branch_id'])
    },
  })
  public customers: ManyToMany<typeof User>

  @hasMany(() => NotificationUsage)
  public notificationUsages: HasMany<typeof NotificationUsage>

  @hasMany(() => ServiceArea)
  public serviceAreas: HasMany<typeof ServiceArea>
}
