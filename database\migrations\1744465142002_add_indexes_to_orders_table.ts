import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add individual indexes
      table.index('user_id')
      table.index('staff_id')
      table.index('vendor_id')
      table.index('branch_id')
      table.index('lot_id')
      table.index('section_id')
      table.index('status')
      table.index('created_at')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove indexes
      table.dropIndex('user_id')
      table.dropIndex('staff_id')
      table.dropIndex('vendor_id')
      table.dropIndex('branch_id')
      table.dropIndex('lot_id')
      table.dropIndex('section_id')
      table.dropIndex('status')
      table.dropIndex('created_at')
    })
  }
} 