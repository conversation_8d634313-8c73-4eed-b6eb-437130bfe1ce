{"typescript": true, "commands": ["./commands", "@adonisjs/core/build/commands/index.js", "@adonisjs/repl/build/commands", "@adonisjs/lucid/build/commands", "@adonisjs/mail/build/commands", "@verful/notifications/build/commands", "@verful/permissions/build/commands", "@melchyore/adonis-lucid-observer/build/commands", "adonis-lucid-filter/build/commands", "adonisjs-scheduler/build/commands", "@rlanz/bull-queue/build/commands"], "exceptionHandlerNamespace": "App/Exceptions/Handler", "aliases": {"App": "app", "Config": "config", "Database": "database", "Contracts": "contracts", "Commands": "commands"}, "preloads": ["./start/routes", "./start/kernel", "./start/notification", "./start/db", "./start/events", {"file": "./start/scheduler", "environment": ["console"]}, "./start/bots"], "providers": ["./providers/AppProvider", "@adonisjs/core", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/ally", "@adonisjs/route-model-binding/build/providers/RmbProvider", "@adonisjs/mail", "@adonisjs/view", "@adonisjs/attachment-lite", "@verful/notifications", "@verful/permissions", "@melchyore/adonis-lucid-observer", "./providers/ObserverProvider", "adonis-lucid-filter", "adonis-lucid-soft-deletes", "adonis-fcm", "@adonisjs/redis", "@adonisjs/drive-s3", "@adonisjs/i18n", "adonisjs-scheduler", "@bitkidd/adonis-ally-apple", "@rlanz/bull-queue"], "aceProviders": ["@adonisjs/repl"], "tests": {"suites": [{"name": "functional", "files": ["tests/functional/**/*.spec(.ts|.js)"], "timeout": 60000}]}, "testProviders": ["@japa/preset-adonis/TestsProvider"], "metaFiles": [{"pattern": "resources/views/**/*.edge", "reloadServer": false}, "resources/lang/**/*.(json|yaml)"], "namespaces": {"observers": "App/Observers"}}