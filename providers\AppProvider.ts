import type { ApplicationContract } from '@ioc:Adonis/Core/Application'
import CamelCaseStrategy from '../app/Strategies/CamelCaseStrategy'

export default class AppProvider {
  constructor(protected app: ApplicationContract) {}

  public register() {
    // Register your own bindings
  }

  public async boot() {
    // IoC container is ready
    const { BaseModel } = await import('@ioc:Adonis/Lucid/Orm')
    BaseModel.namingStrategy = new CamelCaseStrategy()
  }

  public async ready() {
    // App is ready
    const db = this.app.container.use('Adonis/Lucid/Database')

    db.SimplePaginator.namingStrategy = new CamelCaseStrategy()
  }

  public async shutdown() {
    // Cleanup, since app is going down
  }
}
