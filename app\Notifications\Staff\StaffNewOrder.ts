import { NotificationContract } from '@ioc:Verful/Notification'
import Order from 'App/Models/Order'
import User from 'App/Models/User'
import { NotificationMessagePayload } from 'firebase-admin/messaging'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

export default class StaffNewOrder implements NotificationContract {
  constructor(
    private order: Order,
    public message: string = 'New order from {firstName}',
    public title: string = 'New Order Received'
  ) {
    this.message = message || `New order from ${this.order.customer.firstName}`
  }

  public via(_notifiable) {
    return ['database' as const, 'fcm' as const]
  }

  public toDatabase() {
    const personalizedMessage = this.message.replace('{firstName}', this.order.customer.firstName)

    return NotificationHelper.createNotificationData(
      this.title,
      personalizedMessage,
      NotificationHelper.createOrderActions(this.order.id, this.order.status, this.order.vendor?.name),
      {
        category: NotificationCategory.ORDER,
        notificationType: NotificationType.ORDER_CREATED,
        priority: NotificationPriority.HIGH, // High priority for staff notifications
        entityId: this.order.id,
        entityType: 'order',
        orderStatus: this.order.status,
        customerId: this.order.customer?.id,
        customerName: `${this.order.customer?.firstName} ${this.order.customer?.lastName}`,
        vendorId: this.order.vendor?.id,
        vendorName: this.order.vendor?.name,
        orderTotal: this.order.total,
        currency: 'KES',
        orderDate: this.order.createdAt?.toISO(),
        userRole: 'staff'
      },
      'https://cdn.verful.com/icons/staff-order-icon.png'
    )
  }

  public toFcm(_notifiable: User): NotificationMessagePayload {
    const body = this.message.replace('{firstName}', this.order.customer.firstName)

    return {
      title: this.title,
      body,
      // url: `aiastaff://orders/${this.order.id}`,
      // icon: 'https://cdn.verful.com/icons/verful-512x512.png',
    }
  }
}
