import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class AddItemsColumnToOrders extends BaseSchema {
  protected tableName = 'orders'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add items column to support direct order creation
      table.json('items').nullable().comment('Direct items storage for unified order system')
    })

    // Add indexes for better performance
    this.schema.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_items_gin 
      ON orders USING GIN (items);
    `)

    this.schema.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_meta_gin 
      ON orders USING GIN (meta);
    `)

    this.schema.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status_type 
      ON orders (status, type);
    `)

    this.schema.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_vendor_status 
      ON orders (vendor_id, status);
    `)

    this.schema.raw(`
      CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_branch_status 
      ON orders (branch_id, status);
    `)
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('items')
    })

    // Drop indexes
    this.schema.raw('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_items_gin')
    this.schema.raw('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_meta_gin')
    this.schema.raw('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_status_type')
    this.schema.raw('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_vendor_status')
    this.schema.raw('DROP INDEX CONCURRENTLY IF EXISTS idx_orders_branch_status')
  }
}
