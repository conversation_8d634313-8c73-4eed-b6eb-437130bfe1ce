import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import ProductType from '../../Models/ProductType'
import { bind } from '@adonisjs/route-model-binding'

export default class ProductTypesController {
  /**
   * @index
   * @name ProductType management
   * @summary Show all ProductTypes
   * @version 1.0.0
   * @description ProductType management for the application
   * @paramUse filterable
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */

  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const typeQuery = ProductType.filter(filters)

    return await typeQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a ProductType
   * @description Create a ProductType with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <ProductType>
   */

  public async store({ request }: HttpContextContract) {
    const { name, details, serviceId } = request.all()
    const type = new ProductType()

    type.fill({ name, details, serviceId })

    const image = request.file('image')

    if (image) {
      type.image = Attachment.fromFile(image)
    }

    return await type.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a single ProductType
   * @description Show a ProductType with their details (name and details)
   * @responseBody 200 - <ProductType>
   * @response 404 - Product not found
   */
  public async show({ response }: HttpContextContract, type: ProductType) {
    return response.json(type)
  }

  @bind()

  /**
   * @update
   * @summary Update a ProductType
   * @description Update a ProductType with their details (name and details)
   * @requestBody <ProductType>
   * @responseBody 200 - <ProductType>
   * @response 404 - ProductType not found
   */
  public async update({ request, response }: HttpContextContract, type: ProductType) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await type.merge(input).save()

    return response.json(type)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a ProductType
   * @reponseBody 204 - No content
   */
  public async destroy({ response }: HttpContextContract, type: ProductType) {
    await type.delete()

    return response.noContent()
  }
}
