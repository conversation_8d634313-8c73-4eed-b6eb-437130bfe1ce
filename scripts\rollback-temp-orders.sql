-- Temp Orders Rollback Script
-- Purpose: Restore temp_orders table from backup if migration fails

-- 1. Verify backup exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'temp_orders_backup') THEN
        RAISE EXCEPTION 'Backup table temp_orders_backup does not exist. Cannot rollback.';
    END IF;
    
    RAISE NOTICE 'Backup table found. Proceeding with rollback...';
END $$;

-- 2. Update migration status
UPDATE temp_orders_backup_metadata 
SET migration_status = 'rollback_initiated'
WHERE backup_date = (SELECT MAX(backup_date) FROM temp_orders_backup_metadata);

-- 3. Clear current temp_orders table
TRUNCATE TABLE temp_orders CASCADE;

-- 4. Restore data from backup
INSERT INTO temp_orders 
SELECT * FROM temp_orders_backup;

-- 5. Verify restoration
DO $$
DECLARE
    restored_count INTEGER;
    backup_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO restored_count FROM temp_orders;
    SELECT COUNT(*) INTO backup_count FROM temp_orders_backup;
    
    IF restored_count = backup_count THEN
        RAISE NOTICE 'Rollback successful: % records restored', restored_count;
    ELSE
        RAISE EXCEPTION 'Rollback failed: backup=%, restored=%', backup_count, restored_count;
    END IF;
END $$;

-- 6. Update migration status to completed
UPDATE temp_orders_backup_metadata 
SET migration_status = 'rollback_completed'
WHERE backup_date = (SELECT MAX(backup_date) FROM temp_orders_backup_metadata);

-- 7. Verify data integrity after rollback
SELECT 
    'temp_orders_restored' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_count,
    MIN(created_at) as oldest_record,
    MAX(created_at) as newest_record,
    COUNT(CASE WHEN meta IS NOT NULL THEN 1 END) as records_with_meta,
    COUNT(CASE WHEN items IS NOT NULL THEN 1 END) as records_with_items
FROM temp_orders;

-- 8. Sample data verification
SELECT 
    'Sample restored data' as description,
    id,
    status,
    vendor_id,
    branch_id,
    created_at
FROM temp_orders 
ORDER BY created_at DESC 
LIMIT 5;

-- 9. Clean up unified orders if they were created during migration
-- WARNING: Only run this if you're sure the migration created orders with status 'Pending'
-- that should be removed during rollback

-- Uncomment the following lines if you need to remove migrated orders:
/*
DELETE FROM order_items 
WHERE order_id IN (
    SELECT id FROM orders 
    WHERE status = 'Pending' 
    AND created_at >= (SELECT MIN(backup_date) FROM temp_orders_backup_metadata)
);

DELETE FROM orders 
WHERE status = 'Pending' 
AND created_at >= (SELECT MIN(backup_date) FROM temp_orders_backup_metadata);
*/

COMMIT;

-- 10. Final verification message
SELECT 
    'Rollback completed successfully' as status,
    NOW() as completion_time,
    COUNT(*) as temp_orders_count
FROM temp_orders;
