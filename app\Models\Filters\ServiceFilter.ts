import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Service from '../Service'

export default class ServiceFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Service, Service>

  public active(value: boolean): void {
    this.$query.where('active', value)
  }

  public task(value: string): void {
    this.$query.where('taskId', value)
  }

  public with(relations: string) {
    relations
      .split(',')
      .map(
        (relation: 'vendorTypes' | 'vendorCategories' | 'productTypes' | 'productCategories') => {
          this.$query.preload(relation)
        }
      )
  }

  public vendor(vendorId: string) {
    this.$query.whereHas('vendors', (vq) => {
      vq.where('id', [vendorId])
    })
  }
}
