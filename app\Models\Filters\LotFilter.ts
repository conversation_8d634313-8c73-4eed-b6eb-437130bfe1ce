import { BaseModelFilter } from '@ioc:Adonis/Addons/LucidFilter'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import Lot from '../Lot'

export default class LotFilter extends BaseModelFilter {
  public $query: ModelQueryBuilderContract<typeof Lot, Lot>

  public active(value: boolean): void {
    this.$query.where('active', value)
  }

  // public method (value: any): void {
  //   this.$query.where('name', value)
  // }
}
