import { useState, useEffect, useCallback } from 'react'
import { OrderItem, StaffMember, DepartmentStats } from '@/components/DepartmentWorkloadDashboard'

interface UseDepartmentWorkloadReturn {
  pendingItems: OrderItem[]
  preparingItems: OrderItem[]
  readyItems: OrderItem[]
  staffMembers: StaffMember[]
  departmentStats: DepartmentStats
  loading: boolean
  error: string | null
  updateItemStatus: (itemId: number, status: string, metadata?: Record<string, any>) => Promise<void>
  assignItemToStaff: (itemId: number, staffId: string) => Promise<void>
  refreshData: () => Promise<void>
}

interface ApiResponse<T> {
  data: T
  status: string
  message?: string
}

export const useDepartmentWorkload = (
  departmentId: string,
  refreshInterval: number = 30000
): UseDepartmentWorkloadReturn => {
  const [pendingItems, setPendingItems] = useState<OrderItem[]>([])
  const [preparingItems, setPreparingItems] = useState<OrderItem[]>([])
  const [readyItems, setReadyItems] = useState<OrderItem[]>([])
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([])
  const [departmentStats, setDepartmentStats] = useState<DepartmentStats>({
    total_items: 0,
    pending_items: 0,
    preparing_items: 0,
    ready_items: 0,
    overdue_items: 0,
    average_preparation_time: 0,
    completion_rate: 0,
    capacity_utilization: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // API base URL - this would come from environment config
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333/api/v1'

  // Get auth token - this would come from your auth context
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('auth_token') || ''
  }, [])

  // Generic API call function
  const apiCall = useCallback(async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const token = getAuthToken()
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      },
      ...options
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Network error' }))
      throw new Error(errorData.message || `HTTP ${response.status}`)
    }

    return response.json()
  }, [API_BASE_URL, getAuthToken])

  // Fetch department workload data
  const fetchDepartmentData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch all data in parallel
      const [
        pendingResponse,
        preparingResponse,
        readyResponse,
        staffResponse,
        statsResponse
      ] = await Promise.all([
        apiCall<ApiResponse<OrderItem[]>>(`/departments/${departmentId}/pending-items`),
        apiCall<ApiResponse<OrderItem[]>>(`/departments/${departmentId}/preparing-items`),
        apiCall<ApiResponse<OrderItem[]>>(`/departments/${departmentId}/ready-items`),
        apiCall<ApiResponse<StaffMember[]>>(`/departments/${departmentId}/staff`),
        apiCall<ApiResponse<DepartmentStats>>(`/departments/${departmentId}/statistics`)
      ])

      // Update state with fetched data
      setPendingItems(pendingResponse.data || [])
      setPreparingItems(preparingResponse.data || [])
      setReadyItems(readyResponse.data || [])
      setStaffMembers(staffResponse.data || [])
      setDepartmentStats(statsResponse.data || departmentStats)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch department data'
      setError(errorMessage)
      console.error('Error fetching department data:', err)
    } finally {
      setLoading(false)
    }
  }, [departmentId, apiCall, departmentStats])

  // Update item status
  const updateItemStatus = useCallback(async (
    itemId: number,
    status: string,
    metadata: Record<string, any> = {}
  ) => {
    try {
      await apiCall(`/order-items/${itemId}/status`, {
        method: 'PATCH',
        body: JSON.stringify({
          status,
          metadata: {
            ...metadata,
            updated_via: 'dashboard',
            timestamp: new Date().toISOString()
          }
        })
      })

      // Optimistically update local state
      const updateItemInList = (items: OrderItem[]) =>
        items.map(item => item.id === itemId ? { ...item, status: status as any } : item)

      setPendingItems(prev => updateItemInList(prev))
      setPreparingItems(prev => updateItemInList(prev))
      setReadyItems(prev => updateItemInList(prev))

      // Move item between lists based on new status
      const moveItem = (fromList: OrderItem[], toSetter: React.Dispatch<React.SetStateAction<OrderItem[]>>) => {
        const item = fromList.find(item => item.id === itemId)
        if (item) {
          const updatedItem = { ...item, status: status as any }
          toSetter(prev => [...prev, updatedItem])
          return fromList.filter(item => item.id !== itemId)
        }
        return fromList
      }

      if (status === 'preparing') {
        setPendingItems(prev => moveItem(prev, setPreparingItems))
      } else if (status === 'ready') {
        setPreparingItems(prev => moveItem(prev, setReadyItems))
      } else if (status === 'pending') {
        setPreparingItems(prev => moveItem(prev, setPendingItems))
        setReadyItems(prev => moveItem(prev, setPendingItems))
      }

      // Refresh data to ensure consistency
      setTimeout(fetchDepartmentData, 1000)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update item status'
      setError(errorMessage)
      console.error('Error updating item status:', err)
      throw err
    }
  }, [apiCall, fetchDepartmentData])

  // Assign item to staff
  const assignItemToStaff = useCallback(async (itemId: number, staffId: string) => {
    try {
      await apiCall(`/order-items/${itemId}/assign`, {
        method: 'PATCH',
        body: JSON.stringify({
          assigned_staff_id: staffId,
          metadata: {
            assigned_via: 'dashboard',
            timestamp: new Date().toISOString()
          }
        })
      })

      // Optimistically update local state
      const updateItemAssignment = (items: OrderItem[]) =>
        items.map(item => {
          if (item.id === itemId) {
            const staff = staffMembers.find(s => s.id === staffId)
            return {
              ...item,
              assigned_staff_id: staffId,
              assigned_staff_name: staff?.name || 'Unknown'
            }
          }
          return item
        })

      setPendingItems(prev => updateItemAssignment(prev))
      setPreparingItems(prev => updateItemAssignment(prev))
      setReadyItems(prev => updateItemAssignment(prev))

      // Update staff workload
      setStaffMembers(prev => prev.map(staff => {
        if (staff.id === staffId) {
          return {
            ...staff,
            current_workload: staff.current_workload + 1,
            active_items: staff.active_items + 1
          }
        }
        return staff
      }))

      // Refresh data to ensure consistency
      setTimeout(fetchDepartmentData, 1000)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to assign item to staff'
      setError(errorMessage)
      console.error('Error assigning item to staff:', err)
      throw err
    }
  }, [apiCall, staffMembers, fetchDepartmentData])

  // Refresh data manually
  const refreshData = useCallback(async () => {
    await fetchDepartmentData()
  }, [fetchDepartmentData])

  // Initial data fetch
  useEffect(() => {
    fetchDepartmentData()
  }, [fetchDepartmentData])

  // Set up automatic refresh interval
  useEffect(() => {
    if (refreshInterval > 0) {
      const interval = setInterval(fetchDepartmentData, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [fetchDepartmentData, refreshInterval])

  // Handle real-time updates via WebSocket
  useEffect(() => {
    const handleWebSocketMessage = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data)
        
        if (data.type === 'item_status_update' && data.department_id === departmentId) {
          // Update item status in real-time
          const { item_id, status, assigned_staff_id, assigned_staff_name } = data
          
          const updateItem = (items: OrderItem[]) =>
            items.map(item => {
              if (item.id === item_id) {
                return {
                  ...item,
                  status,
                  assigned_staff_id,
                  assigned_staff_name
                }
              }
              return item
            })

          setPendingItems(prev => updateItem(prev))
          setPreparingItems(prev => updateItem(prev))
          setReadyItems(prev => updateItem(prev))
        }

        if (data.type === 'department_workload_update' && data.department_id === departmentId) {
          // Update department statistics in real-time
          setDepartmentStats(prev => ({
            ...prev,
            ...data.workload_data
          }))
        }

        if (data.type === 'staff_assignment' && data.department_id === departmentId) {
          // Update staff assignments in real-time
          const { staff_id, workload_change } = data
          setStaffMembers(prev => prev.map(staff => {
            if (staff.id === staff_id) {
              return {
                ...staff,
                current_workload: Math.max(0, staff.current_workload + workload_change),
                active_items: Math.max(0, staff.active_items + workload_change)
              }
            }
            return staff
          }))
        }

      } catch (error) {
        console.error('Error processing WebSocket message:', error)
      }
    }

    // This would be connected to your WebSocket instance
    // window.addEventListener('websocket-message', handleWebSocketMessage)
    
    return () => {
      // window.removeEventListener('websocket-message', handleWebSocketMessage)
    }
  }, [departmentId])

  return {
    pendingItems,
    preparingItems,
    readyItems,
    staffMembers,
    departmentStats,
    loading,
    error,
    updateItemStatus,
    assignItemToStaff,
    refreshData
  }
}

export default useDepartmentWorkload
