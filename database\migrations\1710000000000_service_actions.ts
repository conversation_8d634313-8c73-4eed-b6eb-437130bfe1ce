import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'service_actions'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.string('id').primary()
      table.string('service_id').references('id').inTable('services').onDelete('CASCADE')
      table.enum('action', [
        'Purchase',
        'Booking',
        'Registration',
        'Access',
        'Process',
        'Reserve',
        'Application',
        'Query',
        'Reservation',
        'Prequalification'
      ]).notNullable()
      table.json('config').nullable() // For action-specific configuration
      table.boolean('is_active').defaultTo(true)
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      // Ensure unique combination of service and action
      table.unique(['service_id', 'action'])
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 