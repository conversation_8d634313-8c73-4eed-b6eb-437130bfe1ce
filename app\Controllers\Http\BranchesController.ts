import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Branch from '../../Models/Branch'
import { bind } from '@adonisjs/route-model-binding'
import Database from '@ioc:Adonis/Lucid/Database'

/**
 * @name Branch management
 * @version 1.0.0
 * @description Branch management for the application
 */
export default class BranchesController {
  /**
   * @index
   * @summary List all branches
   * @description List all branches, paginated
   * @paramUse (filterable)
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page Number
   * @paramQuery order - Order by field
   * @paramQuery sort order - (asc, desc)
   */
  public async index({ request }: HttpContextContract) {
    const {
      per = 10,
      page = 1,
      near = '0 0',
      radius = 15000,
      order = 'createdAt',
      sort = 'asc',
      ...filters
    } = request.qs()
    const branchQuery = Branch.filter(filters).preload('vendor').preload('sections')

    if (near !== '0 0') {
      const st = Database.st()
      branchQuery.where(
        st.distanceSphere('geom', st.geomFromText(`POINT(${near})`, 4326)),
        '<=',
        radius
      )
    }

    return await branchQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a branch
   * @description Create a branch with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Branch>
   */
  public async store({ request, response }: HttpContextContract) {
    const { vendorId, name, details, location, phone: postedPhone } = request.all()
    const phone = postedPhone.replace(/^\+/, '')
    const branch = new Branch()

    branch.fill({ vendorId, name, details, location, phone })

    const image = request.file('image')
    if (image) {
      branch.merge({ image: Attachment.fromFile(image) })
    }

    await branch.save()

    return response.json(branch)
  }

  @bind()
  /**
   * @show
   * @summary Show a single branch
   * @description Show a branch with their details (name and details)
   * @paramPath id required number - Branch ID
   * @responseBody 200 - <Branch>
   * @response 404 - Branch not found
   */
  public async show({ response }: HttpContextContract, branch: Branch) {
    await branch.load('staff')
    await branch.load('vendor')
    await branch.load('settings')

    await branch.loadCount('sections')
    branch.sectionCount = branch.$extras.sections_count

    await branch.loadCount('orders')
    branch.orderCount = branch.$extras.orders_count

    await branch.load('products', (_query) => {
      // query.groupBy('product_category_id')
    })

    return response.json(branch)
  }

  @bind()
  /**
   * @update
   * @summary Update a branch
   * @description Update a branch with their details (name and details)
   * @paramPath id required number - Branch ID
   * @requestBody <Branch>
   * @responseBody 200 - <Branch>
   * @response 404 - Branch not found
   */
  public async update({ request, response }: HttpContextContract, branch: Branch) {
    const payload = request.all()
    const image = request.file('image')

    await branch.merge(payload).save()

    if (image) {
      await branch.merge({ image: Attachment.fromFile(image) }).save()
    }

    return response.json(branch)
  }

  @bind()
  /**
   * @destroy
   * @responseBody 204 - No content
   */
  public async destroy({ response }: HttpContextContract, branch: Branch) {
    await branch.delete()

    return response.noContent()
  }
}
