import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import ProductTemplate from '../../Models/ProductTemplate'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name productTemplate management
 * @version 1.0.0
 * @description productTemplate management for the application
 */
export default class ProductTemplatesController {
  /**
   * @index
   * @summary List all productTemplates
   * @description List all productTemplates, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 30, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const templateQuery = ProductTemplate.filter(filters)
      .preload('vendor')
      .preload('branch')
      .preload('service')

    return await templateQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a productTemplate
   * @description Create a productTemplate with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <productTemplate>
   */
  public async store({ request, response, auth }: HttpContextContract) {
    const {
      name,
      ref,
      details,
      price,
      discounted,
      stock = -1,
      active = true,
      featured = false,
      type = 'Digital',
      condition = 'New',
      status = 'Draft',
      availability = 'In Stock',
      shipping = 'Free',
      unit = 'other',
      mode = 'Single',
      payment = 'Prepaid',
      visibility = 'Public',
      productCategoryId,
      vendorId,
      branchId,
      serviceId,
      meta = {},
      extra = {},
      tagIds = [],
      accompaniments = {},
      upsells = {},
    } = request.all()

    const productTemplate = await ProductTemplate.create({
      name,
      ref,
      details,
      price,
      discounted,
      stock,
      active,
      featured,
      type,
      condition,
      status,
      availability,
      shipping,
      unit,
      mode,
      visibility,
      payment,
      productCategoryId,
      userId: auth.user?.id,
      vendorId,
      branchId,
      serviceId,
      meta,
      extra,
    })

    const image = request.file('image')
    if (image) {
      await productTemplate.merge({ image: Attachment.fromFile(image) }).save()
    }

    // const gallery = request.files('gallery')
    // if (gallery.length > 0) {
    //   await productTemplate.related('gallery').createMany(gallery.map((file) => Attachment.fromFile(file)))
    // }

    await productTemplate.related('tags').sync(tagIds)

    Object.entries(accompaniments).forEach(([productTemplateId, value]) => {
      productTemplate.related('accompaniments').attach({
        [productTemplateId]: {
          price: (value as any).price,
        },
      })
    })

    Object.entries(upsells).forEach(([productTemplateId, value]) => {
      productTemplate.related('upsells').attach({
        [productTemplateId]: {
          price: (value as any).price,
        },
      })
    })

    return response.json(productTemplate)
  }

  @bind()
  /**
   * @show
   * @summary Show a single productTemplate
   * @description Show a productTemplate with their details (name and details)
   * @paramPath id required number - productTemplate ID
   * @responseBody 200 - <productTemplate>
   * @response 404 - productTemplate not found
   */
  public async show({ response }: HttpContextContract, productTemplate: ProductTemplate) {
    await productTemplate.load('vendor')
    await productTemplate.load('branch')
    await productTemplate.load('forms')
    await productTemplate.load('tags')
    await productTemplate.load('accompaniments')
    await productTemplate.load('upsells')
    await productTemplate.load('category', (cq) => cq.preload('productType'))
    await productTemplate.load('service', (sq) => sq.preload('task'))

    return response.json(productTemplate)
  }

  @bind()
  /**
   * @update
   * @summary Update a productTemplate
   * @description Update a productTemplate with their details (name and details)
   * @paramPath id required number - productTemplate ID
   * @requestBody <productTemplate>
   * @responseBody 200 - <productTemplate>
   * @response 404 - productTemplate not found
   */
  public async update(
    { request, response }: HttpContextContract,
    productTemplate: ProductTemplate
  ) {
    const input = request.all()

    console.info(input)

    const image = request.file('image')
    const gallery = request.files('gallery')

    delete input.image
    delete input.gallery

    await productTemplate.merge(input).save()

    if (image) {
      await productTemplate.merge({ image: Attachment.fromFile(image) }).save()
    }

    if (gallery.length > 0) {
      await productTemplate
        .related('gallery')
        .createMany(gallery.map((file) => Attachment.fromFile(file)))
    }

    await productTemplate.related('tags').sync(input.tagIds)

    await productTemplate.related('accompaniments').sync(
      Object.keys(input.accompaniments).reduce(
        (acc, itemId) => ({
          ...acc,
          [itemId]: input.accompaniments[itemId],
        }),
        {}
      )
    )

    await productTemplate
      .related('upsells')
      .sync(
        Object.keys(input.upsells).reduce(
          (acc, itemId) => ({ ...acc, [itemId]: input.upsells[itemId] }),
          {}
        )
      )

    return response.json(productTemplate)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a productTemplate
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, productTemplate: ProductTemplate) {
    return await productTemplate.delete()
  }
}
