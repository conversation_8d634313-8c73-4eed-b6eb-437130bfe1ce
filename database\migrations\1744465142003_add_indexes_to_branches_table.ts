import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'branches'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add individual indexes
      table.index('vendor_id')
      table.index('created_at')

      // Add spatial index using GIST
      table.index('geom', undefined, 'gist')
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove individual indexes
      table.dropIndex('vendor_id')
      table.dropIndex('created_at')

      // Remove spatial index
      table.dropIndex('geom')
    })
  }
} 