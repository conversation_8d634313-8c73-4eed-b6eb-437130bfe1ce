import { EventsList } from '@ioc:Adonis/Core/Event'
import User from 'App/Models/User'
import Booking from 'App/Models/Booking'
import BookingConfirmationNotification from 'App/Notifications/Booking/BookingConfirmationNotification'
import BookingCancelledNotification from 'App/Notifications/Booking/BookingCancelledNotification'
import VendorNewBookingNotification from 'App/Notifications/Booking/VendorNewBookingNotification'
import { Queue } from '@ioc:Rlanz/Queue'
import RealTimeNotificationService from 'App/Services/RealTimeNotificationService'

/**
 * Event listener for booking-related events
 */
export default class BookingNotificationListener {

  /**
   * Handle booking created event
   */
  public async onBookingCreated(data: { booking: Booking }) {
    const { booking } = data

    try {
      // Load necessary relationships
      await booking.load('customer')
      await booking.load('product')
      await booking.load('vendor')
      await booking.load('branch')

      // Send confirmation notification to customer
      if (booking.customer) {
        await booking.customer.notify(new BookingConfirmationNotification(booking, 'created'))
      }

      // Send new booking alert to vendor staff
      await this.notifyVendorStaff(booking, 'new_booking')

      // Send real-time notification
      await RealTimeNotificationService.notifyBookingCreated(
        booking.customerId,
        booking.vendorId,
        {
          booking_id: booking.id,
          confirmation_code: booking.confirmationCode,
          service_name: booking.product?.name,
          appointment_date: booking.appointmentStart.toFormat('yyyy-MM-dd'),
          appointment_time: booking.appointmentStart.toFormat('HH:mm'),
          customer_name: booking.customer?.name,
          total_price: booking.totalPrice
        }
      )

      console.log(`✅ Booking created notifications sent for booking ${booking.id}`)

    } catch (error) {
      console.error(`❌ Failed to send booking created notifications for booking ${booking.id}:`, error.message)
    }
  }

  /**
   * Handle booking confirmed event
   */
  public async onBookingConfirmed(data: { booking: Booking }) {
    const { booking } = data

    try {
      // Load necessary relationships
      await booking.load('customer')
      await booking.load('product')
      await booking.load('vendor')
      await booking.load('branch')

      // Send confirmation notification to customer
      if (booking.customer) {
        await booking.customer.notify(new BookingConfirmationNotification(booking, 'confirmed'))
      }

      // Send confirmation alert to vendor staff
      await this.notifyVendorStaff(booking, 'booking_confirmed')

      // Schedule reminder notifications
      await this.scheduleReminderNotifications(booking)

      // Send real-time notification
      await RealTimeNotificationService.notifyBookingConfirmed(
        booking.customerId,
        booking.vendorId,
        {
          booking_id: booking.id,
          confirmation_code: booking.confirmationCode,
          service_name: booking.product?.name,
          appointment_date: booking.appointmentStart.toFormat('yyyy-MM-dd'),
          appointment_time: booking.appointmentStart.toFormat('HH:mm'),
          customer_name: booking.customer?.name
        }
      )

      console.log(`✅ Booking confirmed notifications sent for booking ${booking.id}`)

    } catch (error) {
      console.error(`❌ Failed to send booking confirmed notifications for booking ${booking.id}:`, error.message)
    }
  }

  /**
   * Handle booking cancelled event
   */
  public async onBookingCancelled(data: { 
    booking: Booking, 
    cancelledBy: string, 
    reason?: string 
  }) {
    const { booking, cancelledBy, reason } = data

    try {
      // Load necessary relationships
      await booking.load('customer')
      await booking.load('product')
      await booking.load('vendor')
      await booking.load('branch')

      // Determine who cancelled the booking
      let cancelledByType: 'customer' | 'vendor' | 'system' = 'customer'
      if (cancelledBy === 'system') {
        cancelledByType = 'system'
      } else if (booking.vendor && (cancelledBy === booking.vendorId || booking.vendor.id === cancelledBy)) {
        cancelledByType = 'vendor'
      }

      // Send cancellation notification to customer
      if (booking.customer) {
        await booking.customer.notify(new BookingCancelledNotification(booking, cancelledByType, reason))
      }

      // Send cancellation alert to vendor staff (if not cancelled by vendor)
      if (cancelledByType !== 'vendor') {
        await this.notifyVendorStaff(booking, 'booking_cancelled', { 
          cancelled_by: cancelledByType,
          cancellation_reason: reason 
        })
      }

      // Cancel scheduled reminder notifications
      await this.cancelScheduledReminders(booking)

      // Send real-time notification
      await RealTimeNotificationService.notifyBookingCancelled(
        booking.customerId,
        booking.vendorId,
        {
          booking_id: booking.id,
          confirmation_code: booking.confirmationCode,
          service_name: booking.product?.name,
          appointment_date: booking.appointmentStart.toFormat('yyyy-MM-dd'),
          appointment_time: booking.appointmentStart.toFormat('HH:mm'),
          customer_name: booking.customer?.name,
          cancelled_by: cancelledByType,
          cancellation_reason: reason
        }
      )

      console.log(`✅ Booking cancelled notifications sent for booking ${booking.id}`)

    } catch (error) {
      console.error(`❌ Failed to send booking cancelled notifications for booking ${booking.id}:`, error.message)
    }
  }

  /**
   * Handle booking modified event
   */
  public async onBookingModified(data: { 
    booking: Booking, 
    previousData: any,
    modifiedBy: string 
  }) {
    const { booking, previousData, modifiedBy } = data

    try {
      // Load necessary relationships
      await booking.load('customer')
      await booking.load('product')
      await booking.load('vendor')
      await booking.load('branch')

      // Send modification notification to customer
      if (booking.customer) {
        // Note: We would need to create a BookingModifiedNotification class
        console.log(`📝 Booking modified notification needed for booking ${booking.id}`)
      }

      // Reschedule reminder notifications if appointment time changed
      if (previousData.appointmentStart !== booking.appointmentStart.toISO()) {
        await this.cancelScheduledReminders(booking)
        await this.scheduleReminderNotifications(booking)
      }

      // Send real-time notification
      await RealTimeNotificationService.notifyBookingModified(
        booking.customerId,
        booking.vendorId,
        {
          booking_id: booking.id,
          confirmation_code: booking.confirmationCode,
          service_name: booking.product?.name,
          new_appointment_date: booking.appointmentStart.toFormat('yyyy-MM-dd'),
          new_appointment_time: booking.appointmentStart.toFormat('HH:mm'),
          previous_appointment_date: previousData.appointmentStart,
          customer_name: booking.customer?.name,
          modified_by: modifiedBy
        }
      )

      console.log(`✅ Booking modified notifications sent for booking ${booking.id}`)

    } catch (error) {
      console.error(`❌ Failed to send booking modified notifications for booking ${booking.id}:`, error.message)
    }
  }

  /**
   * Schedule reminder notifications for a confirmed booking
   */
  private async scheduleReminderNotifications(booking: Booking): Promise<void> {
    try {
      const now = new Date()
      const appointmentTime = booking.appointmentStart.toJSDate()

      // Schedule 24-hour reminder
      const reminder24h = new Date(appointmentTime.getTime() - (24 * 60 * 60 * 1000))
      if (reminder24h > now) {
        await Queue.dispatch('App/Jobs/SendBookingReminder', {
          bookingId: booking.id,
          reminderType: '24_hour'
        }, {
          delay: reminder24h.getTime() - now.getTime(),
          jobId: `booking_reminder_24h_${booking.id}`
        })
      }

      // Schedule 2-hour reminder
      const reminder2h = new Date(appointmentTime.getTime() - (2 * 60 * 60 * 1000))
      if (reminder2h > now) {
        await Queue.dispatch('App/Jobs/SendBookingReminder', {
          bookingId: booking.id,
          reminderType: '2_hour'
        }, {
          delay: reminder2h.getTime() - now.getTime(),
          jobId: `booking_reminder_2h_${booking.id}`
        })
      }

      // Schedule 30-minute reminder
      const reminder30min = new Date(appointmentTime.getTime() - (30 * 60 * 1000))
      if (reminder30min > now) {
        await Queue.dispatch('App/Jobs/SendBookingReminder', {
          bookingId: booking.id,
          reminderType: '30_minute'
        }, {
          delay: reminder30min.getTime() - now.getTime(),
          jobId: `booking_reminder_30min_${booking.id}`
        })
      }

      console.log(`📅 Reminder notifications scheduled for booking ${booking.id}`)

    } catch (error) {
      console.error(`❌ Failed to schedule reminders for booking ${booking.id}:`, error.message)
    }
  }

  /**
   * Cancel scheduled reminder notifications
   */
  private async cancelScheduledReminders(booking: Booking): Promise<void> {
    try {
      // Cancel scheduled reminder jobs
      const reminderJobIds = [
        `booking_reminder_24h_${booking.id}`,
        `booking_reminder_2h_${booking.id}`,
        `booking_reminder_30min_${booking.id}`
      ]

      for (const jobId of reminderJobIds) {
        try {
          // Note: This would need to be implemented based on your queue system
          // await Queue.cancel(jobId)
          console.log(`🗑️ Cancelled reminder job: ${jobId}`)
        } catch (error) {
          console.warn(`⚠️ Could not cancel reminder job ${jobId}:`, error.message)
        }
      }

    } catch (error) {
      console.error(`❌ Failed to cancel reminders for booking ${booking.id}:`, error.message)
    }
  }

  /**
   * Notify vendor staff about booking events
   */
  private async notifyVendorStaff(
    booking: Booking, 
    notificationType: 'new_booking' | 'booking_confirmed' | 'booking_cancelled',
    additionalData?: any
  ): Promise<void> {
    try {
      // Get vendor staff (managers, staff with booking permissions)
      const vendorStaff = await User.query()
        .whereHas('employers', (query) => {
          query.where('vendor_id', booking.vendorId)
          query.where('branch_id', booking.branchId)
          query.whereIn('role', ['manager', 'staff', 'admin'])
        })
        .where('active', true)

      // Send notifications to each staff member
      for (const staff of vendorStaff) {
        if (notificationType === 'new_booking') {
          await staff.notify(new VendorNewBookingNotification(booking, 'new_booking'))
        } else if (notificationType === 'booking_confirmed') {
          await staff.notify(new VendorNewBookingNotification(booking, 'booking_confirmed'))
        } else if (notificationType === 'booking_cancelled') {
          // Note: We might want a separate VendorBookingCancelledNotification
          console.log(`📧 Vendor cancellation notification needed for staff ${staff.id}`)
        }
      }

      console.log(`📧 Vendor staff notifications sent for booking ${booking.id}`)

    } catch (error) {
      console.error(`❌ Failed to notify vendor staff for booking ${booking.id}:`, error.message)
    }
  }
}
