this payload involves something that was stored create by temp-orders
{
    "id": "01jy1mdrd2vatw9vt0zavch0y2",
    "vendorId": "01js3yagzh23spmwf6xfxnm18z",
    "branchId": "01js3yagzj039pwm0z7w49e6fr",
    "sectionId": "01jv723cfvdvswxkst6s98t8r2",
    "lotId": "01jwzx27ejw31qjybprdcx6thv",
    "userId": "01jrsz9r0bgwm5vnzwhgc87em3",
    "staffId": "01jxmc3xcy6gp50a59ke0zv28m",
    "action": "Purchase",
    "type": "Instant",
    "delivery": "Dinein",
    "status": "Pending",
    "meta": {
        "lotId": "01jwzx27ejw31qjybprdcx6thv",
        "charges": {
            "Tip": 0,
            "Test Configuration": 135
        },
        "pricing": {
            "total": 135,
            "currency": "KES",
            "subtotal": 0,
            "chargesTotal": 135,
            "invoiceAmount": 135,
            "modifiersTotal": 0
        },
        "platform": "react-web",
        "sectionId": "01jv723cfvdvswxkst6s98t8r2",
        "timestamp": "2025-06-18T13:42:28.615Z",
        "guestCount": 1,
        "temp_items": {
            "01jwbj4px4gzar64zdjtseyn5f": {
                "quantity": 1
            }
        },
        "orderSource": "web-app",
        "tableNumber": "Bar 2",
        "estimatedTime": "10-15 minutes",
        "fulfillmentType": "dine-in",
        "paymentRequired": false,
        "waiterCommitment": true
    },
    "ref": null,
    "acceptedAt": "2025-06-18T13:42:30.050+00:00",
    "startAt": null,
    "endAt": null,
    "createdAt": "2025-06-18T13:42:30.050+00:00",
    "updatedAt": "2025-06-18T13:42:30.050+00:00",
    "orderNumber": null,
    "deliveryVendorId": null,
    "deliveryBranchId": null,
    "deliveryStatus": "pending_assignment",
    "deliveryLocation": null,
    "pickupLocation": null,
    "deliveryDistance": null,
    "deliveryFee": null,
    "estimatedDeliveryTime": null,
    "actualDeliveryTime": null,
    "pickupTime": null,
    "deliveryNotes": null,
    "trackingData": null,
    "trackingCode": null,
    "requiresSignature": false,
    "signatureImage": null,
    "deliveryMeta": null,
    "customer": {
        "id": "01jrsz9r0bgwm5vnzwhgc87em3",
        "idpass": null,
        "title": null,
        "firstName": "Pa",
        "lastName": "Salieu ",
        "gender": null,
        "dob": null,
        "otp": "236089",
        "email": "<EMAIL>",
        "phone": "254702260557",
        "rememberMeToken": null,
        "details": null,
        "location": null,
        "geom": null,
        "avatar": null,
        "meta": {
            "username": null,
            "online": false
        },
        "status": "Active",
        "emailVerifiedAt": "2025-06-06T14:44:57.107+00:00",
        "phoneVerifiedAt": null,
        "createdAt": "2025-04-14T11:00:08.381+00:00",
        "updatedAt": "2025-06-16T12:01:47.939+00:00",
        "deletedAt": null,
        "name": "Pa Salieu ",
        "avatarUrl": "",
        "initials": "PS",
        "primaryDepartment": null,
        "activeDepartments": [],
        "averageSkillLevel": 0,
        "canSupervise": false
    },
    "branch": {
        "id": "01js3yagzj039pwm0z7w49e6fr",
        "vendorId": "01js3yagzh23spmwf6xfxnm18z",
        "name": "O'Sinkirri Kilimani",
        "details": "",
        "email": "<EMAIL>",
        "phone": "4708087047",
        "image": {
            "url": "",
            "name": "branches/cmc1or7mb02za0go724ni7m6q.jpg",
            "extname": "jpg",
            "size": 175844,
            "mimeType": "image/jpeg"
        },
        "location": {
            "name": "O'Sinkirri Kilimani",
            "address": "Ring Road Kilimani, Off Lenana Road, Nairobi",
            "place_id": null,
            "coordinates": {
                "lat": "-1.2952",
                "lng": "36.7868"
            },
            "regions": {
                "country": "Kenya"
            }
        },
        "geom": "0101000020E6100000143FC6DCB56442409A779CA223B9F4BF",
        "hours": null,
        "createdAt": "2025-04-18T07:55:29.650+00:00",
        "updatedAt": "2025-06-20T13:07:16.920+00:00",
        "code": null
    },
    "vendor": {
        "id": "01js3yagzh23spmwf6xfxnm18z",
        "userId": "01js3yagycbxfjxt06pj5tp8g4",
        "serviceId": "01jrsjk1hj25wabb8ap26g57qa",
        "name": "O'Sinkirri Bar & Restaurant",
        "slug": "osinkirri-bar-and-restaurant-VQ561E",
        "details": "",
        "email": "<EMAIL>",
        "phone": "254708087047",
        "reg": "OSK-REG-001",
        "permit": "OSK-PERMIT-001",
        "kra": "KRA76997210",
        "logo": {
            "url": "",
            "extname": "jpeg",
            "size": 4869,
            "mimeType": "image/jpeg"
        },
        "cover": {
            "url": "",
            "extname": "jpeg",
            "size": 470765,
            "mimeType": "image/jpeg"
        },
        "active": true,
        "featured": true,
        "createdAt": "2025-04-18T07:55:29.649+00:00",
        "updatedAt": "2025-06-18T08:23:19.577+00:00",
        "deletedAt": null,
        "deliveryPreferences": null,
        "availabilitySettings": null,
        "pricingStructure": null,
        "verificationStatus": "pending",
        "verificationNotes": null,
        "verifiedAt": null
    },
    "items": [
        {
            "id": null,
            "orderId": "01jy1mdrd2vatw9vt0zavch0y2",
            "productId": "01jwbj4px4gzar64zdjtseyn5f",
            "quantity": 1,
            "meta": null,
            "createdAt": "2025-06-18T13:42:30.050+00:00",
            "updatedAt": "2025-06-18T13:42:30.050+00:00",
            "price": null,
            "status": "pending",
            "departmentId": null,
            "assignedStaffId": null,
            "estimatedPreparationTime": null,
            "preparationStartedAt": null,
            "preparationCompletedAt": null,
            "servedAt": null,
            "priorityLevel": 1,
            "requiresSpecialAttention": false,
            "specialInstructions": null,
            "preparationNotes": null,
            "statusHistory": null,
            "qualityCheckStatus": "not_required",
            "qualityCheckedBy": null,
            "customerModifications": null,
            "cancellationReason": null,
            "actualPreparationTime": null,
            "preparationAttempts": 1,
            "modifiers": [],
            "product": {
                "id": "01jwbj4px4gzar64zdjtseyn5f",
                "ref": null,
                "name": "Whole Tilapia",
                "details": "<p>Baked or fried, served crispy and full of flavor. Served with seasonal vegetables &amp; your choice of side.</p>",
                "price": 2150,
                "discounted": null,
                "stock": 10,
                "active": true,
                "featured": false,
                "type": "Digital",
                "condition": "New",
                "status": "Published",
                "availability": "In Stock",
                "shipping": "Paid",
                "unit": "unit",
                "mode": "Single",
                "payment": "Prepaid",
                "visibility": "Public",
                "productCategoryId": "01jrt0wnvy6f4wf7xkr0wqt3zf",
                "userId": "01jrsjk1m6bm8mt7ntj3cgjrby",
                "vendorId": "01js3yagzh23spmwf6xfxnm18z",
                "branchId": "01js3yagzj039pwm0z7w49e6fr",
                "serviceId": "01jrsjk1hj25wabb8ap26g57qa",
                "image": null,
                "meta": {
                    "sku": null
                },
                "extra": null,
                "expiresAt": "2025-05-28T13:43:37.124+00:00",
                "createdAt": "2025-05-28T13:43:37.124+00:00",
                "updatedAt": "2025-06-05T13:20:05.657+00:00",
                "hasFulfillmentSettings": false
            },
            "actualPreparationTimeMinutes": null,
            "isOverdue": false,
            "preparationProgress": 0,
            "statusDisplayName": "Pending",
            "canBeStarted": true,
            "canBeCompleted": false,
            "canBeServed": false,
            "requiresAttention": false,
            "estimatedCompletionTime": null,
            "allModifiersCompleted": true,
            "totalItemCost": 0
        }
    ],
    "staff": {
        "id": "01jxmc3xcy6gp50a59ke0zv28m",
        "idpass": "54111202895",
        "title": null,
        "firstName": "John",
        "lastName": "Doe",
        "gender": null,
        "dob": null,
        "otp": null,
        "email": "<EMAIL>",
        "phone": "54111202895",
        "rememberMeToken": null,
        "details": null,
        "location": null,
        "geom": null,
        "avatar": null,
        "meta": {
            "username": null,
            "online": false
        },
        "status": "Active",
        "emailVerifiedAt": null,
        "phoneVerifiedAt": null,
        "createdAt": "2025-06-13T10:07:11.298+00:00",
        "updatedAt": "2025-06-13T10:07:11.298+00:00",
        "deletedAt": null,
        "name": "John Doe",
        "avatarUrl": "",
        "initials": "JD",
        "primaryDepartment": null,
        "activeDepartments": [],
        "averageSkillLevel": 0,
        "canSupervise": false
    },
    "section": {
        "id": "01jv723cfvdvswxkst6s98t8r2",
        "branchId": "01js3yagzj039pwm0z7w49e6fr",
        "name": "Bar",
        "details": null,
        "active": true,
        "image": null,
        "createdAt": "2025-05-14T09:30:36.923+00:00",
        "updatedAt": "2025-05-14T09:30:36.923+00:00"
    },
    "invoices": [],
    "payments": [],
    "fulfillmentProgress": 0,
    "estimatedCompletionTime": null,
    "hasOverdueItems": false,
    "departmentBreakdown": {},
    "canBeCompleted": false,
    "requiresAttention": false,
    "totalPreparationTime": 0,
    "qrCode": "",
    "pricing": {
        "subtotal": 0,
        "modifiersTotal": 0,
        "chargesTotal": 135,
        "total": 135,
        "invoiceAmount": 0,
        "currency": "KES"
    },
    "fulfillment": {
        "totalItems": 0,
        "completedItems": 0,
        "pendingItems": 0,
        "preparingItems": 0,
        "readyItems": 0,
        "progress": 0,
        "estimatedCompletion": null,
        "departments": {},
        "requiresAttention": false
    }
}


white this was created by orders endpoint
{
    "id": "01jxvxkgh7jtb38t9b9v23vscd",
    "vendorId": "01js3yagzh23spmwf6xfxnm18z",
    "branchId": "01js3yagzj039pwm0z7w49e6fr",
    "sectionId": "01jv723cfvdvswxkst6s98t8r2",
    "lotId": "01jwzx1zkbp5jnwbmpdr5r18qs",
    "userId": "01jrssz8dr1s2rtppk5yr0yzdg",
    "staffId": "01jxmc3xcy6gp50a59ke0zv28m",
    "action": "Purchase",
    "type": "Instant",
    "delivery": "Dinein",
    "status": "Placed",
    "meta": {
        "status_history": [
            {
                "from": "Pending",
                "to": "Placed",
                "updated_by": "John Doe",
                "updated_at": "2025-06-19T11:49:32.701+00:00",
                "metadata": {
                    "auto_update_status": false,
                    "updated_via": "api"
                }
            }
        ],
        "pricing": {
            "subtotal": 15400,
            "modifiersTotal": 0,
            "chargesTotal": 220,
            "total": 15620,
            "invoiceAmount": 15620,
            "currency": "KES"
        }
    },
    "ref": null,
    "acceptedAt": "2025-06-19T11:49:31.293+00:00",
    "startAt": "2025-06-16T08:27:29.191+00:00",
    "endAt": "2025-06-16T08:27:29.191+00:00",
    "createdAt": "2025-06-16T08:27:29.191+00:00",
    "updatedAt": "2025-06-21T06:48:45.389+00:00",
    "orderNumber": "ORD-1750062449-190",
    "deliveryVendorId": null,
    "deliveryBranchId": null,
    "deliveryStatus": "pending_assignment",
    "deliveryLocation": null,
    "pickupLocation": null,
    "deliveryDistance": null,
    "deliveryFee": null,
    "estimatedDeliveryTime": null,
    "actualDeliveryTime": null,
    "pickupTime": null,
    "deliveryNotes": null,
    "trackingData": null,
    "trackingCode": null,
    "requiresSignature": false,
    "signatureImage": null,
    "deliveryMeta": null,
    "customer": {
        "id": "01jrssz8dr1s2rtppk5yr0yzdg",
        "idpass": null,
        "title": null,
        "firstName": "Clivon",
        "lastName": "Osire",
        "gender": null,
        "dob": null,
        "otp": "369142",
        "email": "<EMAIL>",
        "phone": "254757429010",
        "rememberMeToken": null,
        "details": null,
        "location": null,
        "geom": null,
        "avatar": null,
        "meta": {
            "username": null,
            "online": false
        },
        "status": "Active",
        "emailVerifiedAt": null,
        "phoneVerifiedAt": null,
        "createdAt": "2025-04-14T09:27:01.852+00:00",
        "updatedAt": "2025-06-13T08:49:31.815+00:00",
        "deletedAt": null,
        "name": "Clivon Osire",
        "avatarUrl": "https://www.gravatar.com/avatar/4fd5c6c4d1f13bd3efdc5352be1c862f?s=250&d=mp",
        "initials": "CO",
        "primaryDepartment": null,
        "activeDepartments": [],
        "averageSkillLevel": 0,
        "canSupervise": false
    },
    "branch": {
        "id": "01js3yagzj039pwm0z7w49e6fr",
        "vendorId": "01js3yagzh23spmwf6xfxnm18z",
        "name": "O'Sinkirri Kilimani",
        "details": "",
        "email": "<EMAIL>",
        "phone": "4708087047",
        "image": {
            "url": "",
            "name": "branches/cmc1or7mb02za0go724ni7m6q.jpg",
            "extname": "jpg",
            "size": 175844,
            "mimeType": "image/jpeg"
        },
        "location": {
            "name": "O'Sinkirri Kilimani",
            "address": "Ring Road Kilimani, Off Lenana Road, Nairobi",
            "place_id": null,
            "coordinates": {
                "lat": "-1.2952",
                "lng": "36.7868"
            },
            "regions": {
                "country": "Kenya"
            }
        },
        "geom": "0101000020E6100000143FC6DCB56442409A779CA223B9F4BF",
        "hours": null,
        "createdAt": "2025-04-18T07:55:29.650+00:00",
        "updatedAt": "2025-06-20T13:07:16.920+00:00",
        "code": null
    },
    "vendor": {
        "id": "01js3yagzh23spmwf6xfxnm18z",
        "userId": "01js3yagycbxfjxt06pj5tp8g4",
        "serviceId": "01jrsjk1hj25wabb8ap26g57qa",
        "name": "O'Sinkirri Bar & Restaurant",
        "slug": "osinkirri-bar-and-restaurant-VQ561E",
        "details": "",
        "email": "<EMAIL>",
        "phone": "254708087047",
        "reg": "OSK-REG-001",
        "permit": "OSK-PERMIT-001",
        "kra": "KRA76997210",
        "logo": {
            "url": "",
            "name": "logos/cm9pi8dxc01m97go7ewmw1suh.jpeg",
            "extname": "jpeg",
            "size": 4869,
            "mimeType": "image/jpeg"
        },
        "cover": {
            "url": "",
            "name": "covers/cmc1oq7i102xr0go7cn6o9n6t.jpeg",
            "extname": "jpeg",
            "size": 470765,
            "mimeType": "image/jpeg"
        },
        "active": true,
        "featured": true,
        "createdAt": "2025-04-18T07:55:29.649+00:00",
        "updatedAt": "2025-06-18T08:23:19.577+00:00",
        "deletedAt": null,
        "deliveryPreferences": null,
        "availabilitySettings": null,
        "pricingStructure": null,
        "verificationStatus": "pending",
        "verificationNotes": null,
        "verifiedAt": null
    },
    "items": [
        {
            "id": 170,
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "productId": "01jwbj4pydfse577fbg79gth39",
            "quantity": 1,
            "meta": null,
            "createdAt": "2025-06-16T08:27:29.199+00:00",
            "updatedAt": "2025-06-16T08:27:29.199+00:00",
            "price": "2200.00",
            "status": "pending",
            "departmentId": null,
            "assignedStaffId": null,
            "estimatedPreparationTime": null,
            "preparationStartedAt": null,
            "preparationCompletedAt": null,
            "servedAt": null,
            "priorityLevel": 1,
            "requiresSpecialAttention": false,
            "specialInstructions": null,
            "preparationNotes": null,
            "statusHistory": null,
            "qualityCheckStatus": "not_required",
            "qualityCheckedBy": null,
            "customerModifications": null,
            "cancellationReason": null,
            "actualPreparationTime": null,
            "preparationAttempts": 1,
            "modifiers": [],
            "assignedStaff": null,
            "qualityChecker": null,
            "department": null,
            "product": {
                "id": "01jwbj4pydfse577fbg79gth39",
                "ref": null,
                "name": "O’sinkirri Burger",
                "details": "Double patty with bold toppings. Served with fries and salad.",
                "price": 2200,
                "discounted": null,
                "stock": 10,
                "active": true,
                "featured": false,
                "type": "Digital",
                "condition": "New",
                "status": "Published",
                "availability": "In Stock",
                "shipping": "Paid",
                "unit": "unit",
                "mode": "Single",
                "payment": "Prepaid",
                "visibility": "Public",
                "productCategoryId": "01jrt0wnsvm5ef5nj6qxfg6dva",
                "userId": "01jrsjk1m6bm8mt7ntj3cgjrby",
                "vendorId": "01js3yagzh23spmwf6xfxnm18z",
                "branchId": "01js3yagzj039pwm0z7w49e6fr",
                "serviceId": "01jrsjk1hj25wabb8ap26g57qa",
                "image": null,
                "meta": {
                    "sku": null
                },
                "extra": null,
                "expiresAt": "2025-05-28T13:43:37.165+00:00",
                "createdAt": "2025-05-28T13:43:37.165+00:00",
                "updatedAt": "2025-05-28T13:43:37.165+00:00",
                "category": {
                    "id": "01jrt0wnsvm5ef5nj6qxfg6dva",
                    "name": "Burgers & Sandwiches",
                    "slug": "burgers-and-sandwiches",
                    "details": "null",
                    "image": null,
                    "productTypeId": "01jrt0vkd4aga5eqee5db74ch2",
                    "createdAt": "2025-04-14T11:27:57.243+00:00",
                    "updatedAt": "2025-04-14T11:35:06.138+00:00"
                },
                "gallery": [],
                "forms": [],
                "branch": {
                    "id": "01js3yagzj039pwm0z7w49e6fr",
                    "vendorId": "01js3yagzh23spmwf6xfxnm18z",
                    "name": "O'Sinkirri Kilimani",
                    "details": "",
                    "email": "<EMAIL>",
                    "phone": "4708087047",
                    "image": {
                        "url": "",
                        "name": "branches/cmc1or7mb02za0go724ni7m6q.jpg",
                        "extname": "jpg",
                        "size": 175844,
                        "mimeType": "image/jpeg"
                    },
                    "location": {
                        "name": "O'Sinkirri Kilimani",
                        "address": "Ring Road Kilimani, Off Lenana Road, Nairobi",
                        "place_id": null,
                        "coordinates": {
                            "lat": "-1.2952",
                            "lng": "36.7868"
                        },
                        "regions": {
                            "country": "Kenya"
                        }
                    },
                    "geom": "0101000020E6100000143FC6DCB56442409A779CA223B9F4BF",
                    "hours": null,
                    "createdAt": "2025-04-18T07:55:29.650+00:00",
                    "updatedAt": "2025-06-20T13:07:16.920+00:00",
                    "code": null
                },
                "vendor": {
                    "id": "01js3yagzh23spmwf6xfxnm18z",
                    "userId": "01js3yagycbxfjxt06pj5tp8g4",
                    "serviceId": "01jrsjk1hj25wabb8ap26g57qa",
                    "name": "O'Sinkirri Bar & Restaurant",
                    "slug": "osinkirri-bar-and-restaurant-VQ561E",
                    "details": "",
                    "email": "<EMAIL>",
                    "phone": "254708087047",
                    "reg": "OSK-REG-001",
                    "permit": "OSK-PERMIT-001",
                    "kra": "KRA76997210",
                    "logo": {
                        "url": "",
                        "name": "logos/cm9pi8dxc01m97go7ewmw1suh.jpeg",
                        "extname": "jpeg",
                        "size": 4869,
                        "mimeType": "image/jpeg"
                    },
                    "cover": {
                        "url": "",
                        "name": "covers/cmc1oq7i102xr0go7cn6o9n6t.jpeg",
                        "extname": "jpeg",
                        "size": 470765,
                        "mimeType": "image/jpeg"
                    },
                    "active": true,
                    "featured": true,
                    "createdAt": "2025-04-18T07:55:29.649+00:00",
                    "updatedAt": "2025-06-18T08:23:19.577+00:00",
                    "deletedAt": null,
                    "deliveryPreferences": null,
                    "availabilitySettings": null,
                    "pricingStructure": null,
                    "verificationStatus": "pending",
                    "verificationNotes": null,
                    "verifiedAt": null
                },
                "hasFulfillmentSettings": false
            },
            "actualPreparationTimeMinutes": null,
            "isOverdue": false,
            "preparationProgress": 0,
            "statusDisplayName": "Pending",
            "canBeStarted": true,
            "canBeCompleted": false,
            "canBeServed": false,
            "requiresAttention": false,
            "estimatedCompletionTime": null,
            "allModifiersCompleted": true,
            "totalItemCost": 2200
        }
    ],
    "staff": {
        "id": "01jxmc3xcy6gp50a59ke0zv28m",
        "idpass": "54111202895",
        "title": null,
        "firstName": "John",
        "lastName": "Doe",
        "gender": null,
        "dob": null,
        "otp": null,
        "email": "<EMAIL>",
        "phone": "54111202895",
        "rememberMeToken": null,
        "details": null,
        "location": null,
        "geom": null,
        "avatar": null,
        "meta": {
            "username": null,
            "online": false
        },
        "status": "Active",
        "emailVerifiedAt": null,
        "phoneVerifiedAt": null,
        "createdAt": "2025-06-13T10:07:11.298+00:00",
        "updatedAt": "2025-06-13T10:07:11.298+00:00",
        "deletedAt": null,
        "name": "John Doe",
        "avatarUrl": "",
        "initials": "JD",
        "primaryDepartment": null,
        "activeDepartments": [],
        "averageSkillLevel": 0,
        "canSupervise": false
    },
    "section": {
        "id": "01jv723cfvdvswxkst6s98t8r2",
        "branchId": "01js3yagzj039pwm0z7w49e6fr",
        "name": "Bar",
        "details": null,
        "active": true,
        "image": null,
        "createdAt": "2025-05-14T09:30:36.923+00:00",
        "updatedAt": "2025-05-14T09:30:36.923+00:00"
    },
    "invoices": [
        {
            "id": "01jy63egsze49a4x68vf268kcr",
            "number": "AIA-01JY63-NAPPN",
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "status": "Pending",
            "amount": 2200,
            "createdAt": "2025-06-20T07:22:01.407+00:00",
            "updatedAt": "2025-06-20T07:22:01.407+00:00"
        },
        {
            "id": "01jy63e52ep1egsh7zawngb0wr",
            "number": "AIA-01JY63-AYD9T",
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "status": "Pending",
            "amount": 2200,
            "createdAt": "2025-06-20T07:21:49.390+00:00",
            "updatedAt": "2025-06-20T07:21:49.390+00:00"
        },
        {
            "id": "01jy63bz1xjt6t92fbjq5ecvrz",
            "number": "AIA-01JY63-6TFBF",
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "status": "Pending",
            "amount": 2200,
            "createdAt": "2025-06-20T07:20:37.693+00:00",
            "updatedAt": "2025-06-20T07:20:37.693+00:00"
        },
        {
            "id": "01jy63aqsrb3d9xap7cs5zywf2",
            "number": "AIA-01JY63-X81F0",
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "status": "Pending",
            "amount": 2200,
            "createdAt": "2025-06-20T07:19:57.496+00:00",
            "updatedAt": "2025-06-20T07:19:57.496+00:00"
        },
        {
            "id": "01jy3yfa8kk1g4bz5w1hdv1gp5",
            "number": "AIA-01JY3Y-GXEDR",
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "status": "Pending",
            "amount": 2200,
            "createdAt": "2025-06-19T11:16:35.731+00:00",
            "updatedAt": "2025-06-19T11:16:35.731+00:00"
        },
        {
            "id": "01jxvxkghsbwdtzq9qxg9rr2mr",
            "number": "AIA-01JXVX-MJWFH",
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "status": "Pending",
            "amount": 2310,
            "createdAt": "2025-06-16T08:27:29.209+00:00",
            "updatedAt": "2025-06-16T08:27:29.209+00:00"
        },
        {
            "id": "01jxvxkghm5zjqcwhtsdrtpxf7",
            "number": "AIA-01JXVX-QNFVK",
            "orderId": "01jxvxkgh7jtb38t9b9v23vscd",
            "status": "Pending",
            "amount": 2310,
            "createdAt": "2025-06-16T08:27:29.204+00:00",
            "updatedAt": "2025-06-16T08:27:29.204+00:00"
        }
    ],
    "payments": [
        {
            "id": "01jy3yfa8p5jk0dkwtyjf3nm1g",
            "userId": "01jxmc3xcy6gp50a59ke0zv28m",
            "vendorId": "01js3yagzh23spmwf6xfxnm18z",
            "invoiceId": "01jy3yfa8kk1g4bz5w1hdv1gp5",
            "ref": "AIA-01JY3Y-GXEDR",
            "req": "ws_CO_19062025141637355757429010",
            "receipt": null,
            "amount": "2200.00",
            "method": "Mpesa",
            "status": "Failed",
            "createdAt": "2025-06-19T11:16:35.734+00:00",
            "updatedAt": "2025-06-19T11:17:09.066+00:00",
            "barcode": ""
        },
        {
            "id": "01jy63aqsv1nygz5c05bw4dt6e",
            "userId": "01js3yagycbxfjxt06pj5tp8g4",
            "vendorId": "01js3yagzh23spmwf6xfxnm18z",
            "invoiceId": "01jy63aqsrb3d9xap7cs5zywf2",
            "ref": "AIA-01JY63-X81F0",
            "req": "ws_CO_20062025101959384757429010",
            "receipt": null,
            "amount": "2200.00",
            "method": "Mpesa",
            "status": "Failed",
            "createdAt": "2025-06-20T07:19:57.499+00:00",
            "updatedAt": "2025-06-20T07:20:31.282+00:00",
            "barcode": ""
        },
        {
            "id": "01jy63egt1dxc157q69w7pqphs",
            "userId": "01js3yagycbxfjxt06pj5tp8g4",
            "vendorId": "01js3yagzh23spmwf6xfxnm18z",
            "invoiceId": "01jy63egsze49a4x68vf268kcr",
            "ref": "AIA-01JY63-NAPPN",
            "req": "ws_CO_20062025102202265757429010",
            "receipt": null,
            "amount": "2200.00",
            "method": "Mpesa",
            "status": "Failed",
            "createdAt": "2025-06-20T07:22:01.409+00:00",
            "updatedAt": "2025-06-20T07:22:05.397+00:00",
            "barcode": ""
        },
        {
            "id": "01jy63bz1z6namg0hked4ta494",
            "userId": "01js3yagycbxfjxt06pj5tp8g4",
            "vendorId": "01js3yagzh23spmwf6xfxnm18z",
            "invoiceId": "01jy63bz1xjt6t92fbjq5ecvrz",
            "ref": "AIA-01JY63-6TFBF",
            "req": "ws_CO_20062025102038825757429010",
            "receipt": null,
            "amount": "2200.00",
            "method": "Mpesa",
            "status": "Failed",
            "createdAt": "2025-06-20T07:20:37.695+00:00",
            "updatedAt": "2025-06-20T07:20:41.237+00:00",
            "barcode": ""
        },
        {
            "id": "01jxvxkghvf58w72mtpsd1w1re",
            "userId": "01jrssz8dr1s2rtppk5yr0yzdg",
            "vendorId": "01js3yagzh23spmwf6xfxnm18z",
            "invoiceId": "01jxvxkghsbwdtzq9qxg9rr2mr",
            "ref": "AIA-01JXVX-MJWFH",
            "req": "ws_CO_16062025112731162757429010",
            "receipt": null,
            "amount": "2310.00",
            "method": "ke.mpesa",
            "status": "Failed",
            "createdAt": "2025-06-16T08:27:29.211+00:00",
            "updatedAt": "2025-06-16T08:27:42.938+00:00",
            "barcode": ""
        },
        {
            "id": "01jy63e52je532x9qc4gprnry9",
            "userId": "01js3yagycbxfjxt06pj5tp8g4",
            "vendorId": "01js3yagzh23spmwf6xfxnm18z",
            "invoiceId": "01jy63e52ep1egsh7zawngb0wr",
            "ref": "AIA-01JY63-AYD9T",
            "req": "ws_CO_20062025102150499757429010",
            "receipt": null,
            "amount": "2200.00",
            "method": "Mpesa",
            "status": "Failed",
            "createdAt": "2025-06-20T07:21:49.394+00:00",
            "updatedAt": "2025-06-20T07:21:52.914+00:00",
            "barcode": ""
        }
    ],
    "fulfillmentProgress": 0,
    "estimatedCompletionTime": null,
    "hasOverdueItems": false,
    "departmentBreakdown": {},
    "canBeCompleted": false,
    "requiresAttention": false,
    "totalPreparationTime": 0,
    "qrCode": "",
    "pricing": {
        "subtotal": 0,
        "modifiersTotal": 0,
        "chargesTotal": 0,
        "total": 0,
        "invoiceAmount": 2200,
        "currency": "KES"
    },
    "fulfillment": {
        "totalItems": 0,
        "completedItems": 0,
        "pendingItems": 0,
        "preparingItems": 0,
        "readyItems": 0,
        "progress": 0,
        "estimatedCompletion": null,
        "departments": {},
        "requiresAttention": false
    }
}