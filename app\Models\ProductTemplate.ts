import { DateTime } from 'luxon'
import {
  BaseModel,
  BelongsTo,
  HasMany,
  ManyToMany,
  beforeCreate,
  belongsTo,
  column,
  computed,
  hasMany,
  manyToMany,
} from '@ioc:Adonis/Lucid/Orm'
import { AttachmentContract, attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Attachment from './Attachment'
import { Filterable } from '@ioc:Adonis/Addons/LucidFilter'
import { compose } from '@ioc:Adonis/Core/Helpers'
import ProductFilter from './Filters/ProductFilter'
import ProductCategory from './ProductCategory'
import Service from './Service'
import { ulid } from 'ulidx'
import Vendor from './Vendor'
import Form from './Form'
import Branch from './Branch'
import Tag from './Tag'
import Product from './Product'

export default class ProductTemplate extends compose(BaseModel, Filterable) {
  public static $filter = () => ProductFilter

  @column({ isPrimary: true })
  public id: string

  @column()
  public name: string

  @column()
  public ref: string

  @column()
  public details: string

  @column()
  public price: number

  @column()
  public discounted: number

  @column()
  public stock: number

  @column()
  public active: boolean

  @column()
  public featured: boolean

  @column()
  public type: 'Physical' | 'Digital' | 'Service'

  @column()
  public condition: 'New' | 'Used' | 'Refurbished'

  @column()
  public status: 'Draft' | 'Pending' | 'Published' | 'Unpublished' | 'Archived'

  @column()
  public availability: 'In Stock' | 'Out of Stock' | 'Pre Order'

  @column()
  public shipping: 'Free' | 'Paid' | 'Pickup'

  @column()
  public unit: MeasurementUnit

  @column()
  public mode: 'Single' | 'Variable'

  @column()
  public payment: 'Prepaid' | 'Postpaid'

  @column()
  public visibility: 'Private' | 'Public' | 'Restricted'

  @column()
  public productCategoryId: string

  @column()
  public userId: string

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public serviceId: string

  @column()
  public meta: Record<string, any>

  @column()
  public extra: Record<string, any>

  @attachment({ folder: 'products', preComputeUrl: true })
  public image: AttachmentContract | null

  @column.dateTime({ autoCreate: true })
  public expiresAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeCreate()
  public static async generateUlid(product: Service) {
    product.id = ulid().toLowerCase()
  }

  @computed()
  public get quantity() {
    return this.$extras.pivot_quantity
  }

  @hasMany(() => Attachment, {
    foreignKey: 'attachableId',
    onQuery: (query) => query.where('attachableType', 'Product'),
  })
  public gallery: HasMany<typeof Attachment>

  @hasMany(() => Form)
  public forms: HasMany<typeof Form>

  @manyToMany(() => Product, {
    pivotTable: 'accompaniments',
    pivotForeignKey: 'side_id',
    pivotColumns: ['price'],
    pivotTimestamps: true,
  })
  public accompaniments: ManyToMany<typeof Product>

  @manyToMany(() => Product, {
    pivotTable: 'upsells',
    pivotForeignKey: 'upsell_id',
    pivotColumns: ['price'],
    pivotTimestamps: true,
  })
  public upsells: ManyToMany<typeof Product>

  @manyToMany(() => Tag, {
    pivotTable: 'product_tags',
  })
  public tags: ManyToMany<typeof Tag>

  @belongsTo(() => ProductCategory)
  public category: BelongsTo<typeof ProductCategory>

  @belongsTo(() => Service)
  public service: BelongsTo<typeof Service>

  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>
}
