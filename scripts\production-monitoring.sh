#!/bin/bash

# Production Monitoring Script for Unified Temp Orders System
# Monitors system health and performance after migration

# Configuration
BASE_URL="http://localhost:3080/v1"  # Update with production URL
LOG_FILE="/tmp/temp-orders-monitoring.log"
ALERT_EMAIL="<EMAIL>"  # Update with actual email
CHECK_INTERVAL=300  # 5 minutes
MAX_RESPONSE_TIME=1000  # milliseconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging function
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# Health check function
check_api_health() {
    local endpoint=$1
    local expected_status=$2
    local description=$3
    
    log_message "INFO" "Checking $description..."
    
    # Make request and measure response time
    start_time=$(date +%s%3N)
    response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$endpoint" \
        -H "Content-Type: application/json" \
        --max-time 10)
    end_time=$(date +%s%3N)
    
    # Extract status code and response time
    status_code=$(echo "$response" | tail -c 4)
    response_time=$((end_time - start_time))
    
    # Check status code
    if [ "$status_code" = "$expected_status" ]; then
        if [ $response_time -le $MAX_RESPONSE_TIME ]; then
            log_message "SUCCESS" "$description - Status: $status_code, Time: ${response_time}ms"
            return 0
        else
            log_message "WARNING" "$description - Slow response: ${response_time}ms (max: ${MAX_RESPONSE_TIME}ms)"
            return 1
        fi
    else
        log_message "ERROR" "$description - Unexpected status: $status_code (expected: $expected_status)"
        return 1
    fi
}

# Database health check
check_database_health() {
    log_message "INFO" "Checking database health..."
    
    # Check temp orders count
    temp_orders_count=$(psql -h localhost -U username -d database -t -c \
        "SELECT COUNT(*) FROM orders WHERE status = 'Pending' AND meta->>'temp_items' IS NOT NULL;" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_message "SUCCESS" "Database accessible - Migrated temp orders: $temp_orders_count"
        
        # Check for any data integrity issues
        integrity_check=$(psql -h localhost -U username -d database -t -c \
            "SELECT COUNT(*) FROM orders WHERE status = 'Pending' AND (meta->>'temp_items' IS NULL OR meta->>'temp_items' = '{}');" 2>/dev/null)
        
        if [ "$integrity_check" -gt 0 ]; then
            log_message "WARNING" "Found $integrity_check pending orders without temp_items"
        fi
        
        return 0
    else
        log_message "ERROR" "Database connection failed"
        return 1
    fi
}

# Queue monitoring
check_queue_health() {
    log_message "INFO" "Checking queue processing..."
    
    # Check application logs for queue errors
    queue_errors=$(tail -n 100 /path/to/app/logs/app.log | grep -i "ProcessTempOrder\|queue.*error\|job.*failed" | wc -l)
    
    if [ "$queue_errors" -eq 0 ]; then
        log_message "SUCCESS" "No queue processing errors detected"
        return 0
    else
        log_message "ERROR" "Found $queue_errors queue-related errors in recent logs"
        return 1
    fi
}

# Performance monitoring
monitor_performance() {
    log_message "INFO" "Monitoring system performance..."
    
    # Check system resources
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    disk_usage=$(df -h / | awk 'NR==2{printf "%s", $5}' | sed 's/%//')
    
    log_message "INFO" "System Resources - CPU: ${cpu_usage}%, Memory: ${memory_usage}%, Disk: ${disk_usage}%"
    
    # Alert if resources are high
    if (( $(echo "$cpu_usage > 80" | bc -l) )); then
        log_message "WARNING" "High CPU usage: ${cpu_usage}%"
    fi
    
    if (( $(echo "$memory_usage > 85" | bc -l) )); then
        log_message "WARNING" "High memory usage: ${memory_usage}%"
    fi
    
    if [ "$disk_usage" -gt 90 ]; then
        log_message "WARNING" "High disk usage: ${disk_usage}%"
    fi
}

# Error rate monitoring
check_error_rates() {
    log_message "INFO" "Checking error rates..."
    
    # Check recent application errors
    recent_errors=$(tail -n 1000 /path/to/app/logs/app.log | grep -i "error\|exception\|failed" | wc -l)
    
    if [ "$recent_errors" -lt 10 ]; then
        log_message "SUCCESS" "Error rate normal: $recent_errors errors in recent logs"
        return 0
    else
        log_message "WARNING" "Elevated error rate: $recent_errors errors in recent logs"
        return 1
    fi
}

# Send alert email
send_alert() {
    local subject=$1
    local message=$2
    
    echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
    log_message "INFO" "Alert sent: $subject"
}

# Main monitoring function
run_monitoring_cycle() {
    log_message "INFO" "Starting monitoring cycle..."
    
    local issues=0
    
    # API Health Checks
    check_api_health "/health" "200" "API Health Endpoint" || ((issues++))
    
    # Database Health
    check_database_health || ((issues++))
    
    # Queue Health
    check_queue_health || ((issues++))
    
    # Performance Monitoring
    monitor_performance
    
    # Error Rate Check
    check_error_rates || ((issues++))
    
    # Summary
    if [ $issues -eq 0 ]; then
        log_message "SUCCESS" "All health checks passed"
    else
        log_message "WARNING" "$issues health check(s) failed"
        
        if [ $issues -ge 3 ]; then
            send_alert "CRITICAL: Temp Orders System Issues" \
                "Multiple health checks failed. Please investigate immediately."
        fi
    fi
    
    log_message "INFO" "Monitoring cycle completed"
    echo "---" >> "$LOG_FILE"
}

# Cleanup verification
verify_cleanup() {
    log_message "INFO" "Verifying cleanup status..."
    
    # Check if temp_orders table still exists
    temp_table_exists=$(psql -h localhost -U username -d database -t -c \
        "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'temp_orders');" 2>/dev/null)
    
    if [ "$temp_table_exists" = "t" ]; then
        log_message "INFO" "temp_orders table still exists (cleanup not performed)"
    else
        log_message "INFO" "temp_orders table removed (cleanup completed)"
    fi
    
    # Check backup table
    backup_table_exists=$(psql -h localhost -U username -d database -t -c \
        "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'temp_orders_backup');" 2>/dev/null)
    
    if [ "$backup_table_exists" = "t" ]; then
        log_message "SUCCESS" "Backup table preserved"
    else
        log_message "WARNING" "Backup table not found"
    fi
}

# Migration status report
generate_status_report() {
    log_message "INFO" "Generating migration status report..."
    
    local report_file="/tmp/migration-status-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
Temp Orders Migration Status Report
Generated: $(date)

=== DATABASE STATUS ===
$(psql -h localhost -U username -d database -c "
SELECT 
    'Pending Orders (Total)' as metric,
    COUNT(*) as value
FROM orders WHERE status = 'Pending'
UNION ALL
SELECT 
    'Migrated Temp Orders',
    COUNT(*)
FROM orders WHERE status = 'Pending' AND meta->>'temp_items' IS NOT NULL
UNION ALL
SELECT 
    'Placed Orders',
    COUNT(*)
FROM orders WHERE status = 'Placed'
UNION ALL
SELECT 
    'Total Order Items',
    COUNT(*)
FROM order_items
UNION ALL
SELECT 
    'Total Invoices',
    COUNT(*)
FROM invoices;
" 2>/dev/null)

=== SYSTEM HEALTH ===
CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')
Memory Usage: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')
Disk Usage: $(df -h / | awk 'NR==2{print $5}')

=== RECENT ACTIVITY ===
$(tail -n 20 "$LOG_FILE")

EOF

    log_message "SUCCESS" "Status report generated: $report_file"
    echo "$report_file"
}

# Usage information
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo "Options:"
    echo "  --monitor     Run continuous monitoring"
    echo "  --check       Run single health check"
    echo "  --cleanup     Verify cleanup status"
    echo "  --report      Generate status report"
    echo "  --help        Show this help message"
}

# Main script logic
case "${1:-}" in
    --monitor)
        echo -e "${BLUE}Starting continuous monitoring...${NC}"
        echo "Monitoring interval: $CHECK_INTERVAL seconds"
        echo "Log file: $LOG_FILE"
        echo "Press Ctrl+C to stop"
        
        while true; do
            run_monitoring_cycle
            sleep $CHECK_INTERVAL
        done
        ;;
    
    --check)
        echo -e "${BLUE}Running single health check...${NC}"
        run_monitoring_cycle
        ;;
    
    --cleanup)
        echo -e "${BLUE}Verifying cleanup status...${NC}"
        verify_cleanup
        ;;
    
    --report)
        echo -e "${BLUE}Generating status report...${NC}"
        report_file=$(generate_status_report)
        echo -e "${GREEN}Report saved to: $report_file${NC}"
        ;;
    
    --help)
        show_usage
        ;;
    
    *)
        echo -e "${RED}Invalid option. Use --help for usage information.${NC}"
        exit 1
        ;;
esac
