import WebSocketServer from './WebSocketServer'
import { Server as HttpServer } from 'http'
import { DateTime } from 'luxon'

/**
 * Singleton manager for WebSocket server instance and operations
 */
class WebSocketManager {
  private static instance: WebSocketManager
  private webSocketServer: WebSocketServer | null = null
  private isInitialized = false

  private constructor() {}

  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager()
    }
    return WebSocketManager.instance
  }

  /**
   * Initialize WebSocket server with HTTP server
   */
  public initialize(httpServer?: HttpServer): void {
    if (this.isInitialized) {
      console.warn('WebSocket server already initialized')
      return
    }

    if (!httpServer) {
      console.warn('No HTTP server provided, WebSocket initialization skipped')
      return
    }

    this.webSocketServer = new WebSocketServer(httpServer)
    this.isInitialized = true
    console.log('WebSocket server initialized successfully')
  }

  /**
   * Get WebSocket server instance
   */
  public getServer(): WebSocketServer {
    if (!this.webSocketServer) {
      throw new Error('WebSocket server not initialized. Call initialize() first.')
    }
    return this.webSocketServer
  }

  /**
   * Check if WebSocket server is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.webSocketServer !== null
  }

  /**
   * Broadcast order status update
   */
  public broadcastOrderStatusUpdate(data: {
    orderId: string
    vendorId: string
    branchId?: string
    customerId?: string
    status: string
    previousStatus: string
    updatedBy?: string
    timestamp?: DateTime
  }): void {
    if (!this.isReady()) return

    const server = this.getServer()
    const timestamp = data.timestamp || DateTime.now()

    const updateData = {
      type: 'order_status_update',
      order_id: data.orderId,
      status: data.status,
      previous_status: data.previousStatus,
      updated_by: data.updatedBy,
      timestamp: timestamp.toISO(),
    }

    // Broadcast to vendor staff
    server.broadcastToRoom(`vendor:${data.vendorId}:staff`, 'order_status_update', updateData)

    // Broadcast to branch staff if specified
    if (data.branchId) {
      server.broadcastToRoom(`branch:${data.branchId}:staff`, 'order_status_update', updateData)
    }

    // Broadcast to managers
    server.broadcastToRoom(`vendor:${data.vendorId}:manager`, 'order_status_update', updateData)

    // Broadcast to customer if specified
    if (data.customerId) {
      server.sendToUser(data.customerId, 'order_status_update', updateData)
    }
  }

  /**
   * Broadcast item status update
   */
  public broadcastItemStatusUpdate(data: {
    orderId: string
    itemId: number
    vendorId: string
    branchId?: string
    departmentId?: string
    status: string
    previousStatus: string
    assignedStaffId?: string
    updatedBy?: string
    timestamp?: DateTime
  }): void {
    if (!this.isReady()) return

    const server = this.getServer()
    const timestamp = data.timestamp || DateTime.now()

    const updateData = {
      type: 'item_status_update',
      order_id: data.orderId,
      item_id: data.itemId,
      status: data.status,
      previous_status: data.previousStatus,
      assigned_staff_id: data.assignedStaffId,
      updated_by: data.updatedBy,
      timestamp: timestamp.toISO(),
    }

    // Broadcast to department if specified
    if (data.departmentId) {
      server.broadcastToRoom(`department:${data.departmentId}`, 'item_status_update', updateData)
    }

    // Broadcast to vendor staff
    server.broadcastToRoom(`vendor:${data.vendorId}:staff`, 'item_status_update', updateData)

    // Broadcast to branch staff if specified
    if (data.branchId) {
      server.broadcastToRoom(`branch:${data.branchId}:staff`, 'item_status_update', updateData)
    }

    // Broadcast to assigned staff if specified
    if (data.assignedStaffId) {
      server.sendToUser(data.assignedStaffId, 'item_status_update', updateData)
    }
  }

  /**
   * Broadcast modifier status update
   */
  public broadcastModifierStatusUpdate(data: {
    orderId: string
    itemId: number
    modifierId: number
    vendorId: string
    departmentId?: string
    status: string
    previousStatus: string
    preparedByStaffId?: string
    updatedBy?: string
    timestamp?: DateTime
  }): void {
    if (!this.isReady()) return

    const server = this.getServer()
    const timestamp = data.timestamp || DateTime.now()

    const updateData = {
      type: 'modifier_status_update',
      order_id: data.orderId,
      item_id: data.itemId,
      modifier_id: data.modifierId,
      status: data.status,
      previous_status: data.previousStatus,
      prepared_by_staff_id: data.preparedByStaffId,
      updated_by: data.updatedBy,
      timestamp: timestamp.toISO(),
    }

    // Broadcast to department if specified
    if (data.departmentId) {
      server.broadcastToRoom(
        `department:${data.departmentId}`,
        'modifier_status_update',
        updateData
      )
    }

    // Broadcast to vendor staff
    server.broadcastToRoom(`vendor:${data.vendorId}:staff`, 'modifier_status_update', updateData)

    // Broadcast to prepared staff if specified
    if (data.preparedByStaffId) {
      server.sendToUser(data.preparedByStaffId, 'modifier_status_update', updateData)
    }
  }

  /**
   * Broadcast department workload update
   */
  public broadcastDepartmentWorkloadUpdate(data: {
    departmentId: string
    vendorId: string
    branchId?: string
    workloadData: {
      pending_items: number
      preparing_items: number
      ready_items: number
      total_items: number
      staff_count: number
      capacity_utilization: number
    }
    timestamp?: DateTime
  }): void {
    if (!this.isReady()) return

    const server = this.getServer()
    const timestamp = data.timestamp || DateTime.now()

    const updateData = {
      type: 'department_workload_update',
      department_id: data.departmentId,
      workload: data.workloadData,
      timestamp: timestamp.toISO(),
    }

    // Broadcast to department
    server.broadcastToRoom(
      `department:${data.departmentId}`,
      'department_workload_update',
      updateData
    )

    // Broadcast to managers
    server.broadcastToRoom(
      `vendor:${data.vendorId}:manager`,
      'department_workload_update',
      updateData
    )

    if (data.branchId) {
      server.broadcastToRoom(
        `branch:${data.branchId}:manager`,
        'department_workload_update',
        updateData
      )
    }
  }

  /**
   * Broadcast order completion notification
   */
  public broadcastOrderCompletion(data: {
    orderId: string
    vendorId: string
    branchId?: string
    customerId: string
    orderType: string
    deliveryType: string
    completionTime: DateTime
    totalItems: number
    departmentBreakdown: Record<string, any>
  }): void {
    if (!this.isReady()) return

    const server = this.getServer()

    const completionData = {
      type: 'order_completed',
      order_id: data.orderId,
      order_type: data.orderType,
      delivery_type: data.deliveryType,
      completion_time: data.completionTime.toISO(),
      total_items: data.totalItems,
      department_breakdown: data.departmentBreakdown,
    }

    // Notify customer
    server.sendToUser(data.customerId, 'order_completed', completionData)

    // Notify staff
    server.broadcastToRoom(`vendor:${data.vendorId}:staff`, 'order_completed', completionData)

    if (data.branchId) {
      server.broadcastToRoom(`branch:${data.branchId}:staff`, 'order_completed', completionData)
    }

    // Notify managers
    server.broadcastToRoom(`vendor:${data.vendorId}:manager`, 'order_completed', completionData)
  }

  /**
   * Broadcast overdue item alert
   */
  public broadcastOverdueAlert(data: {
    orderId: string
    itemId: number
    vendorId: string
    branchId?: string
    departmentId?: string
    assignedStaffId?: string
    overdueMinutes: number
    priorityLevel: 'low' | 'medium' | 'high' | 'critical'
    timestamp?: DateTime
  }): void {
    if (!this.isReady()) return

    const server = this.getServer()
    const timestamp = data.timestamp || DateTime.now()

    const alertData = {
      type: 'overdue_alert',
      order_id: data.orderId,
      item_id: data.itemId,
      overdue_minutes: data.overdueMinutes,
      priority_level: data.priorityLevel,
      assigned_staff_id: data.assignedStaffId,
      timestamp: timestamp.toISO(),
    }

    // Alert department
    if (data.departmentId) {
      server.broadcastToRoom(`department:${data.departmentId}`, 'overdue_alert', alertData)
    }

    // Alert assigned staff
    if (data.assignedStaffId) {
      server.sendToUser(data.assignedStaffId, 'overdue_alert', alertData)
    }

    // Alert managers
    server.broadcastToRoom(`vendor:${data.vendorId}:manager`, 'overdue_alert', alertData)

    if (data.branchId) {
      server.broadcastToRoom(`branch:${data.branchId}:manager`, 'overdue_alert', alertData)
    }
  }

  /**
   * Send staff assignment notification
   */
  public notifyStaffAssignment(data: {
    staffId: string
    orderId: string
    itemId: number
    departmentId?: string
    assignedBy: string
    estimatedTime?: number
    priority?: number
    timestamp?: DateTime
  }): void {
    if (!this.isReady()) return

    const server = this.getServer()
    const timestamp = data.timestamp || DateTime.now()

    const notificationData = {
      type: 'staff_assignment',
      order_id: data.orderId,
      item_id: data.itemId,
      department_id: data.departmentId,
      assigned_by: data.assignedBy,
      estimated_time: data.estimatedTime,
      priority: data.priority,
      timestamp: timestamp.toISO(),
    }

    // Notify assigned staff
    server.sendToUser(data.staffId, 'staff_assignment', notificationData)

    // Notify department if specified
    if (data.departmentId) {
      server.broadcastToRoom(
        `department:${data.departmentId}`,
        'staff_assignment',
        notificationData
      )
    }
  }

  /**
   * Get WebSocket server statistics
   */
  public getStats(): any {
    if (!this.isReady()) {
      return {
        initialized: false,
        error: 'WebSocket server not initialized',
      }
    }

    return {
      initialized: true,
      ...this.getServer().getStats(),
    }
  }

  /**
   * Broadcast custom event to specific room
   */
  public broadcastToRoom(room: string, event: string, data: any): void {
    if (!this.isReady()) return
    this.getServer().broadcastToRoom(room, event, data)
  }

  /**
   * Send custom event to specific user
   */
  public sendToUser(userId: string, event: string, data: any): void {
    if (!this.isReady()) return
    this.getServer().sendToUser(userId, event, data)
  }

  /**
   * Close WebSocket server gracefully
   */
  public async close(): Promise<void> {
    if (!this.isReady()) {
      console.log('WebSocket server not initialized, nothing to close')
      return
    }

    await this.webSocketServer!.close()
    this.webSocketServer = null
    this.isInitialized = false
    console.log('WebSocket manager closed')
  }
}

// Export singleton instance
export default WebSocketManager.getInstance()
