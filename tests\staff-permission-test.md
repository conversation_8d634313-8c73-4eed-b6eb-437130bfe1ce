# Staff Permission Middleware Test Documentation

## Overview
This document outlines the testing strategy for the StaffPermission middleware implementation for the unified temp orders system.

## Middleware Features Implemented

### 1. Permission Guards
- `place-order`: Validates staff can place orders
- `manage-temp-orders`: Validates staff can manage temp orders
- `admin`: Validates admin privileges
- `branch-staff`: Validates staff works at specific branch

### 2. Permission Logic
- **Assigned Staff**: User assigned to the order can place it
- **Admin/Manager/Supervisor**: Can place any order
- **Branch Staff**: Can place orders for their branch
- **Authentication Required**: All operations require valid auth token

### 3. Route Protection
```typescript
// Applied to place order endpoint
Route.post('temp-orders/:id/place-order', 'TempOrdersController.placeOrder')
  .middleware(['auth', 'staffPermission:place-order'])

// Applied to all temp order operations
Route.resource('temp-orders', 'TempOrdersController')
  .middleware({ '*': ['auth'] })
```

## Test Scenarios

### Scenario 1: Successful Order Placement (Authorized Staff)
```bash
# Test with authorized staff credentials
curl -X POST "http://localhost:3080/v1/temp-orders/{temp-order-id}/place-order" \
  -H "Authorization: Bearer {staff-token}" \
  -H "Content-Type: application/json"

# Expected: 200 OK with Order object
# Expected: Order status changed from 'Pending' to 'Placed'
# Expected: Invoice created
# Expected: OrderItems created from meta.temp_items
```

### Scenario 2: Unauthorized Access (No Token)
```bash
# Test without authentication token
curl -X POST "http://localhost:3080/v1/temp-orders/{temp-order-id}/place-order" \
  -H "Content-Type: application/json"

# Expected: 401 Unauthorized
# Expected: {"error": "Authentication required"}
```

### Scenario 3: Forbidden Access (Wrong Staff)
```bash
# Test with staff from different branch
curl -X POST "http://localhost:3080/v1/temp-orders/{temp-order-id}/place-order" \
  -H "Authorization: Bearer {wrong-staff-token}" \
  -H "Content-Type: application/json"

# Expected: 403 Forbidden
# Expected: {"error": "You do not have permission to place orders"}
```

### Scenario 4: Admin Override
```bash
# Test with admin credentials
curl -X POST "http://localhost:3080/v1/temp-orders/{temp-order-id}/place-order" \
  -H "Authorization: Bearer {admin-token}" \
  -H "Content-Type: application/json"

# Expected: 200 OK (admin can place any order)
```

### Scenario 5: Order Not Found
```bash
# Test with non-existent temp order ID
curl -X POST "http://localhost:3080/v1/temp-orders/invalid-id/place-order" \
  -H "Authorization: Bearer {staff-token}" \
  -H "Content-Type: application/json"

# Expected: 404 Not Found
# Expected: {"error": "Temp order not found"}
```

## Permission Matrix

| User Type | Can Create Temp Order | Can View Temp Order | Can Update Temp Order | Can Place Order | Can Delete Temp Order |
|-----------|----------------------|--------------------|-----------------------|-----------------|----------------------|
| Customer | ✅ (own) | ✅ (own) | ✅ (own) | ❌ | ✅ (own) |
| Staff (assigned) | ✅ | ✅ | ✅ | ✅ | ✅ |
| Staff (same branch) | ✅ | ✅ | ✅ | ✅ | ✅ |
| Staff (other branch) | ✅ | ❌ | ❌ | ❌ | ❌ |
| Admin/Manager | ✅ | ✅ | ✅ | ✅ | ✅ |

## Error Response Format

### 401 Unauthorized
```json
{
  "error": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "error": "You do not have permission to place orders"
}
```

### 404 Not Found
```json
{
  "error": "Temp order not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "Permission validation failed"
}
```

## Integration with Existing System

### Backward Compatibility
- All existing temp order endpoints continue to work
- Same response formats maintained
- Only place-order endpoint requires staff permissions
- Customer operations (create, view own, update own) unaffected

### Security Enhancements
- Prevents unauthorized order placement
- Ensures only qualified staff can confirm orders
- Maintains audit trail of who placed orders
- Protects against privilege escalation

## Testing Checklist

- [ ] Middleware registered in kernel.ts
- [ ] Routes protected with appropriate middleware
- [ ] Permission guards work correctly
- [ ] Error responses are consistent
- [ ] Backward compatibility maintained
- [ ] Admin override functionality works
- [ ] Branch-level permissions enforced
- [ ] Authentication requirement enforced
- [ ] Order status transitions properly
- [ ] Invoice creation works after placement
- [ ] OrderItems conversion works correctly

## Production Considerations

### Performance
- Middleware adds minimal overhead
- Database queries optimized for permission checks
- Caching can be added for role/permission lookups

### Monitoring
- Log permission denials for security monitoring
- Track order placement patterns
- Monitor for unauthorized access attempts

### Scalability
- Permission logic can be extended for new roles
- Guards can be added for new operations
- Middleware can be reused across controllers
