import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'order_item_modifiers'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('order_item_id').unsigned().references('id').inTable('order_items').onDelete('CASCADE')
      table.string('modifier_option_id').references('id').inTable('modifier_options').onDelete('CASCADE')
      table.integer('quantity').defaultTo(1)
      table.decimal('price_at_time_of_order', 10, 2)
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 