import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import SubscriptionPlan from 'App/Models/SubscriptionPlan'

export default class SubscriptionPlansController {
  /**
   * List all subscription plans
   */
  public async index({ response }: HttpContextContract) {
    const plans = await SubscriptionPlan.query().where('is_active', true)
    return response.ok(plans)
  }

  /**
   * Get a specific subscription plan
   */
  public async show({ params, response }: HttpContextContract) {
    const plan = await SubscriptionPlan.findOrFail(params.id)
    return response.ok(plan)
  }

  /**
   * Create a new subscription plan
   */
  public async store({ request, response }: HttpContextContract) {
    const data = request.only([
      'name',
      'code',
      'description',
      'amount',
      'currency',
      'billingCycle',
      'features',
      'orderLimit',
      'notificationLimit',
      'isActive',
      'meta',
    ])

    const plan = await SubscriptionPlan.create(data)
    return response.created(plan)
  }

  /**
   * Update a subscription plan
   */
  public async update({ params, request, response }: HttpContextContract) {
    const plan = await SubscriptionPlan.findOrFail(params.id)
    const data = request.only([
      'name',
      'code',
      'description',
      'amount',
      'currency',
      'billingCycle',
      'features',
      'orderLimit',
      'notificationLimit',
      'isActive',
      'meta',
    ])

    plan.merge(data)
    await plan.save()

    return response.ok(plan)
  }

  /**
   * Delete a subscription plan
   */
  public async destroy({ params, response }: HttpContextContract) {
    const plan = await SubscriptionPlan.findOrFail(params.id)
    await plan.delete()
    return response.noContent()
  }
}
