import { NotificationContract } from '@ioc:Verful/Notification'
import { NotificationMessagePayload } from 'App/Interfaces/NotificationMessagePayload'
import Order from 'App/Models/Order'
import User from 'App/Models/User'
import { NotificationHelper } from 'App/Helpers/NotificationHelper'
import { NotificationCategory } from 'App/Enums/NotificationCategory'
import { NotificationType } from 'App/Enums/NotificationType'
import { NotificationPriority } from 'App/Enums/NotificationPriority'

export default class CustomerNewOrder implements NotificationContract {
  constructor(
    private order: Order,
    public message: string = 'Hello {firstName}. Your order from {vendor} is {status}',
    public title: string = 'Order Confirmed'
  ) {
    this.message =
      message ||
      `Hello ${this.order.customer.firstName}. Your order from ${order.vendor.name} is ${order.status}`
  }

  public via(_notifiable) {
    return ['database' as const, 'fcm' as const]
  }

  public toDatabase() {
    const personalizedMessage = this.message
      .replace('{firstName}', this.order.customer.firstName)
      .replace('{status}', this.order.status)
      .replace('{vendor}', this.order.vendor.name)

    return NotificationHelper.createNotificationData(
      this.title,
      personalizedMessage,
      NotificationHelper.createOrderActions(this.order.id, this.order.status, this.order.vendor.name),
      {
        category: NotificationCategory.ORDER,
        notificationType: NotificationType.ORDER_CREATED,
        priority: NotificationPriority.MEDIUM,
        entityId: this.order.id,
        entityType: 'order',
        orderStatus: this.order.status,
        vendorId: this.order.vendor?.id,
        vendorName: this.order.vendor?.name,
        orderTotal: this.order.total,
        currency: 'KES',
        orderDate: this.order.createdAt?.toISO()
      },
      'https://cdn.verful.com/icons/order-confirmed-icon.png'
    )
  }

  public toFcm(notifiable: User): NotificationMessagePayload {
    const body = this.message
      .replace('{firstName}', notifiable.firstName)
      .replace('{status}', this.order.status)
      .replace('{vendor}', this.order.vendor.name)

    return {
      title: this.title,
      body,
      url: `/orders/${this.order.id}`,
      icon: 'https://cdn.verful.com/icons/order-confirmed-icon.png',
      actions: [
        {
          screen: 'view_order',
          label: 'View Order',
          args: { orderId: this.order.id.toString() }
        },
        {
          screen: 'track_order',
          label: 'Track Order',
          args: { orderId: this.order.id.toString() }
        }
      ]
    }
  }
}
