import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from '../../Models/Order'
import { bind } from '@adonisjs/route-model-binding'
import CustomerNewOrder from 'App/Notifications/Customer/CustomerNewOrder'
import User from 'App/Models/User'
import StaffNewOrder from 'App/Notifications/Staff/StaffNewOrder'
import Lot from 'App/Models/Lot'
import { Queue } from '@ioc:Rlanz/Queue'
import OrderPricingService from 'App/Services/OrderPricingService'
import { OrderValidationHelper } from 'App/Helpers/OrderValidationHelper'

/**
 * @name Order management
 * @version 1.0.0
 * @description Order management for the application
 */
export default class OrdersController {
  /**
   * @index
   * @summary List all Orders
   * @description List all Orders, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery isSession - Filter orders by session criteria (true/false). When true, returns orders with purchase action, valid status, vendor/branch IDs, dine-in delivery, instant type, and postpaid payment
   */
  @bind()
  public async index({ request }: HttpContextContract, user: User) {
    const {
      per = 10,
      page = 1,
      order = 'createdAt',
      sort = 'desc',
      isSession,
      ...filters
    } = request.qs()
    const OrderQuery = user.related('orders').query().filter(filters)

    if (isSession === 'true') {
      OrderQuery.select([
        'id',
        'action',
        'vendorId',
        'branchId',
        'delivery',
        'type',
        'status',
        'createdAt',
      ])
        .where('action', 'purchase')
        .whereIn('status', ['placed', 'processing', 'ready', 'delivered'])
        .whereNotNull('vendorId')
        .whereNotNull('branchId')
        .where('delivery', 'dine-in')
        .where('type', 'instant')
        .whereHas('invoices', (query) => {
          query.whereHas('payments', (paymentQuery) => {
            paymentQuery.where('type', 'postpaid')
          })
        })
    } else {
      OrderQuery.preload('customer')
        .preload('branch')
        .preload('vendor')
        .preload('items', (itemQuery) => {
          itemQuery.preload('product')
          itemQuery.preload('modifiers')
        })
        .preload('staff')
        .preload('section')
        .preload('invoices', (iq) => iq.preload('payments'))
    }

    const orders = await OrderQuery.orderBy(order, sort).paginate(page, per)

    // For session orders, return as-is (minimal data)
    if (isSession === 'true') {
      return orders
    }

    // Handle temp orders (Pending status) - populate items from meta.temp_items
    // Process each order in the paginated result
    for (const order of orders) {
      if (order.status === 'Pending' && order.meta?.temp_items) {
        // This is a temp order - populate items from meta.temp_items
        const tempItems = order.meta.temp_items
        const productIds = Object.keys(tempItems)

        if (productIds.length > 0) {
          // Import Product model dynamically to avoid circular dependency
          const { default: Product } = await import('../../Models/Product')
          const products = await Product.query()
            .whereIn('id', productIds)
            .preload('category')
            .preload('gallery')
            .preload('forms')
            .preload('branch')
            .preload('vendor')
            .exec()

          // Create items array and assign to order
          const items = products.map((product) => {
            const quantity = tempItems[product.id]?.quantity || 0
            const productPrice = product.price || 0
            const totalItemCost = productPrice * quantity

            return {
              id: null, // No order_items record yet
              orderId: order.id,
              productId: product.id,
              quantity: quantity,
              meta: null,
              createdAt: order.createdAt,
              updatedAt: order.updatedAt,
              price: productPrice, // ✅ FIX: Set actual product price
              status: 'pending',
              departmentId: null,
              assignedStaffId: null,
              estimatedPreparationTime: null,
              preparationStartedAt: null,
              preparationCompletedAt: null,
              servedAt: null,
              priorityLevel: 1,
              requiresSpecialAttention: false,
              specialInstructions: null,
              preparationNotes: null,
              statusHistory: null,
              qualityCheckStatus: 'not_required',
              qualityCheckedBy: null,
              customerModifications: null,
              cancellationReason: null,
              actualPreparationTime: null,
              preparationAttempts: 1,
              modifiers: [],
              product: product.serialize(), // ✅ FIX: Use serialize() instead of toJSON()
              actualPreparationTimeMinutes: null,
              isOverdue: false,
              preparationProgress: 0,
              statusDisplayName: 'Pending',
              canBeStarted: true,
              canBeCompleted: false,
              canBeServed: false,
              requiresAttention: false,
              estimatedCompletionTime: null,
              allModifiersCompleted: true,
              totalItemCost: totalItemCost, // ✅ FIX: Calculate actual cost
            }
          })

          // Assign items to the order instance
          order.$setRelated('items', items)
        }
      }
    }

    // Add pricing information using standardized service
    return OrderPricingService.addPricingToPaginatedOrders(orders)
  }

  /**
   * @store
   * @summary Create a Order
   * @description Create a Order with their details (name and details)
   * @requestBody {"staffId": "", "vendorId": "", "branchId": "", "sectionId": "", "action": "", "type": "", "delivery": "", "status": "", "meta": {}} - <Order>
   * @responseBody 200 - <Order>
   */
  public async store({ request, response, auth }: HttpContextContract) {
    try {
      const {
        staffId,
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
        userId = null,
        items = [],
      } = request.all()

      // Validate items before creating order
      const itemValidation = OrderValidationHelper.validateDirectOrderItems(items)
      if (!itemValidation.isValid) {
        return OrderValidationHelper.createItemValidationErrorResponse(response, itemValidation)
      }

      const order = await Order.create({
        userId: userId ? userId : auth.user?.id,
        staffId,
        vendorId,
        branchId,
        lotId,
        sectionId,
        action,
        type,
        delivery,
        status,
        meta,
      })

      // Create order items using HasMany relationship
      await Promise.all(
        items.map(async (item: Record<string, number>) => {
          return Promise.all(
            Object.keys(item).map(async (productId) => {
              return await order.related('items').create({
                productId: productId,
                quantity: item[productId],
              })
            })
          )
        })
      )

      order.load('items').then(async () => {
        let amount = order.items?.reduce((acc, item) => acc + item.price * item.quantity, 0)

        if (order.meta && order.meta.charges) {
          amount += Object.values(order.meta.charges as Record<string, number>)?.reduce(
            (acc, charge) => acc + charge,
            0
          )
        }

        if (amount > 0) {
          await order.related('invoices').create({
            amount,
            status: 'Pending',
          })
        }
      })

      await order.load('customer')
      await order.load('vendor')
      await order.load('branch')
      await order.load('invoices', (iq) => iq.preload('payments'))

      await order.customer.notify(new CustomerNewOrder(order))

      order.customer
        .related('branches')
        .sync({ [branchId]: { active: true, vendor_id: vendorId, branch_id: branchId } })

      const staff = lotId
        ? await Lot.findOrFail(lotId).then(
            async (lot) => await lot.related('staff').query().orderBy('created_at', 'desc')
          )
        : await User.query().whereHas('employers', (q) => {
            q.where('branch_id', branchId)
            q.wherePivot('online', true)
          })

      staff.map((x) => x.notify(new StaffNewOrder(order)))

      // Check if branch has hotelplus integration, and send order data to h+ /sales
      const settings = await order.branch
        ?.related('settings')
        .query()
        .where('name', 'hotelplus')
        .first()

      if (settings?.options) {
        Queue.dispatch('App/Jobs/SyncOrder', {
          order,
          settings: settings.options,
          branchId,
        })
      }

      return response.json(order)
    } catch (error) {
      console.error(error)

      return response.badRequest({ error: error.message })
    }
  }
}
