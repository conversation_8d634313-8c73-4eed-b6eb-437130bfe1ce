import test from 'japa'
import Duration from 'App/Models/Duration'
import Database from '@ioc:Adonis/Lucid/Database'

test.group('Duration Model', (group) => {
  group.beforeEach(async () => {
    await Database.beginGlobalTransaction()
  })

  group.afterEach(async () => {
    await Database.rollbackGlobalTransaction()
  })

  test('should create a duration with valid data', async (assert) => {
    const durationData = {
      name: 'Test Service Duration',
      description: 'A test duration for services',
      minutes: 120,
      bufferMinutes: 30,
      category: 'medium' as const,
      maxConcurrent: 3,
      allowsBackToBack: true,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 5,
        timeSlots: ['morning', 'afternoon'],
        blackoutDays: ['sunday']
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 2,
        equipmentRequired: ['basic-kit']
      },
      active: true
    }

    const duration = await Duration.create(durationData)

    assert.exists(duration.id)
    assert.equal(duration.name, durationData.name)
    assert.equal(duration.minutes, durationData.minutes)
    assert.equal(duration.bufferMinutes, durationData.bufferMinutes)
    assert.equal(duration.category, durationData.category)
    assert.isTrue(duration.active)
  })

  test('should calculate calendar block minutes correctly', async (assert) => {
    const duration = await Duration.create({
      name: 'Test Duration',
      minutes: 120,
      bufferMinutes: 30,
      category: 'medium',
      maxConcurrent: 1,
      allowsBackToBack: true,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 5,
        timeSlots: [],
        blackoutDays: []
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: []
      }
    })

    assert.equal(duration.calendarBlockMinutes, 150) // 120 + 30
  })

  test('should calculate total hours correctly', async (assert) => {
    const duration = await Duration.create({
      name: 'Test Duration',
      minutes: 90,
      bufferMinutes: 30,
      category: 'medium',
      maxConcurrent: 1,
      allowsBackToBack: true,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 5,
        timeSlots: [],
        blackoutDays: []
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: []
      }
    })

    assert.equal(duration.totalHours, 2) // (90 + 30) / 60 = 2
  })

  test('should validate category computed properties', async (assert) => {
    const shortDuration = await Duration.create({
      name: 'Short Duration',
      minutes: 30,
      bufferMinutes: 15,
      category: 'short',
      maxConcurrent: 5,
      allowsBackToBack: true,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 1,
        maxPerDay: 8,
        timeSlots: [],
        blackoutDays: []
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: []
      }
    })

    assert.isTrue(shortDuration.isShortDuration)
    assert.isFalse(shortDuration.isMediumDuration)
    assert.isFalse(shortDuration.isLongDuration)
    assert.isFalse(shortDuration.isFullDay)
  })

  test('should validate scheduling rules correctly', async (assert) => {
    const duration = await Duration.create({
      name: 'Test Duration',
      minutes: 120,
      bufferMinutes: 30,
      category: 'medium',
      maxConcurrent: 3,
      allowsBackToBack: false,
      requiredBreakAfter: 30,
      schedulingRules: {
        minAdvanceHours: 24,
        maxPerDay: 2,
        timeSlots: ['morning'],
        blackoutDays: ['sunday', 'saturday']
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 2,
        equipmentRequired: ['premium-kit']
      }
    })

    // Test advance booking requirement
    assert.isTrue(duration.requiresAdvanceBooking(25))
    assert.isFalse(duration.requiresAdvanceBooking(23))

    // Test day scheduling
    assert.isTrue(duration.canScheduleOnDay('monday'))
    assert.isFalse(duration.canScheduleOnDay('sunday'))
    assert.isFalse(duration.canScheduleOnDay('saturday'))

    // Test time slot scheduling
    assert.isTrue(duration.canScheduleInTimeSlot('morning'))
    assert.isFalse(duration.canScheduleInTimeSlot('afternoon'))

    // Test back-to-back scheduling
    assert.isFalse(duration.canScheduleBackToBack())

    // Test required break
    assert.equal(duration.getRequiredBreakMinutes(), 30)

    // Test max per day
    assert.equal(duration.getMaxPerDay(), 2)
  })

  test('should validate branch constraints correctly', async (assert) => {
    const duration = await Duration.create({
      name: 'Test Duration',
      minutes: 240,
      bufferMinutes: 60,
      category: 'long',
      maxConcurrent: 1,
      allowsBackToBack: false,
      requiredBreakAfter: 60,
      schedulingRules: {
        minAdvanceHours: 48,
        maxPerDay: 1,
        timeSlots: [],
        blackoutDays: []
      },
      branchConstraints: {
        respectBranchHours: false,
        staffRequired: 3,
        equipmentRequired: ['premium-kit', 'vehicle']
      }
    })

    assert.isFalse(duration.shouldRespectBranchHours())
    assert.equal(duration.getRequiredStaff(), 3)
    assert.deepEqual(duration.getRequiredEquipment(), ['premium-kit', 'vehicle'])
  })

  test('should validate configuration correctly', async (assert) => {
    // Valid configuration
    const validDuration = await Duration.create({
      name: 'Valid Duration',
      minutes: 120,
      bufferMinutes: 30,
      category: 'medium',
      maxConcurrent: 3,
      allowsBackToBack: true,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 5,
        timeSlots: [],
        blackoutDays: []
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 2,
        equipmentRequired: []
      }
    })

    const validValidation = validDuration.validateConfiguration()
    assert.isTrue(validValidation.valid)
    assert.equal(validValidation.errors.length, 0)

    // Invalid configuration
    const invalidDuration = new Duration()
    invalidDuration.fill({
      minutes: -10, // Invalid
      bufferMinutes: -5, // Invalid
      maxConcurrent: 0, // Invalid
      requiredBreakAfter: -30, // Invalid
      schedulingRules: {
        minAdvanceHours: -1, // Invalid
        maxPerDay: 0, // Invalid
        timeSlots: [],
        blackoutDays: []
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: -1, // Invalid
        equipmentRequired: []
      }
    })

    const invalidValidation = invalidDuration.validateConfiguration()
    assert.isFalse(invalidValidation.valid)
    assert.isAbove(invalidValidation.errors.length, 0)
  })

  test('should create template correctly', async (assert) => {
    const template = Duration.createTemplate('Quick Service', 30, 'short', {
      maxConcurrent: 10,
      schedulingRules: {
        minAdvanceHours: 1,
        maxPerDay: 15,
        timeSlots: [],
        blackoutDays: []
      }
    })

    assert.equal(template.name, 'Quick Service')
    assert.equal(template.minutes, 30)
    assert.equal(template.category, 'short')
    assert.equal(template.maxConcurrent, 10)
    assert.equal(template.bufferMinutes, 15) // Default for short
    assert.isTrue(template.allowsBackToBack) // Default for short
    assert.equal(template.schedulingRules?.minAdvanceHours, 1)
    assert.equal(template.schedulingRules?.maxPerDay, 15)
  })
})
