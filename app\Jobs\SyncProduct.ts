import type { JobHandlerContract, Job } from '@ioc:Rlanz/Queue'
import Product from 'App/Models/Product'
import Hotelplus from 'App/Services/Hotelplus'
import Redis from '@ioc:Adonis/Addons/Redis'
import ProductType from 'App/Models/ProductType'
import Branch from 'App/Models/Branch'
import { string } from '@ioc:Adonis/Core/Helpers'
import Service from 'App/Models/Service'
import ProductTemplate from 'App/Models/ProductTemplate'

export type SyncProductsPayload = {
  channel: string
  vendor: string
}

export default class SyncProducts implements JobHandlerContract {
  constructor(public job: Job) {
    this.job = job
  }

  /**
   * Base Entry point
   */
  public async handle(payload: SyncProductsPayload) {
    let response: any[] = []
    try {
      switch (payload.channel) {
        case 'hotelplus':
          const branch = await Branch.find(payload.vendor)

          const settings = await branch
            ?.related('settings')
            .query()
            .where('name', 'hotelplus')
            .first()

          const hotelplus = new Hotelplus(payload.vendor, settings?.options)

          let lastProductSaved = Number(await Redis.get(`lastProductSaved${payload.vendor}`)) || 1

          response = await hotelplus.syncProducts(lastProductSaved)

          const service = await Service.firstOrCreate({
            name: 'Buy Food',
          })

          const productType = await ProductType.firstOrCreate(
            {
              name: 'Restaurant Food',
            },
            {
              serviceId: service.id,
            }
          )

          for (const product of response) {
            const slug = productType.id + '-' + string.toSlug(product.category.toLowerCase())

            const productCategory = await productType?.related('categories').firstOrCreate(
              {
                slug,
              },
              {
                name: string.capitalCase(product.category),
              }
            )

            let productTemplate: ProductTemplate | null = null

            if (product.sku) {
              productTemplate = await ProductTemplate.findBy('sku', product.sku)
            }

            if (!product.name.toLowerCase().includes('deleted')) {
              await Product.updateOrCreate(
                {
                  ref: product.ref,
                  branchId: payload.vendor,
                  vendorId: branch?.vendorId,
                },
                {
                  ...(productTemplate ? productTemplate.toJSON() : {}),
                  name: string.capitalCase(product.name),
                  details: product.details,
                  price: product.price,
                  branchId: payload.vendor,
                  stock: product.stock,
                  productCategoryId: productCategory?.id,
                  serviceId: productType?.serviceId,
                }
              )
            }

            await Redis.set(`lastProductSaved${payload.vendor}`, lastProductSaved + 1)
          }

          break
        case 'other':
          break
        default:
          throw new Error('Vendor not found')
      }
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * This is an optional method that gets called if it exists when the retries has exceeded and is marked failed.
   */
  public async failed() {}
}
