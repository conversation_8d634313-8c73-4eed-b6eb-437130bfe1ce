import test from 'japa'
import { DateTime } from 'luxon'
import BookingValidationService from 'App/Services/BookingValidationService'
import Booking, { BookingStatus } from 'App/Models/Booking'
import Product from 'App/Models/Product'
import Branch from 'App/Models/Branch'
import Vendor from 'App/Models/Vendor'
import User from 'App/Models/User'
import Duration from 'App/Models/Duration'
import ServiceConfiguration from 'App/Models/ServiceConfiguration'
import ServiceConfigurationOption from 'App/Models/ServiceConfigurationOption'
import { ServiceOptionType } from 'App/Models/ServiceConfigurationOption'

test.group('Booking Validation Service', (group) => {
  let vendor: Vendor
  let branch: Branch
  let customer: User
  let product: Product
  let duration: Duration
  let serviceConfiguration: ServiceConfiguration

  group.beforeEach(async () => {
    // Create test data (similar to availability test)
    vendor = await Vendor.create({
      name: 'Test Spa',
      email: '<EMAIL>',
      phone: '1234567890',
      active: true
    })

    branch = await Branch.create({
      vendorId: vendor.id,
      name: 'Main Branch',
      details: 'Main spa location',
      location: {
        name: 'Test Location',
        coordinates: { lat: 0, lng: 0 }
      },
      phone: '1234567890'
    })

    customer = await User.create({
      name: 'Test Customer',
      email: '<EMAIL>',
      phone: '1234567890',
      password: 'password123'
    })

    duration = await Duration.create({
      name: 'Standard Massage (60 minutes)',
      description: '60-minute relaxing massage',
      minutes: 60,
      bufferMinutes: 15,
      category: 'medium',
      maxConcurrent: 3,
      allowsBackToBack: false,
      requiredBreakAfter: 30,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 8,
        timeSlots: [],
        blackoutDays: ['sunday']
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: ['massage_table']
      },
      active: true
    })

    serviceConfiguration = await ServiceConfiguration.create({
      name: 'Massage Service Configuration',
      description: 'Configuration for massage services',
      active: true
    })

    await ServiceConfigurationOption.create({
      serviceConfigurationId: serviceConfiguration.id,
      name: 'Standard Duration',
      type: ServiceOptionType.DURATION,
      description: '60-minute session',
      priceAdjustment: 0,
      durationId: duration.id,
      isDefault: true,
      sortOrder: 1,
      constraints: {},
      active: true
    })

    product = await Product.create({
      name: 'Relaxing Massage',
      details: 'A relaxing full-body massage',
      price: 100,
      vendorId: vendor.id,
      branchId: branch.id,
      serviceConfigurationId: serviceConfiguration.id,
      active: true,
      type: 'Service'
    })
  })

  test('should validate a valid booking request', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const validation = await BookingValidationService.validateBookingRequest({
      productId: product.id,
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: [{
        id: 'test-option',
        name: 'Standard Duration',
        type: 'duration',
        priceAdjustment: 0
      }]
    })

    assert.isTrue(validation.valid)
    assert.equal(validation.errors.length, 0)
  })

  test('should reject booking with invalid time range', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.minus({ hours: 1 }) // End before start

    const validation = await BookingValidationService.validateBookingRequest({
      productId: product.id,
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    assert.isFalse(validation.valid)
    assert.isAbove(validation.errors.length, 0)
    assert.include(validation.errors.join(' '), 'before end time')
  })

  test('should reject booking in the past', async (assert) => {
    const appointmentStart = DateTime.now().minus({ hours: 1 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const validation = await BookingValidationService.validateBookingRequest({
      productId: product.id,
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    assert.isFalse(validation.valid)
    assert.include(validation.errors.join(' '), 'past')
  })

  test('should reject booking with insufficient advance time', async (assert) => {
    const appointmentStart = DateTime.now().plus({ hours: 1 }) // Only 1 hour advance
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const validation = await BookingValidationService.validateBookingRequest({
      productId: product.id,
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    assert.isFalse(validation.valid)
    assert.include(validation.errors.join(' '), '2 hours in advance')
  })

  test('should reject booking with too short duration', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ minutes: 10 }) // Only 10 minutes

    const validation = await BookingValidationService.validateBookingRequest({
      productId: product.id,
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    assert.isFalse(validation.valid)
    assert.include(validation.errors.join(' '), 'at least 15 minutes')
  })

  test('should detect time conflicts with existing bookings', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    // Create an existing booking that conflicts
    await Booking.create({
      customerId: customer.id,
      vendorId: vendor.id,
      branchId: branch.id,
      productId: product.id,
      appointmentStart,
      appointmentEnd,
      durationMinutes: 60,
      bufferMinutes: 15,
      status: BookingStatus.CONFIRMED,
      staffAssignments: [],
      equipmentReservations: [],
      selectedServiceOptions: [],
      basePrice: 100,
      optionsTotal: 0,
      totalPrice: 100
    })

    // Try to create another booking at the same time
    const validation = await BookingValidationService.validateBookingRequest({
      productId: product.id,
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    assert.isFalse(validation.valid)
    assert.include(validation.errors.join(' '), 'conflicts')
    assert.exists(validation.conflicts)
    assert.isAbove(validation.conflicts!.timeConflicts.length, 0)
  })

  test('should validate missing required fields', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    // Test with missing product ID
    const validation = await BookingValidationService.validateBookingRequest({
      productId: '', // Missing
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    assert.isFalse(validation.valid)
    assert.include(validation.errors.join(' '), 'Product ID is required')
  })

  test('should validate non-existent entities', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 1 })

    const validation = await BookingValidationService.validateBookingRequest({
      productId: 'non-existent-product',
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    assert.isFalse(validation.valid)
    assert.include(validation.errors.join(' '), 'Product not found')
  })

  test('should provide warnings for long bookings', async (assert) => {
    const appointmentStart = DateTime.now().plus({ days: 1, hours: 10 })
    const appointmentEnd = appointmentStart.plus({ hours: 9 }) // 9 hours - very long

    const validation = await BookingValidationService.validateBookingRequest({
      productId: product.id,
      branchId: branch.id,
      customerId: customer.id,
      appointmentStart,
      appointmentEnd,
      selectedServiceOptions: []
    })

    // Should be valid but with warnings
    assert.isTrue(validation.valid)
    assert.isAbove(validation.warnings.length, 0)
    assert.include(validation.warnings.join(' '), '8 hours')
  })

  group.afterEach(async () => {
    // Clean up test data
    await Booking.query().delete()
    await product.delete()
    await serviceConfiguration.delete()
    await duration.delete()
    await branch.delete()
    await vendor.delete()
    await customer.delete()
  })
})
