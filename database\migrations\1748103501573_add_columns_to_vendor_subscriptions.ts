import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'vendor_subscriptions'

  public async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add relationships
      table.string('vendor_id').references('id').inTable('vendors').onDelete('CASCADE').notNullable()
      table.integer('plan_id').unsigned().references('id').inTable('vendor_subscription_plans').onDelete('CASCADE').notNullable()
      
      // Add subscription details
      table.enum('status', ['active', 'cancelled', 'expired']).notNullable().defaultTo('active')
      table.timestamp('start_date', { useTz: true }).notNullable()
      table.timestamp('end_date', { useTz: true }).notNullable()
      table.enum('billing_cycle', ['monthly', 'yearly']).notNullable()
      table.decimal('amount', 10, 2).notNullable()
      table.string('currency').notNullable().defaultTo('KES')
      table.boolean('auto_renew').notNullable().defaultTo(true)
      
      // Add billing info
      table.timestamp('last_billed_at', { useTz: true }).nullable()
      table.timestamp('next_billing_at', { useTz: true }).nullable()
      table.string('payment_method_id').nullable()
      
      // Add metadata
      table.jsonb('meta').nullable()
      
      // Add indexes
      table.index(['vendor_id'])
      table.index(['plan_id'])
      table.index(['status'])
      table.index(['end_date'])
    })
  }

  public async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('vendor_id')
      table.dropColumn('plan_id')
      table.dropColumn('status')
      table.dropColumn('start_date')
      table.dropColumn('end_date')
      table.dropColumn('billing_cycle')
      table.dropColumn('amount')
      table.dropColumn('currency')
      table.dropColumn('auto_renew')
      table.dropColumn('last_billed_at')
      table.dropColumn('next_billing_at')
      table.dropColumn('payment_method_id')
      table.dropColumn('meta')
    })
  }
}
