import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Vendor from '../../Models/Vendor'
import { bind } from '@adonisjs/route-model-binding'
import Task from 'App/Models/Task'

/**
 * @name Vendor management
 * @version 1.0.0
 * @description Vendor management for the application
 */
export default class VendorTasksController {
  /**
   * @index
   * @summary List all vendor tasks
   * @description List all vendor tasks, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const vendorQuery = vendor
      .related('tasks')
      .query()
      .preload('services', (sq) => sq.whereHas('providers', (vq) => vq.where('id', vendor.id)))
      .filter(filters)

    return await vendorQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Attach a task to a vendor
   * @description Attach a task to a vendor with active status
   * @requestBody {"tasks": "", "active": true}
   * @responseBody 200 - <Vendor>
   */
  @bind()
  public async store({ request, response }: HttpContextContract, vendor: Vendor) {
    const { tasks = [], active = true } = request.all()
    tasks.map(async (taskId: number) => {
      await vendor.related('tasks').attach({ [taskId]: { active } })
    })

    await vendor.save()

    return response.json(vendor)
  }

  /**
   * @update
   * @summary Update a vendor task
   * @description Update a vendor with active status
   * @requestBody <Vendor>
   * @responseBody 200 - <Vendor>
   * @response 404 - Vendor not found
   */
  @bind()
  public async update({ request, response }: HttpContextContract, vendor: Vendor, task: Task) {
    const { active } = request.all()
    await vendor.related('tasks').sync({ [task.id]: { active } })
    await vendor.save()

    return response.json(vendor)
  }

  /**
   * @destroy
   * @summary Delete a vendor task
   * @responseBody 204 - No content
   */
  @bind()
  public async destroy({ response }: HttpContextContract, vendor: Vendor, task: Task) {
    await vendor.related('tasks').detach([task.id])

    return response.noContent()
  }
}
