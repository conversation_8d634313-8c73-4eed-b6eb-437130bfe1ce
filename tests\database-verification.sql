-- Database Verification Script for Unified Temp Orders System
-- Run this script to verify the database state after migration

-- 1. Check temp orders in unified orders table
SELECT 
    'Temp Orders (Pending Status)' as description,
    COUNT(*) as count,
    MIN(created_at) as oldest,
    MA<PERSON>(created_at) as newest
FROM orders 
WHERE status = 'Pending';

-- 2. Check placed orders
SELECT 
    'Placed Orders' as description,
    COUNT(*) as count,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM orders 
WHERE status != 'Pending';

-- 3. Verify temp_items structure in meta
SELECT 
    'Orders with temp_items' as description,
    COUNT(*) as count
FROM orders 
WHERE status = 'Pending' 
AND meta->>'temp_items' IS NOT NULL;

-- 4. Sample temp order data
SELECT 
    'Sample Temp Order Data' as description,
    id,
    status,
    vendor_id,
    branch_id,
    meta->'temp_items' as temp_items,
    meta->'charges' as charges,
    created_at
FROM orders 
WHERE status = 'Pending' 
LIMIT 3;

-- 5. Check order items for placed orders
SELECT 
    'Order Items for Placed Orders' as description,
    COUNT(DISTINCT o.id) as orders_with_items,
    COUNT(oi.id) as total_items,
    AVG(oi.quantity) as avg_quantity
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id
WHERE o.status = 'Placed';

-- 6. Check invoices for placed orders
SELECT 
    'Invoices for Placed Orders' as description,
    COUNT(DISTINCT o.id) as orders_with_invoices,
    COUNT(i.id) as total_invoices,
    AVG(i.amount) as avg_amount
FROM orders o
LEFT JOIN invoices i ON o.id = i.order_id
WHERE o.status = 'Placed';

-- 7. Verify data integrity - orders without temp_items in Pending status
SELECT 
    'Pending Orders Missing temp_items' as description,
    COUNT(*) as count
FROM orders 
WHERE status = 'Pending' 
AND (meta->>'temp_items' IS NULL OR meta->>'temp_items' = '{}');

-- 8. Check for orphaned temp orders (if temp_orders table still exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'temp_orders') THEN
        RAISE NOTICE 'temp_orders table still exists - checking for remaining records';
        PERFORM COUNT(*) FROM temp_orders;
    ELSE
        RAISE NOTICE 'temp_orders table has been removed (expected after cleanup)';
    END IF;
END $$;

-- 9. Backup table verification
SELECT 
    'Backup Table Status' as description,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'temp_orders_backup') 
        THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as backup_status;

-- 10. Migration metadata
SELECT 
    'Migration Metadata' as description,
    backup_date,
    migration_status,
    record_count
FROM temp_orders_backup_metadata 
ORDER BY backup_date DESC 
LIMIT 1;

-- 11. Performance check - index usage
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM orders WHERE status = 'Pending' LIMIT 10;

-- 12. Data consistency check
SELECT 
    'Data Consistency Check' as description,
    vendor_id,
    branch_id,
    COUNT(*) as order_count,
    COUNT(CASE WHEN status = 'Pending' THEN 1 END) as temp_orders,
    COUNT(CASE WHEN status != 'Pending' THEN 1 END) as placed_orders
FROM orders 
GROUP BY vendor_id, branch_id
ORDER BY order_count DESC
LIMIT 5;

-- 13. Recent activity
SELECT 
    'Recent Order Activity' as description,
    DATE(created_at) as date,
    status,
    COUNT(*) as count
FROM orders 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at), status
ORDER BY date DESC, status;

-- 14. System health check
SELECT 
    'System Health Summary' as description,
    (SELECT COUNT(*) FROM orders WHERE status = 'Pending') as temp_orders,
    (SELECT COUNT(*) FROM orders WHERE status = 'Placed') as placed_orders,
    (SELECT COUNT(*) FROM order_items) as total_order_items,
    (SELECT COUNT(*) FROM invoices) as total_invoices,
    (SELECT COUNT(*) FROM orders WHERE meta->>'temp_items' IS NOT NULL) as orders_with_temp_items;

-- 15. Validation queries for testing
-- These can be used to verify test data

-- Check if test orders exist
SELECT 
    'Test Orders Check' as description,
    COUNT(*) as test_orders
FROM orders 
WHERE meta->>'customerName' LIKE '%Test%' 
OR meta->>'notes' LIKE '%test%';

-- Check for orders with specific test patterns
SELECT 
    'Orders with Test Patterns' as description,
    id,
    status,
    meta->>'customerName' as customer_name,
    meta->>'notes' as notes,
    created_at
FROM orders 
WHERE (meta->>'customerName' LIKE '%Test%' OR meta->>'notes' LIKE '%test%')
AND created_at >= CURRENT_DATE
ORDER BY created_at DESC
LIMIT 5;
