/*
|--------------------------------------------------------------------------
| Preloaded File
|--------------------------------------------------------------------------
|
| Any code written inside this file will be executed during the application
| boot.
|
*/
import Notification from '@ioc:Verful/Notification'
import SmsChannel from '../app/Channels/Sms'
import FcmChannel from 'App/Channels/Fcm'
import MailChannel from 'App/Channels/Mail'
import DatabaseChannel from 'App/Channels/Database'

Notification.extend('sms', () => new SmsChannel())
Notification.extend('fcm', () => new FcmChannel())
Notification.extend('mail', () => new MailChannel())
Notification.extend('database', () => new DatabaseChannel())
