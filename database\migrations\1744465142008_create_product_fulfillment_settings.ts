import BaseSchema from '@ioc:Adonis/Lucid/Schema'
import { ProductArchetype } from 'App/Enums/ProductArchetype'

export default class extends BaseSchema {
  protected tableName = 'product_fulfillment_settings'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('product_id').references('id').inTable('products').onDelete('CASCADE').nullable()
      table.enum('archetype', Object.values(ProductArchetype)).notNullable().defaultTo(ProductArchetype.PHYSICAL)
      table.boolean('is_payable').defaultTo(true)
      table.boolean('has_form').defaultTo(false)
      table.boolean('is_informational').defaultTo(false)
      table.boolean('is_deliverable').defaultTo(true)
      table.boolean('is_pickup').defaultTo(true)
      table.boolean('physical_consumption_is_onsite').defaultTo(true)
      table.boolean('is_downloadable').defaultTo(false)
      table.enum('digital_delivery_method', ['emailLink', 'inAppAccess', 'licenseKey']).nullable()
      table.boolean('is_schedulable').defaultTo(false)
      table.boolean('service_is_onsite').defaultTo(false)
      table.boolean('service_is_remote').defaultTo(true)
      table.boolean('service_is_dynamic_query').defaultTo(false)
      table.boolean('preorder_allowed').defaultTo(true)
      table.boolean('schedule_allowed').defaultTo(true)

      // Add indexes
      table.index('product_id')
      table.index('archetype')
      table.index('is_payable')
      table.index('has_form')
      table.index('is_informational')
      table.index('is_deliverable')
      table.index('is_pickup')
      table.index('physical_consumption_is_onsite')
      table.index('is_schedulable')
      table.index('service_is_dynamic_query')

      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
} 