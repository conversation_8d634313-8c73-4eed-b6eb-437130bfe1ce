import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import User from '../../Models/User'
import { bind } from '@adonisjs/route-model-binding'
// import UpdateUserValidator from '../../Validators/UpdateUserValidator'
import CreateUserValidator from '../../Validators/CreateUserValidator'

export default class UsersController {
  /**
   * @index
   * @summary List all Users
   * @description List all Users with pagination and filtering options
   * @paramQuery per number - Number of items per page
   * @paramQuery page number - Page number
   * @paramQuery order string - Order by column
   * @paramQuery sort string - Sort order (asc or desc)
   * @paramQuery s string - Search term to filter users by name, email, or phone
   * @paramQuery status string - Filter by user status (Active, Inactive, Suspended)
   * @paramQuery email string - Filter by exact email
   * @paramQuery phone string - Filter by exact phone
   * @paramQuery gender string - Filter by gender
   * @responseBody 200 - <User[]>
   */
  public async index({ request, response }: HttpContextContract) {
    const { page = 1, per = 10, order = 'createdAt', sort = 'desc', ...filters } = request.qs()

    const userQuery = User.filter(filters).preload('roles')

    const users = await userQuery.orderBy(order, sort).paginate(page, per)

    return response.json(users)
  }

  /**
   * @store
   * @summary Create a user
   * @description Create a user with their details (phone and/or email address) and password
   * @requestBody {"firstName": "", "lastName": "", "gender": "Other", "dob": "YYYY-MM-DD", "email": "", "phone": "254700000000", "details": "", "location": {}}
   */
  public async store({ request, response }: HttpContextContract) {
    const {
      title = null,
      firstName,
      lastName,
      email,
      phone: postedPhone,
      idpass,
      dob,
      role = 'customer',
      location = null,
    } = await request.validate(CreateUserValidator)

    const phone = postedPhone ? postedPhone.replace(/^\+/, '') : ''

    let user = await User.query().where('phone', phone).orWhere('email', email).first()

    if (!user) {
      user = await User.create({
        title,
        firstName,
        lastName,
        email,
        phone,
        idpass,
        dob,
        location,
        password: phone.toString(),
      })
    }

    const avatar = request.file('avatar')

    if (avatar) {
      user.avatar = Attachment.fromFile(avatar)
    }

    await user.save()
    await user.syncRoles(role)

    return response.json(user)
  }

  /**
   * @show
   * @summary Show a single User
   * @description Show a User with their details (name and details)
   * @paramPath id required number - User ID
   * @responseBody 200 - <User>
   * @response 404 - User not found
   */
  @bind()
  public async show({ response }: HttpContextContract, user: User) {
    await user.load('roles')
    await user.load('permissions')
    await user.load('devices', (dq) => dq.where('status', 'Active'))

    return response.json(user)
  }

  /**
   * @update
   *
   * @summary Update a User
   * @description Update a User with their details (name and details)
   * @paramPath id required number - User ID
   *
   * @requestBody <User>
   * @responseBody 200 - <User>
   * @response 404 - User not found
   */
  @bind()
  public async update({ request, response }: HttpContextContract, user: User) {
    const { email, phone: postedPhone, gender, details, idpass } = request.all()
    //validate(UpdateUserValidator)
    const avatar = request.file('avatar')!
    const phone = postedPhone ? postedPhone.replace(/^\+/, '') : ''

    if (avatar) {
      user.avatar = Attachment.fromFile(avatar)
    }

    await user.merge({ email, phone, gender, details, idpass }).save()

    return response.json(user)
  }

  /**
   * @destroy
   * @summary delete a user
   * @responseBody 204 - No content
   */
  @bind()
  public async destroy({ request, response }: HttpContextContract, user: User) {
    if (request.input('force')) {
      await user.forceDelete()
    } else {
      await user.delete()
    }

    return response.noContent()
  }
}
