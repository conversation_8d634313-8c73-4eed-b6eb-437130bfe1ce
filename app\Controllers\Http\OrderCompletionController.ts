import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import OrderCompletionDetector from '../../Services/OrderCompletionDetector'
import { bind } from '@adonisjs/route-model-binding'
import { schema, rules } from '@ioc:Adonis/Core/Validator'
import Order from '../../Models/Order'
import { DateTime } from 'luxon'

/**
 * Controller for managing order completion detection and automation
 */
export default class OrderCompletionController {

  /**
   * @summary Check order completion status
   * @description Check if an order should be marked as completed and update status
   * @paramPath id required string - Order ID
   * @responseBody 200 - Completion check result
   * @response 404 - Order not found
   */
  @bind()
  public async checkCompletion({ response }: HttpContextContract, order: Order) {
    try {
      const result = await OrderCompletionDetector.checkOrderCompletion(order.id)
      
      return response.json({
        order_id: order.id,
        completion_result: result,
        message: result.statusChanged ? 
          `Order status updated from ${result.previousStatus} to ${result.newStatus}` :
          'Order completion checked - no status change needed'
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to check order completion',
        details: error.message
      })
    }
  }

  /**
   * @summary Bulk check order completion
   * @description Check completion status for multiple orders
   * @requestBody Array of order IDs
   * @responseBody 200 - Bulk completion results
   */
  public async bulkCheck({ request, response }: HttpContextContract) {
    const validationSchema = schema.create({
      order_ids: schema.array().members(schema.string()),
      vendor_id: schema.string.optional()
    })

    const { order_ids, vendor_id } = await request.validate({ schema: validationSchema })

    try {
      let orderIds = order_ids

      // If vendor_id is provided, filter orders by vendor
      if (vendor_id) {
        const vendorOrders = await Order.query()
          .whereIn('id', order_ids)
          .where('vendor_id', vendor_id)
          .select('id')
          .exec()
        
        orderIds = vendorOrders.map(order => order.id)
      }

      const results = await OrderCompletionDetector.bulkCheckOrderCompletion(orderIds)
      
      const summary = {
        total_requested: order_ids.length,
        total_checked: results.length,
        completed_orders: results.filter(r => r.statusChanged).length,
        errors: results.filter(r => r.error).length
      }

      return response.json({
        summary,
        results
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to bulk check order completion',
        details: error.message
      })
    }
  }

  /**
   * @summary Get orders ready for completion
   * @description Get list of orders that are ready for completion check
   * @paramQuery vendor_id - Filter by vendor ID
   * @paramQuery limit - Limit number of results
   * @responseBody 200 - Orders ready for completion
   */
  public async getReadyOrders({ request, response }: HttpContextContract) {
    const { vendor_id, limit = 50 } = request.qs()

    try {
      let orders = await OrderCompletionDetector.getOrdersReadyForCompletion()

      // Filter by vendor if specified
      if (vendor_id) {
        orders = orders.filter(order => order.vendorId === vendor_id)
      }

      // Apply limit
      if (limit) {
        orders = orders.slice(0, parseInt(limit))
      }

      // Transform for response
      const ordersData = orders.map(order => ({
        id: order.id,
        vendor_id: order.vendorId,
        branch_id: order.branchId,
        status: order.status,
        type: order.type,
        delivery: order.delivery,
        created_at: order.createdAt,
        total_items: order.items.length,
        ready_items: order.items.filter(item => ['ready', 'served'].includes(item.status)).length,
        pending_items: order.items.filter(item => item.status === 'pending').length,
        preparing_items: order.items.filter(item => item.status === 'preparing').length
      }))

      return response.json({
        total_found: ordersData.length,
        orders: ordersData
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to get orders ready for completion',
        details: error.message
      })
    }
  }

  /**
   * @summary Run scheduled completion check
   * @description Run the scheduled completion check for all pending orders
   * @responseBody 200 - Scheduled check results
   */
  public async scheduledCheck({ response }: HttpContextContract) {
    try {
      const result = await OrderCompletionDetector.scheduledCompletionCheck()
      
      return response.json({
        message: 'Scheduled completion check completed',
        result,
        timestamp: DateTime.now().toISO()
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to run scheduled completion check',
        details: error.message
      })
    }
  }

  /**
   * @summary Get completion statistics
   * @description Get completion statistics for analytics
   * @paramQuery vendor_id - Filter by vendor ID
   * @paramQuery start_date - Start date for statistics (ISO string)
   * @paramQuery end_date - End date for statistics (ISO string)
   * @paramQuery period - Predefined period (today, week, month)
   * @responseBody 200 - Completion statistics
   */
  public async getStatistics({ request, response }: HttpContextContract) {
    const { vendor_id, start_date, end_date, period = 'week' } = request.qs()

    try {
      let startDate: DateTime | undefined
      let endDate: DateTime | undefined

      // Handle predefined periods
      if (start_date && end_date) {
        startDate = DateTime.fromISO(start_date)
        endDate = DateTime.fromISO(end_date)
      } else {
        endDate = DateTime.now()
        switch (period) {
          case 'today':
            startDate = endDate.startOf('day')
            break
          case 'week':
            startDate = endDate.minus({ weeks: 1 })
            break
          case 'month':
            startDate = endDate.minus({ months: 1 })
            break
          default:
            startDate = endDate.minus({ weeks: 1 })
        }
      }

      const stats = await OrderCompletionDetector.getCompletionStatistics(
        vendor_id,
        startDate,
        endDate
      )

      const completionRate = stats.totalOrders > 0 ? 
        Math.round((stats.completedOrders / stats.totalOrders) * 100) : 0

      return response.json({
        period: {
          start: startDate?.toISO(),
          end: endDate?.toISO(),
          period_name: period
        },
        statistics: {
          ...stats,
          completion_rate_percentage: completionRate
        },
        vendor_id: vendor_id || 'all'
      })
    } catch (error) {
      return response.status(500).json({
        error: 'Failed to get completion statistics',
        details: error.message
      })
    }
  }

  /**
   * @summary Force order completion
   * @description Manually force an order to be marked as completed (manager override)
   * @paramPath id required string - Order ID
   * @requestBody reason - Reason for manual completion
   * @responseBody 200 - Force completion result
   */
  @bind()
  public async forceCompletion({ request, response, auth }: HttpContextContract, order: Order) {
    const validationSchema = schema.create({
      reason: schema.string({ trim: true }),
      override_incomplete_items: schema.boolean.optional()
    })

    const { reason, override_incomplete_items = false } = await request.validate({ schema: validationSchema })

    try {
      // Check if user has permission to force completion (manager role)
      const user = auth.user
      if (!user) {
        return response.unauthorized({ error: 'Authentication required' })
      }

      // Load order items to check current status
      await order.load('items', (itemQuery) => {
        itemQuery.preload('modifiers')
      })

      // Check if order can be completed normally
      const normalCheck = await OrderCompletionDetector.checkOrderCompletion(order.id)
      
      if (normalCheck.orderCompleted) {
        return response.json({
          message: 'Order was already ready for completion',
          completion_result: normalCheck
        })
      }

      // Force completion with manager override
      if (override_incomplete_items) {
        const previousStatus = order.status
        order.status = 'Ready'
        
        // Add override metadata
        const meta = order.meta || {}
        meta.force_completed = {
          by_user_id: user.id,
          by_user_name: user.name,
          reason,
          timestamp: DateTime.now().toISO(),
          previous_status: previousStatus,
          incomplete_items: order.items.filter(item => !['ready', 'served'].includes(item.status)).length
        }
        order.meta = meta

        await order.save()

        return response.json({
          message: 'Order force completed successfully',
          order_id: order.id,
          previous_status: previousStatus,
          new_status: order.status,
          override_reason: reason,
          forced_by: user.name,
          timestamp: DateTime.now().toISO()
        })
      } else {
        return response.badRequest({
          error: 'Order cannot be completed - items are not ready',
          details: 'Set override_incomplete_items to true to force completion',
          incomplete_items: order.items.filter(item => !['ready', 'served'].includes(item.status)).length
        })
      }

    } catch (error) {
      return response.status(500).json({
        error: 'Failed to force order completion',
        details: error.message
      })
    }
  }
}
