import Route from '@ioc:Adonis/Core/Route'
import AutoSwagger from 'adonis-autoswagger'
import swagger from '../../../config/swagger'

// Serve the OpenAPI specification
Route.group(() => {
  Route.get('swagger', async () => AutoSwagger.docs(Route.toJSON() as any, swagger))
}).prefix('/v1')

// Serve the Swagger UI at root level for easier access
Route.get('docs', () => AutoSwagger.ui('/v1/swagger', swagger))
