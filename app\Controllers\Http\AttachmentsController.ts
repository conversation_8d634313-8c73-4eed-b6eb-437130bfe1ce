import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment as Upload } from '@ioc:Adonis/Addons/AttachmentLite'
import Attachment from '../../Models/Attachment'
import { bind } from '@adonisjs/route-model-binding'

export default class AttachmentsController {
  /**
   * @index
   * @summary List all attachments
   * @description List all attachments, paginated
   * @paramUse (filterable)
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by field
   * @paramQuery sort - Sort order (asc, desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    const attachmentQuery = Attachment.filter(filters)

    return await attachmentQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create an attachment
   * @description Create an attachment with their details (name and details)
   * @requestBody {"name": "", "details": "", attachableId: "", attachableType: ""}
   * @responseBody 200 - <Attachment>
   */

  public async store({ request }: HttpContextContract) {
    const { name, details, attachableId, attachableType } = request.all()
    const attachment = new Attachment()

    attachment.fill({ name, details, attachableId, attachableType })

    const path = request.file('path')

    if (path) {
      attachment.path = Upload.fromFile(path)
    }

    return await attachment.save()
  }

  @bind()

  /**
   * @show
   * @summary Show a single attachment
   * @description Show an attachment
   * @paramPath id required number - Attachment Id
   * @responseBody 200 - <Attachment>
   * @response 404 - Address not found
   */
  public async show({ response }: HttpContextContract, attachment: Attachment) {
    return response.json(attachment)
  }

  /**
   * @update
   * @summary Update attachment
   * @description Update attachment with details (name and details)
   * @paramPath id required number - Attachment ID
   * @requestBody <Attachment>
   * @responseBody 200 - <Attachment>
   * @response 404 - Attachment not found
   */

  public async update({ params, request, response }: HttpContextContract) {
    const attachment = await Attachment.findOrFail(params.attachmentId)
    const input = request.all()
    const path = request.file('path')!

    delete input.name
    delete input.path

    if (path) {
      input.path = Upload.fromFile(path)
    }

    await attachment.merge(input).save()

    return response.json(attachment)
  }

  /**
   * @destroy
   * @responseBody 204 - No content
   */

  public async destroy({ params }: HttpContextContract) {
    const attachment = await Attachment.findOrFail(params.attachmentId)
    return attachment.delete()
  }
}
