import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Speciality from '../../Models/Speciality'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Speciality management
 * @version 1.0.0
 * @description Speciality management for the application
 */
export default class SpecialitiesController {
  /**
   * @index
   * @summary List all specialities
   * @description List all specialities, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @responseBody 200 - <Speciality>.paginated().with(relations) - Paginated list of specialities
   *
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc', ...filters } = request.qs()
    const specialityQuery = Speciality.filter(filters).preload('service')

    return await specialityQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a speciality
   * @description Create a speciality with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Speciality>
   */
  public async store({ request, response }: HttpContextContract) {
    const { name, details, serviceId } = request.all()
    const speciality = new Speciality()

    speciality.fill({ name, details, serviceId })

    const image = request.file('image')
    if (image) {
      speciality.merge({ image: Attachment.fromFile(image) })
    }

    await speciality.save()

    return response.json(speciality)
  }

  @bind()
  /**
   * @show
   * @summary Show a single speciality
   * @description Show a speciality with their details (name and details)
   * @paramPath id required number - Speciality ID
   * @responseBody 200 - <Speciality>
   * @response 404 - Speciality not found
   */
  public async show({ response }: HttpContextContract, speciality: Speciality) {
    await speciality.load('service')

    return response.json(speciality)
  }

  @bind()
  /**
   * @update
   * @summary Update a speciality
   * @description Update a speciality with their details (name and details)
   * @paramPath id required number - Speciality ID
   * @requestBody <Speciality>
   * @responseBody 200 - <Speciality>
   * @response 404 - Speciality not found
   */
  public async update({ request, response }: HttpContextContract, speciality: Speciality) {
    const { name, details, serviceId } = request.all()

    await speciality.merge({ name, details, serviceId }).save()

    const image = request.file('image')
    if (image) {
      await speciality
        .merge({
          image: Attachment.fromFile(image),
        })
        .save()
    }

    return response.json(speciality)
  }

  @bind()
  public async destroy(_: HttpContextContract, speciality: Speciality) {
    return await speciality.delete()
  }
}
