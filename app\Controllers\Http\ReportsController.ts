import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { Attachment } from '@ioc:Adonis/Addons/AttachmentLite'
import Report from '../../Models/Report'
import { bind } from '@adonisjs/route-model-binding'

/**
 * @name Report management
 * @version 1.0.0
 * @description Report management for the application
 */
export default class ReportsController {
  /**
   * @index
   * @summary List all reports
   * @description List all reports, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   */
  public async index({ request }: HttpContextContract) {
    const { per = 10, page = 1, order = 'createdAt', sort = 'asc' } = request.qs()
    const reportQuery = Report.query()

    return await reportQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a report
   * @description Create a report with their details (name and details)
   * @requestBody {"name": "", "details": ""}
   * @responseBody 200 - <Report>
   */
  public async store({ request, response }: HttpContextContract) {
    const { userId, details } = request.all()
    const report = new Report()

    report.fill({ userId, details })

    await report.save()

    return response.json(report)
  }

  @bind()
  /**
   * @show
   * @summary Show a single report
   * @description Show a report with their details (name and details)
   * @paramPath id required number - Report ID
   * @responseBody 200 - <Report>
   * @response 404 - Report not found
   */
  public async show({ response }: HttpContextContract, report: Report) {
    return response.json(report)
  }

  @bind()
  /**
   * @update
   * @summary Update a report
   * @description Update a report with their details (name and details)
   * @paramPath id required number - Report ID
   * @requestBody <Report>
   * @responseBody 200 - <Report>
   * @response 404 - Report not found
   */
  public async update({ request, response }: HttpContextContract, report: Report) {
    const { image: uploadedImage, ...input } = request.all()

    const upload = request.file('image')
    if (upload) {
      input.image = Attachment.fromFile(upload)
    }

    await report.merge(input).save()

    return response.json(report)
  }

  @bind()

  /**
   * @destroy
   * @summary delete a Report
   * @reponseBody 204 - No content
   */
  public async destroy(_: HttpContextContract, report: Report) {
    return await report.delete()
  }
}
