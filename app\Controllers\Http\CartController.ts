import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';
import Database from '@ioc:Adonis/Lucid/Database';
import Product from 'App/Models/Product';


// Define a simple type for cart items for better clarity (Optional but recommended)
// interface CartItem {
//   id: string | number; // Adjust based on your actual product ID type
//   price: string | number; // Adjust based on actual type, handle parsing carefully
//   count: number;
//   // Include other necessary fields from Product model potentially stored in cart
//   name?: string;
//   ref?: string;
//   product_category_id?: string;
//   vendor_id?: string;
//   branch_id?: string;
//   service_id?: string;
//   meta?: Record<string, any>;
//   // Add any other fields you expect in the cart_items JSON
// }

export default class CartController {

    //Get cart
    /**
     * @swagger
     * /v1/cart/getcart:
     *   get:
     *     summary: Get cart items with optional vendor filtering
     *     description: Retrieves the user's cart items with optional vendor filtering
     *     tags:
     *       - CART
     *     parameters:
     *       - in: query
     *         name: page
     *         schema:
     *           type: number
     *         description: Page number for pagination
     *       - in: query
     *         name: perPage
     *         schema:
     *           type: number
     *         description: Number of items per page
     *       - in: query
     *         name: vendorId
     *         schema:
     *           type: string
     *         description: Filter cart items by vendor ID
     *     responses:
     *       200:
     *         description: Success
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 status:
     *                   type: string
     *                 cart:
     *                   type: object
     *                   properties:
     *                     id:
     *                       type: string
     *                     user_id:
     *                       type: string
     *                     total:
     *                       type: string
     *                     cart_items:
     *                       type: array
     *                       items:
     *                         type: object
     *                         properties:
     *                           id:
     *                             type: string
     *                           price:
     *                             type: string
     *                           count:
     *                             type: number
     *                           name:
     *                             type: string
     *                           description:
     *                             type: string
     *                           image:
     *                             type: string
     *                             nullable: true
     *                           vendorId:
     *                             type: string
     *                           serviceId:
     *                             type: string
     *                           productCategoryId:
     *                             type: string
     *                           vendor:
     *                             type: object
     *                             properties:
     *                               id:
     *                                 type: string
     *                               name:
     *                                 type: string
     *                               image:
     *                                 type: string
     *                                 nullable: true
     *                           meta:
     *                             type: object
     *                     pagination:
     *                       type: object
     *                       properties:
     *                         page:
     *                           type: number
     *                         perPage:
     *                           type: number
     *                         total:
     *                           type: number
     *                         lastPage:
     *                           type: number
     *       401:
     *         description: Unauthorized - Invalid or missing token
     *       500:
     *         description: Internal server error
     *     security:
     *       - BearerAuth: []
     */
    public async getCart({ auth, response, request }: HttpContextContract) {
        const user = auth.user;
        const page = Number(request.input('page', 1));
        const perPage = Number(request.input('perPage', 10));
        const vendorId = request.input('vendorId');
        const includeFulfillment = request.input('includeFulfillment', false);

        // Get the cart
        const cart = await Database.from('cart')
            .select('*')
            .where('user_id', user!.id)
            .first();

        let parsedCartItems: any[] = [];
        if (cart && cart.cart_items) {
            try {
                parsedCartItems = JSON.parse(cart.cart_items);
            } catch (e) {
                console.error('Failed to parse cart items:', e);
            }
        }

        // Pagination logic
        const totalItems = parsedCartItems.length;
        const paginatedItems = parsedCartItems.slice((page - 1) * perPage, page * perPage);
        const productIds = paginatedItems.map((item) => item.id);

        // Fetch product details
        const products = await Product.query()
            .whereIn('id', productIds)
            .preload('vendor')
            .preload('branch')
            .preload('category')
            .preload('service');

        // Map product details to cart items
        let enrichedItems = paginatedItems.map((cartItem) => {
            const product = products.find((p) => p.id === cartItem.id);
            if (!product) {
                // If product not found, return the cart item as is
                return {
                    id: cartItem.id,
                    price: cartItem.price,
                    count: cartItem.count,
                    name: cartItem.name || 'Product not found',
                    description: cartItem.description || '',
                    image: null,
                    vendorId: cartItem.vendor_id,
                    serviceId: cartItem.service_id,
                    productCategoryId: cartItem.product_category_id,
                    vendor: null,
                    meta: cartItem.meta || {},
                };
            }
            return {
                id: product.id,
                price: cartItem.price,
                count: cartItem.count,
                name: product.name,
                description: product.details,
                image: product.image ? product.image.url : null,
                vendorId: product.vendorId,
                serviceId: product.serviceId,
                productCategoryId: product.productCategoryId,
                vendor: product.vendor
                    ? {
                        id: product.vendor.id,
                        name: product.vendor.name,
                        image: product.vendor.logo ? product.vendor.logo.url : null,
                        branchId: product.branchId
                    }
                    : null,
                meta: cartItem.meta || {},
            };
        });

        // Filter by vendor if vendorId is provided - moved after enrichment
        if (vendorId) {
            enrichedItems = enrichedItems.filter(item => item.vendorId === vendorId);
        }

        // Add fulfillment options if requested
        let fulfillmentOptions = null;
        if (includeFulfillment && enrichedItems.length > 0) {
            try {
                fulfillmentOptions = await this.calculateCartFulfillmentOptions(enrichedItems);
            } catch (error) {
                console.error('Failed to calculate fulfillment options:', error);
            }
        }

        // Response structure
        return response.status(200).json({
            status: 'get cart items',
            cart: cart
                ? {
                    id: cart.id,
                    user_id: cart.user_id,
                    total: cart.total,
                    cart_items: enrichedItems,
                    fulfillmentOptions,
                    pagination: {
                        page,
                        perPage,
                        total: totalItems,
                        lastPage: Math.ceil(totalItems / perPage),
                    },
                }
                : null,
        });
    }

    //Post to cart cart
    /**
     * @swagger
     * /v1/cart/postcart:
     *   post:
     *     summary: Add or update items in cart
     *     description: Add or update items in the user's cart
     *     tags:
     *       - CART
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: array
     *             items:
     *               type: object
     *               required:
     *                 - id
     *                 - price
     *                 - count
     *               properties:
     *                 id:
     *                   type: string
     *                   description: Product ID
     *                 price:
     *                   type: string
     *                   description: Product price
     *                 count:
     *                   type: number
     *                   description: Quantity of the product
     *                 name:
     *                   type: string
     *                   description: Product name
     *                 ref:
     *                   type: string
     *                   description: Product reference
     *                 product_category_id:
     *                   type: string
     *                   description: Product category ID
     *                 vendor_id:
     *                   type: string
     *                   description: Vendor ID
     *                 branch_id:
     *                   type: string
     *                   description: Branch ID
     *                 service_id:
     *                   type: string
     *                   description: Service ID
     *                 meta:
     *                   type: object
     *                   description: Additional metadata
     *     responses:
     *       200:
     *         description: Success
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 status:
     *                   type: string
     *                 cart_total:
     *                   type: number
     *                 cart_items:
     *                   type: array
     *                   items:
     *                     type: object
     *       400:
     *         description: Bad request - Invalid input data
     *       401:
     *         description: Unauthorized - Invalid or missing token
     *       500:
     *         description: Internal server error
     *     security:
     *       - BearerAuth: []
     */
    public async postCart({ auth, request, response }: HttpContextContract) {
        const trx = await Database.transaction()

        try {
            const user = auth.user;
            let p = request.body();

            if (!Array.isArray(p)) {
                await trx.rollback();
                return response.status(400).json({ status: 'error', message: 'Request body must be an array of cart items.' });
            }

            console.log("IN: " + JSON.stringify(p) );

            // It seems product IDs are strings based on Product.ts.Model.txt (ulid)
            let p1 = p.map((element) => element['id']);
            console.log(JSON.stringify(p1));

            const cart = await Database
                .from('cart')
                .select('id', 'cart_items', 'total')
                .where('user_id', user!.id)
                .first();


            if(!cart){ //insert cart for user
                var total = 0.0;
                // FIX: Explicitly type the array
                const newCartItems: any[] = []; // Use `CartItem[]` if defined above
                // const newCartItems: CartItem[] = []; // Preferred if interface is defined

                for(const item of p) {
                    if (typeof item.id === 'undefined' || typeof item.price === 'undefined' || typeof item.count === 'undefined') {
                       await trx.rollback();
                       return response.status(400).json({ status: 'error', message: 'Each cart item must have id, price, and count.' });
                    }
                    const price = parseFloat(item.price);
                    const count = parseInt(item.count, 10);

                    if (isNaN(price) || isNaN(count)) {
                        await trx.rollback();
                        return response.status(400).json({ status: 'error', message: `Invalid price or count for item ID ${item.id}` });
                    }

                    if (count <= 0) continue;

                    const cleanItem = { ...item };
                    delete cleanItem.user_id;

                    // FIX: Now assignable because newCartItems is typed as any[] (or CartItem[])
                    newCartItems.push(cleanItem);
                    total += price * count;
                }

                if (newCartItems.length > 0) {
                    await Database
                        .table('cart')
                        .useTransaction(trx)
                        .insert({
                            user_id: user!.id,
                            cart_items: JSON.stringify(newCartItems),
                            total: total.toString()
                    });
                } else {
                     await Database
                        .table('cart')
                        .useTransaction(trx)
                        .insert({
                            user_id: user!.id,
                            cart_items: '[]',
                            total: '0'
                    });
                }

                await trx.commit();
                return response.status(200).json({ status: 'done', cart_total: total, cart_items: newCartItems });

            } else { //update cart for user
                 // FIX: Explicitly type the array
                 let existingItems: any[] = []; // Use `CartItem[]` if defined above
                 // let existingItems: CartItem[] = []; // Preferred if interface is defined
                try {
                    const parsed = JSON.parse(cart.cart_items);
                    if (Array.isArray(parsed)) {
                        existingItems = parsed;
                    }
                } catch (e) {
                    console.error("Failed to parse existing cart items:", e);
                    // Keep existingItems as empty array
                }

                let updatedTotal = 0.0;
                // Use Map<string, any> or Map<string, CartItem>
                const updatedItemsMap = new Map<string, any>();

                for (const item of existingItems) {
                    if (item && typeof item.id !== 'undefined') {
                       updatedItemsMap.set(String(item.id), item); // Ensure key is string
                    }
                }

                for (const newItem of p) {
                    if (typeof newItem.id === 'undefined' || typeof newItem.price === 'undefined' || typeof newItem.count === 'undefined') {
                        await trx.rollback();
                        return response.status(400).json({ status: 'error', message: 'Each incoming cart item must have id, price, and count.' });
                    }

                    const itemId = String(newItem.id); // Ensure key is string
                    const newCount = parseInt(newItem.count, 10);
                    const newPrice = parseFloat(newItem.price);

                     if (isNaN(newCount) || isNaN(newPrice)) {
                        await trx.rollback();
                        return response.status(400).json({ status: 'error', message: `Invalid price or count for incoming item ID ${itemId}` });
                    }

                    const cleanNewItem = { ...newItem };
                    delete cleanNewItem.user_id;

                    if (updatedItemsMap.has(itemId)) {
                        const existingItem = updatedItemsMap.get(itemId)!; // Use '!' as we know it exists
                        if (newCount <= 0) {
                            updatedItemsMap.delete(itemId);
                        } else {
                           existingItem.count = newCount;
                           existingItem.price = newPrice;
                           // Potentially update other fields like name, etc.
                           // existingItem.name = cleanNewItem.name;
                           updatedItemsMap.set(itemId, existingItem);
                        }
                    } else if (newCount > 0) {
                        updatedItemsMap.set(itemId, cleanNewItem);
                    }
                }

                 const finalCartItems = Array.from(updatedItemsMap.values());
                 for (const item of finalCartItems) {
                    const price = parseFloat(item.price);
                    const count = parseInt(item.count, 10);
                    if (!isNaN(price) && !isNaN(count)) {
                       updatedTotal += price * count;
                    }
                 }

                 await Database
                    .query()
                    .useTransaction(trx)
                    .from('cart')
                    .where('user_id', user!.id)
                    .update({
                        cart_items: JSON.stringify(finalCartItems),
                        total: updatedTotal.toString()
                    });

                 await trx.commit();

                 return response.status(200).json({
                    status: 'done',
                    cart_total: updatedTotal,
                    cart_items: finalCartItems
                 });
            }
        } catch (error) {
            await trx.rollback();
            console.error("Cart Update Error:", error);
            return response.status(500).json({ status: 'error', message: 'An internal server error occurred.' });
        }
    }

    //input    [{'id':'', 'price':'', 'ref':'', 'product_category_id':'', 'user_id':'', 'vendor_id':'', 'branch_id':'', 'service_id':'', 'name':'', 'meta':''}] from model Product
    // FIX: Correctly destructure 'request' to '_request' to mark as unused
    public async postCartOld({ auth, response, request: _request }: HttpContextContract) {
        const trx = await Database.transaction()

        try {
            const user = auth.user;

            var products = await Database.from('products')
                // Ensure price is selected correctly - Model shows it as 'price' (number)
                .select('id', 'price', 'ref', 'product_category_id', 'user_id', 'vendor_id', 'branch_id', 'service_id', 'name', 'meta')
                .where('active', true)
                .limit(2);

            var total = 0.0;
            // FIX: Explicitly type the array
            const cartItemsForDb: any[] = []; // Use `CartItem[]` if defined above
            // const cartItemsForDb: CartItem[] = []; // Preferred if interface is defined

            for (const product of products) {
                const itemWithCount = { ...product, count: 2 };
                delete itemWithCount.user_id;

                // FIX: Now assignable because cartItemsForDb is typed as any[] (or CartItem[])
                cartItemsForDb.push(itemWithCount);

                // Product price is number according to model, no need for parseFloat here
                const price = product.price; // Already a number
                if (typeof price === 'number' && !isNaN(price)) {
                      total += price * 2;
                 } else {
                     console.warn(`Product ID ${product.id} has invalid price: ${product.price}`);
                 }
            }

            console.warn(`postCartOld is potentially overwriting cart for user ID: ${user!.id}`);

            await Database
                .table('cart')
                .useTransaction(trx)
                // Check if cart exists first if overwrite is not desired
                // .onConflict('user_id').merge() // Or use conflict resolution if supported/desired
                .insert({
                    user_id: user!.id,
                    cart_items: JSON.stringify(cartItemsForDb),
                    total: total.toString()
            });

            await trx.commit()

            console.log('post cart old');
            return response.status(200).json({ status: 'post cart old - done', cart_total: total, cart_items: cartItemsForDb });

        } catch (error) {
            await trx.rollback();
            console.error("postCartOld Error:", error);
            return response.status(500).json({ status: 'error', message: 'An internal server error occurred in postCartOld.' });
        }
    }

    /**
     * Calculate fulfillment options for cart items
     */
    private async calculateCartFulfillmentOptions(cartItems: any[]) {
        const Product = (await import('App/Models/Product')).default;
        const ProductFulfillmentSetting = (await import('App/Models/ProductFulfillmentSetting')).default;

        // Get unique product IDs
        const productIds = [...new Set(cartItems.map(item => item.id))];

        // Get products with fulfillment settings
        const products = await Product.query()
            .whereIn('id', productIds)
            .preload('fulfillmentSettings')
            .exec();

        // Calculate available fulfillment options
        const options = {
            delivery: { available: true, reasons: [] },
            pickup: { available: true, reasons: [] },
            dinein: { available: true, reasons: [] },
            download: { available: true, reasons: [] },
            schedule: { available: true, reasons: [] },
            preorder: { available: true, reasons: [] }
        };

        // Check each product's fulfillment capabilities
        for (const product of products) {
            const settings = product.fulfillmentSettings;

            if (!settings) {
                // Use defaults based on product type
                if (product.type === 'Digital') {
                    options.delivery.available = false;
                    options.pickup.available = false;
                    options.dinein.available = false;
                } else if (product.type === 'Service') {
                    options.delivery.available = false;
                    options.dinein.available = false;
                }
                continue;
            }

            // Apply product-specific constraints
            if (!settings.isDeliverable) {
                options.delivery.available = false;
                options.delivery.reasons.push(`${product.name} is not deliverable`);
            }

            if (!settings.isPickup) {
                options.pickup.available = false;
                options.pickup.reasons.push(`${product.name} is not available for pickup`);
            }

            if (!settings.physicalConsumptionIsOnsite) {
                options.dinein.available = false;
                options.dinein.reasons.push(`${product.name} is not available for dine-in`);
            }

            if (!settings.isDownloadable) {
                options.download.available = false;
                options.download.reasons.push(`${product.name} is not downloadable`);
            }

            if (!settings.scheduleAllowed) {
                options.schedule.available = false;
                options.schedule.reasons.push(`${product.name} cannot be scheduled`);
            }

            if (!settings.preorderAllowed) {
                options.preorder.available = false;
                options.preorder.reasons.push(`${product.name} cannot be preordered`);
            }
        }

        return options;
    }
}
