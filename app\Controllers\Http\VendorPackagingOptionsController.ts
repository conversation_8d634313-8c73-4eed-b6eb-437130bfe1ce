import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { bind } from '@adonisjs/route-model-binding'
import Vendor from 'App/Models/Vendor'
import PackagingOption from 'App/Models/PackagingOption'
import PackagingOptionValidator, { PackagingOptionUpdateValidator } from 'App/Validators/PackagingOptionValidator'

/**
 * @name Vendor Packaging Options management
 * @version 1.0.0
 * @description Vendor packaging options management for the application
 */
export default class VendorPackagingOptionsController {
  /**
   * @index
   * @summary List all packaging options for a vendor
   * @description List all packaging options for a specific vendor, paginated
   *
   * @paramQuery per - Number of items per page
   * @paramQuery page - Page number
   * @paramQuery order - Order by column
   * @paramQuery sort - Sort order (asc or desc)
   * @paramQuery active - Filter by active status (true/false)
   * @paramQuery name - Filter by name (partial match)
   * @paramQuery priceMin - Filter by minimum price
   * @paramQuery priceMax - Filter by maximum price
   * @responseBody 200 - <PackagingOption[]>
   * @response 403 - Forbidden - User not authorized for this vendor
   */
  @bind()
  public async index({ request }: HttpContextContract, vendor: Vendor) {
    const { per = 25, page = 1, order = 'createdAt', sort = 'desc', ...filters } = request.qs()
    
    const packagingOptionsQuery = PackagingOption.filter(filters)
      .where('vendorId', vendor.id)

    return await packagingOptionsQuery.orderBy(order, sort).paginate(page, per)
  }

  /**
   * @store
   * @summary Create a packaging option
   * @description Create a new packaging option for a specific vendor. Only the vendor owner or admin can create packaging options.
   * @requestBody {
   *   "name": "Gift Wrapping",
   *   "price": 250,
   *   "description": "Includes premium paper and a personalized card.",
   *   "active": true
   * }
   * @responseBody 200 - <PackagingOption>
   * @response 400 - Bad Request - Validation errors
   * @response 403 - Forbidden - User not authorized for this vendor
   */
  @bind()
  public async store({ request, response, auth }: HttpContextContract, vendor: Vendor) {
    try {
      // Validate request data
      const payload = await request.validate(PackagingOptionValidator)

      // Check if vendor belongs to authenticated user (authorization)
      if (vendor.userId !== auth.user?.id) {
        // Check if user is an admin
        await auth.user!.load('roles')
        const isAdmin = auth.user!.roles.some(role => role.name.toLowerCase() === 'admin')

        if (!isAdmin) {
          return response.forbidden({
            message: 'You are not authorized to create packaging options for this vendor'
          })
        }
      }

      const packagingOption = await PackagingOption.create({
        name: payload.name,
        description: payload.description,
        price: payload.price,
        vendorId: vendor.id,
        active: payload.active ?? true,
      })

      return response.json(packagingOption)
    } catch (error) {
      if (error.messages) {
        // Validation error
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages
        })
      }

      return response.internalServerError({
        message: 'Failed to create packaging option',
        error: error.message
      })
    }
  }

  /**
   * @show
   * @summary Get a specific packaging option
   * @description Get details of a specific packaging option for a vendor
   * @paramPath vendorId required string - Vendor ID
   * @paramPath id required string - Packaging Option ID
   * @responseBody 200 - <PackagingOption>
   * @response 404 - Not Found - Packaging option not found for this vendor
   */
  @bind()
  public async show({ response }: HttpContextContract, vendor: Vendor, packagingOption: PackagingOption) {
    // Ensure the packaging option belongs to the vendor
    if (packagingOption.vendorId !== vendor.id) {
      return response.notFound({
        message: 'Packaging option not found for this vendor'
      })
    }

    return response.json(packagingOption)
  }

  /**
   * @update
   * @summary Update a packaging option
   * @description Update details of an existing packaging option. Only the vendor owner or admin can update packaging options.
   * @paramPath vendorId required string - Vendor ID
   * @paramPath id required string - Packaging Option ID
   * @requestBody {
   *   "name": "Premium Gift Wrapping",
   *   "price": 350,
   *   "description": "Updated description",
   *   "active": true
   * }
   * @responseBody 200 - <PackagingOption>
   * @response 400 - Bad Request - Validation errors
   * @response 403 - Forbidden - User not authorized for this vendor
   * @response 404 - Not Found - Packaging option not found for this vendor
   */
  @bind()
  public async update({ request, response, auth }: HttpContextContract, vendor: Vendor, packagingOption: PackagingOption) {
    try {
      // Ensure the packaging option belongs to the vendor
      if (packagingOption.vendorId !== vendor.id) {
        return response.notFound({
          message: 'Packaging option not found for this vendor'
        })
      }

      // Check authorization
      if (vendor.userId !== auth.user?.id) {
        await auth.user!.load('roles')
        const isAdmin = auth.user!.roles.some(role => role.name.toLowerCase() === 'admin')

        if (!isAdmin) {
          return response.forbidden({
            message: 'You are not authorized to update packaging options for this vendor'
          })
        }
      }

      // Validate request data
      const payload = await request.validate(PackagingOptionUpdateValidator)

      packagingOption.merge({
        ...(payload.name !== undefined && { name: payload.name }),
        ...(payload.description !== undefined && { description: payload.description }),
        ...(payload.price !== undefined && { price: payload.price }),
        ...(payload.active !== undefined && { active: payload.active }),
      })

      await packagingOption.save()

      return response.json(packagingOption)
    } catch (error) {
      if (error.messages) {
        // Validation error
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages
        })
      }

      return response.internalServerError({
        message: 'Failed to update packaging option',
        error: error.message
      })
    }
  }

  /**
   * @destroy
   * @summary Delete a packaging option
   * @description Remove a packaging option from a vendor's library. Cannot delete if associated with products. Only the vendor owner or admin can delete packaging options.
   * @paramPath vendorId required string - Vendor ID
   * @paramPath id required string - Packaging Option ID
   * @responseBody 204 - No content
   * @response 400 - Bad Request - Cannot delete packaging option associated with products
   * @response 403 - Forbidden - User not authorized for this vendor
   * @response 404 - Not Found - Packaging option not found for this vendor
   */
  @bind()
  public async destroy({ response, auth }: HttpContextContract, vendor: Vendor, packagingOption: PackagingOption) {
    try {
      // Ensure the packaging option belongs to the vendor
      if (packagingOption.vendorId !== vendor.id) {
        return response.notFound({
          message: 'Packaging option not found for this vendor'
        })
      }

      // Check authorization
      if (vendor.userId !== auth.user?.id) {
        await auth.user!.load('roles')
        const isAdmin = auth.user!.roles.some(role => role.name.toLowerCase() === 'admin')

        if (!isAdmin) {
          return response.forbidden({
            message: 'You are not authorized to delete packaging options for this vendor'
          })
        }
      }

      // Check if packaging option is being used by any products
      await packagingOption.load('products')
      if (packagingOption.products && packagingOption.products.length > 0) {
        return response.badRequest({
          message: 'Cannot delete packaging option that is currently associated with products',
          details: `This packaging option is used by ${packagingOption.products.length} product(s). Please remove it from all products before deleting.`
        })
      }

      await packagingOption.delete()
      return response.noContent()
    } catch (error) {
      return response.internalServerError({
        message: 'Failed to delete packaging option',
        error: error.message
      })
    }
  }
}
