import type { JobHandlerContract, Job } from '@ioc:Rlanz/Queue'
import User from 'App/Models/User'
import Hotelplus from 'App/Services/Hotelplus'
import Redis from '@ioc:Adonis/Addons/Redis'
import Branch from 'App/Models/Branch'
import { string } from '@ioc:Adonis/Core/Helpers'

export type SyncUsersPayload = {
  channel: string
  vendor: string
}

export default class SyncUsers implements JobHandlerContract {
  constructor(public job: Job) {
    this.job = job
  }

  /**
   * Base Entry point
   */
  public async handle(payload: SyncUsersPayload) {
    let response: any[] = []
    try {
      switch (payload.channel) {
        case 'hotelplus':
          const branch = await Branch.find(payload.vendor)

          const settings = await branch
            ?.related('settings')
            .query()
            .where('name', 'hotelplus')
            .first()

          const hotelplus = new Hotelplus(payload.vendor, settings?.options)

          let lastUserSaved = Number(await Redis.get(`lastUserSaved${payload.vendor}`)) || 1

          response = await hotelplus.syncStaff(lastUserSaved)

          console.info('Syncing Users', response)

          for (const user of response) {
            const waiter = await User.updateOrCreate(
              {
                email: user.email,
              },
              { ...user, firstName: string.capitalCase(user.firstName) }
            )

            await waiter.related('stations').attach({
              [payload.vendor]: { vendor_id: branch?.vendorId, identifier: user.meta.ref },
            })

            await Redis.set(`lastUserSaved${payload.vendor}`, lastUserSaved + 1)
          }

          break
        case 'other':
          break
        default:
          throw new Error('Vendor not found')
      }
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * This is an optional method that gets called if it exists when the retries has exceeded and is marked failed.
   */
  public async failed() {}
}
