import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany, manyToMany, BelongsTo, HasMany, ManyToMany, computed } from '@ioc:Adonis/Lucid/Orm'
import Vendor from './Vendor'
import Branch from './Branch'
import OrderItem from './OrderItem'
import User from './User'

export default class Department extends BaseModel {
  public static selfAssignPrimaryKey = true
  public static table = 'departments'

  @column({ isPrimary: true })
  public id: string

  @column()
  public vendorId: string

  @column()
  public branchId: string

  @column()
  public name: string

  @column()
  public description: string | null

  @column()
  public active: boolean

  // Workflow and timing settings
  @column()
  public averagePreparationTime: number | null

  @column()
  public maxConcurrentOrders: number | null

  @column()
  public priorityLevel: number

  // Working hours and scheduling
  @column()
  public workingHours: Record<string, any> | null

  @column()
  public breakTimes: Record<string, any> | null

  // Department configuration
  @column()
  public workflowSettings: Record<string, any> | null

  @column()
  public notificationSettings: Record<string, any> | null

  @column()
  public meta: Record<string, any> | null

  // Performance tracking
  @column()
  public efficiencyRating: number | null

  @column()
  public totalOrdersCompleted: number

  @column()
  public averageCompletionTime: number | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  // Relationships
  @belongsTo(() => Vendor)
  public vendor: BelongsTo<typeof Vendor>

  @belongsTo(() => Branch)
  public branch: BelongsTo<typeof Branch>

  @hasMany(() => OrderItem, { foreignKey: 'departmentId' })
  public orderItems: HasMany<typeof OrderItem>

  @manyToMany(() => User, {
    pivotTable: 'department_staff',
    pivotColumns: ['role', 'active', 'is_primary_department', 'skill_level', 'performance_rating']
  })
  public staff: ManyToMany<typeof User>

  // Computed properties for workload calculation
  @computed()
  public get currentWorkload(): number {
    if (!this.orderItems) return 0
    return this.orderItems.filter(item => 
      ['pending', 'preparing'].includes(item.status)
    ).length
  }

  @computed()
  public get isAtCapacity(): boolean {
    if (!this.maxConcurrentOrders) return false
    return this.currentWorkload >= this.maxConcurrentOrders
  }

  @computed()
  public get workloadPercentage(): number {
    if (!this.maxConcurrentOrders) return 0
    return Math.min(100, (this.currentWorkload / this.maxConcurrentOrders) * 100)
  }

  @computed()
  public get activeStaffCount(): number {
    if (!this.staff) return 0
    return this.staff.filter(staff => staff.$pivot.active).length
  }

  @computed()
  public get isOperational(): boolean {
    return this.active && this.activeStaffCount > 0 && this.isWithinWorkingHours
  }

  @computed()
  public get isWithinWorkingHours(): boolean {
    if (!this.workingHours) return true // Always operational if no hours set
    
    const now = DateTime.now()
    const dayOfWeek = now.weekdayLong?.toLowerCase()
    const currentTime = now.toFormat('HH:mm')
    
    const todayHours = this.workingHours[dayOfWeek]
    if (!todayHours || !todayHours.open || !todayHours.close) return false
    
    return currentTime >= todayHours.open && currentTime <= todayHours.close
  }

  @computed()
  public get averageItemsPerHour(): number {
    if (!this.averageCompletionTime || this.averageCompletionTime === 0) return 0
    return Math.round(60 / this.averageCompletionTime)
  }

  @computed()
  public get estimatedWaitTime(): number {
    if (!this.averagePreparationTime || this.currentWorkload === 0) return 0
    return this.averagePreparationTime * this.currentWorkload
  }

  @computed()
  public get performanceStatus(): 'excellent' | 'good' | 'average' | 'poor' | 'unknown' {
    if (!this.efficiencyRating) return 'unknown'
    
    if (this.efficiencyRating >= 4.5) return 'excellent'
    if (this.efficiencyRating >= 3.5) return 'good'
    if (this.efficiencyRating >= 2.5) return 'average'
    return 'poor'
  }

  // Business logic methods
  public async assignStaff(userId: string, role: string = 'staff', skillLevel: number = 1): Promise<void> {
    await this.related('staff').attach({
      [userId]: {
        role,
        active: true,
        skill_level: skillLevel,
        is_primary_department: false
      }
    })
  }

  public async removeStaff(userId: string): Promise<void> {
    await this.related('staff').detach([userId])
  }

  public async updateStaffRole(userId: string, role: string): Promise<void> {
    await this.related('staff').pivotQuery().where('user_id', userId).update({ role })
  }

  public async getAvailableStaff(): Promise<User[]> {
    await this.load('staff')
    return this.staff.filter(staff => 
      staff.$pivot.active && 
      this.isStaffAvailable(staff)
    )
  }

  private isStaffAvailable(staff: User): boolean {
    // Check if staff is within their availability schedule
    // This would integrate with shift management
    return true // Simplified for now
  }

  public async calculateEfficiency(): Promise<number> {
    // Calculate efficiency based on completion times vs estimates
    await this.load('orderItems')
    
    const completedItems = this.orderItems.filter(item => 
      item.status === 'served' && 
      item.actualPreparationTime && 
      item.estimatedPreparationTime
    )

    if (completedItems.length === 0) return 0

    const efficiencyScores = completedItems.map(item => {
      const efficiency = item.estimatedPreparationTime! / item.actualPreparationTime!
      return Math.min(5, Math.max(0, efficiency)) // Cap between 0 and 5
    })

    const averageEfficiency = efficiencyScores.reduce((sum, score) => sum + score, 0) / efficiencyScores.length
    
    this.efficiencyRating = Math.round(averageEfficiency * 100) / 100
    await this.save()
    
    return this.efficiencyRating
  }

  public async updatePerformanceMetrics(): Promise<void> {
    await this.load('orderItems')
    
    const completedItems = this.orderItems.filter(item => item.status === 'served')
    this.totalOrdersCompleted = completedItems.length

    if (completedItems.length > 0) {
      const totalTime = completedItems.reduce((sum, item) => 
        sum + (item.actualPreparationTime || 0), 0
      )
      this.averageCompletionTime = totalTime / completedItems.length
    }

    await this.calculateEfficiency()
    await this.save()
  }

  public canAcceptNewOrder(): boolean {
    return this.isOperational && !this.isAtCapacity
  }

  public getEstimatedCompletionTime(): DateTime {
    const waitTime = this.estimatedWaitTime
    return DateTime.now().plus({ minutes: waitTime })
  }

  public async getWorkloadSummary(): Promise<{
    pending: number
    preparing: number
    ready: number
    total: number
    capacity: number
    utilizationPercentage: number
  }> {
    await this.load('orderItems')
    
    const pending = this.orderItems.filter(item => item.status === 'pending').length
    const preparing = this.orderItems.filter(item => item.status === 'preparing').length
    const ready = this.orderItems.filter(item => item.status === 'ready').length
    const total = pending + preparing + ready

    return {
      pending,
      preparing,
      ready,
      total,
      capacity: this.maxConcurrentOrders || 0,
      utilizationPercentage: this.workloadPercentage
    }
  }
}
